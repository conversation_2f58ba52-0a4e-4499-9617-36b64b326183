// !$*UTF8*$!
{
	archiveVersion = 1;
	classes = {
	};
	objectVersion = 77;
	objects = {

/* Begin PBXBuildFile section */
		6BDBA95068483846065CD998 /* Pods_MJSceneryShare.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 1E06060ACDBA6557D188F001 /* Pods_MJSceneryShare.framework */; };
		7E4BC4032E1AC21500609E16 /* StoreKit.framework in Frameworks */ = {isa = PBXBuildFile; fileRef = 7E4BC4022E1AC21500609E16 /* StoreKit.framework */; };
		7E87FF852E1820E5000509AD /* GoogleGenerativeAI in Frameworks */ = {isa = PBXBuildFile; productRef = 7E87FF842E1820E5000509AD /* GoogleGenerativeAI */; };
/* End PBXBuildFile section */

/* Begin PBXFileReference section */
		1B2AF2BD739F3C991707DCA2 /* Pods-MJSceneryShare.debug.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MJSceneryShare.debug.xcconfig"; path = "Target Support Files/Pods-MJSceneryShare/Pods-MJSceneryShare.debug.xcconfig"; sourceTree = "<group>"; };
		1E06060ACDBA6557D188F001 /* Pods_MJSceneryShare.framework */ = {isa = PBXFileReference; explicitFileType = wrapper.framework; includeInIndex = 0; path = Pods_MJSceneryShare.framework; sourceTree = BUILT_PRODUCTS_DIR; };
		7E4BC4022E1AC21500609E16 /* StoreKit.framework */ = {isa = PBXFileReference; lastKnownFileType = wrapper.framework; name = StoreKit.framework; path = System/Library/Frameworks/StoreKit.framework; sourceTree = SDKROOT; };
		7EF877E12DDB5C69000F7644 /* MJSceneryShare.app */ = {isa = PBXFileReference; explicitFileType = wrapper.application; includeInIndex = 0; path = MJSceneryShare.app; sourceTree = BUILT_PRODUCTS_DIR; };
		86DBD71E7C7FCE6A884C8B7F /* Pods-MJSceneryShare.release.xcconfig */ = {isa = PBXFileReference; includeInIndex = 1; lastKnownFileType = text.xcconfig; name = "Pods-MJSceneryShare.release.xcconfig"; path = "Target Support Files/Pods-MJSceneryShare/Pods-MJSceneryShare.release.xcconfig"; sourceTree = "<group>"; };
/* End PBXFileReference section */

/* Begin PBXFileSystemSynchronizedBuildFileExceptionSet section */
		7EF877F32DDB5C6A000F7644 /* Exceptions for "MJSceneryShare" folder in "MJSceneryShare" target */ = {
			isa = PBXFileSystemSynchronizedBuildFileExceptionSet;
			membershipExceptions = (
				Info.plist,
			);
			target = 7EF877E02DDB5C69000F7644 /* MJSceneryShare */;
		};
/* End PBXFileSystemSynchronizedBuildFileExceptionSet section */

/* Begin PBXFileSystemSynchronizedRootGroup section */
		7EF877E32DDB5C69000F7644 /* MJSceneryShare */ = {
			isa = PBXFileSystemSynchronizedRootGroup;
			exceptions = (
				7EF877F32DDB5C6A000F7644 /* Exceptions for "MJSceneryShare" folder in "MJSceneryShare" target */,
			);
			path = MJSceneryShare;
			sourceTree = "<group>";
		};
/* End PBXFileSystemSynchronizedRootGroup section */

/* Begin PBXFrameworksBuildPhase section */
		7EF877DE2DDB5C69000F7644 /* Frameworks */ = {
			isa = PBXFrameworksBuildPhase;
			buildActionMask = 2147483647;
			files = (
				7E4BC4032E1AC21500609E16 /* StoreKit.framework in Frameworks */,
				6BDBA95068483846065CD998 /* Pods_MJSceneryShare.framework in Frameworks */,
				7E87FF852E1820E5000509AD /* GoogleGenerativeAI in Frameworks */,
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXFrameworksBuildPhase section */

/* Begin PBXGroup section */
		52BDDC3D629909705546B131 /* Frameworks */ = {
			isa = PBXGroup;
			children = (
				7E4BC4022E1AC21500609E16 /* StoreKit.framework */,
				1E06060ACDBA6557D188F001 /* Pods_MJSceneryShare.framework */,
			);
			name = Frameworks;
			sourceTree = "<group>";
		};
		7EF877D82DDB5C69000F7644 = {
			isa = PBXGroup;
			children = (
				7EF877E32DDB5C69000F7644 /* MJSceneryShare */,
				7EF877E22DDB5C69000F7644 /* Products */,
				BDE812F8D0F981D25215F193 /* Pods */,
				52BDDC3D629909705546B131 /* Frameworks */,
			);
			sourceTree = "<group>";
		};
		7EF877E22DDB5C69000F7644 /* Products */ = {
			isa = PBXGroup;
			children = (
				7EF877E12DDB5C69000F7644 /* MJSceneryShare.app */,
			);
			name = Products;
			sourceTree = "<group>";
		};
		BDE812F8D0F981D25215F193 /* Pods */ = {
			isa = PBXGroup;
			children = (
				1B2AF2BD739F3C991707DCA2 /* Pods-MJSceneryShare.debug.xcconfig */,
				86DBD71E7C7FCE6A884C8B7F /* Pods-MJSceneryShare.release.xcconfig */,
			);
			path = Pods;
			sourceTree = "<group>";
		};
/* End PBXGroup section */

/* Begin PBXNativeTarget section */
		7EF877E02DDB5C69000F7644 /* MJSceneryShare */ = {
			isa = PBXNativeTarget;
			buildConfigurationList = 7EF877F42DDB5C6A000F7644 /* Build configuration list for PBXNativeTarget "MJSceneryShare" */;
			buildPhases = (
				4744D8EC3AE1558FAB513586 /* [CP] Check Pods Manifest.lock */,
				7EF877DD2DDB5C69000F7644 /* Sources */,
				7EF877DE2DDB5C69000F7644 /* Frameworks */,
				7EF877DF2DDB5C69000F7644 /* Resources */,
				4C49DA7116C9283E09E4EB33 /* [CP] Embed Pods Frameworks */,
			);
			buildRules = (
			);
			dependencies = (
			);
			fileSystemSynchronizedGroups = (
				7EF877E32DDB5C69000F7644 /* MJSceneryShare */,
			);
			name = MJSceneryShare;
			productName = MJSceneryShare;
			productReference = 7EF877E12DDB5C69000F7644 /* MJSceneryShare.app */;
			productType = "com.apple.product-type.application";
		};
/* End PBXNativeTarget section */

/* Begin PBXProject section */
		7EF877D92DDB5C69000F7644 /* Project object */ = {
			isa = PBXProject;
			attributes = {
				BuildIndependentTargetsInParallel = 1;
				CLASSPREFIX = MJ;
				LastSwiftUpdateCheck = 1630;
				LastUpgradeCheck = 1630;
				TargetAttributes = {
					7EF877E02DDB5C69000F7644 = {
						CreatedOnToolsVersion = 16.3;
					};
				};
			};
			buildConfigurationList = 7EF877DC2DDB5C69000F7644 /* Build configuration list for PBXProject "MJSceneryShare" */;
			developmentRegion = en;
			hasScannedForEncodings = 0;
			knownRegions = (
				en,
				Base,
			);
			mainGroup = 7EF877D82DDB5C69000F7644;
			minimizedProjectReferenceProxies = 1;
			packageReferences = (
				7E87FF832E1820E5000509AD /* XCRemoteSwiftPackageReference "generative-ai-swift" */,
			);
			preferredProjectObjectVersion = 77;
			productRefGroup = 7EF877E22DDB5C69000F7644 /* Products */;
			projectDirPath = "";
			projectRoot = "";
			targets = (
				7EF877E02DDB5C69000F7644 /* MJSceneryShare */,
			);
		};
/* End PBXProject section */

/* Begin PBXResourcesBuildPhase section */
		7EF877DF2DDB5C69000F7644 /* Resources */ = {
			isa = PBXResourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXResourcesBuildPhase section */

/* Begin PBXShellScriptBuildPhase section */
		4744D8EC3AE1558FAB513586 /* [CP] Check Pods Manifest.lock */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
			);
			inputPaths = (
				"${PODS_PODFILE_DIR_PATH}/Podfile.lock",
				"${PODS_ROOT}/Manifest.lock",
			);
			name = "[CP] Check Pods Manifest.lock";
			outputFileListPaths = (
			);
			outputPaths = (
				"$(DERIVED_FILE_DIR)/Pods-MJSceneryShare-checkManifestLockResult.txt",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "diff \"${PODS_PODFILE_DIR_PATH}/Podfile.lock\" \"${PODS_ROOT}/Manifest.lock\" > /dev/null\nif [ $? != 0 ] ; then\n    # print error to STDERR\n    echo \"error: The sandbox is not in sync with the Podfile.lock. Run 'pod install' or update your CocoaPods installation.\" >&2\n    exit 1\nfi\n# This output is used by Xcode 'outputs' to avoid re-running this script phase.\necho \"SUCCESS\" > \"${SCRIPT_OUTPUT_FILE_0}\"\n";
			showEnvVarsInLog = 0;
		};
		4C49DA7116C9283E09E4EB33 /* [CP] Embed Pods Frameworks */ = {
			isa = PBXShellScriptBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			inputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-MJSceneryShare/Pods-MJSceneryShare-frameworks-${CONFIGURATION}-input-files.xcfilelist",
			);
			name = "[CP] Embed Pods Frameworks";
			outputFileListPaths = (
				"${PODS_ROOT}/Target Support Files/Pods-MJSceneryShare/Pods-MJSceneryShare-frameworks-${CONFIGURATION}-output-files.xcfilelist",
			);
			runOnlyForDeploymentPostprocessing = 0;
			shellPath = /bin/sh;
			shellScript = "\"${PODS_ROOT}/Target Support Files/Pods-MJSceneryShare/Pods-MJSceneryShare-frameworks.sh\"\n";
			showEnvVarsInLog = 0;
		};
/* End PBXShellScriptBuildPhase section */

/* Begin PBXSourcesBuildPhase section */
		7EF877DD2DDB5C69000F7644 /* Sources */ = {
			isa = PBXSourcesBuildPhase;
			buildActionMask = 2147483647;
			files = (
			);
			runOnlyForDeploymentPostprocessing = 0;
		};
/* End PBXSourcesBuildPhase section */

/* Begin XCBuildConfiguration section */
		7EF877F52DDB5C6A000F7644 /* Debug */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 1B2AF2BD739F3C991707DCA2 /* Pods-MJSceneryShare.debug.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = MJSceneryShare/MJSceneryShare.entitlements;
				CODE_SIGN_IDENTITY = "Apple Development";
				"CODE_SIGN_IDENTITY[sdk=iphoneos*]" = "iPhone Developer";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				DEVELOPMENT_TEAM = "";
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 5F4RL56AYJ;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MJSceneryShare/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = BeautySpots;
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "BeautySpots would like to save beautiful photos to your camera roll so you can keep your favorite moments forever ✨";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "BeautySpots needs access to your photo library to save and share beautiful lifestyle moments";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.zewen.askina;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				PROVISIONING_PROFILE_SPECIFIER = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = askina_dev;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Debug;
		};
		7EF877F62DDB5C6A000F7644 /* Release */ = {
			isa = XCBuildConfiguration;
			baseConfigurationReference = 86DBD71E7C7FCE6A884C8B7F /* Pods-MJSceneryShare.release.xcconfig */;
			buildSettings = {
				ASSETCATALOG_COMPILER_APPICON_NAME = AppIcon;
				ASSETCATALOG_COMPILER_GLOBAL_ACCENT_COLOR_NAME = AccentColor;
				CODE_SIGN_ENTITLEMENTS = MJSceneryShare/MJSceneryShare.entitlements;
				CODE_SIGN_IDENTITY = "";
				CODE_SIGN_STYLE = Manual;
				CURRENT_PROJECT_VERSION = 1;
				"DEVELOPMENT_TEAM[sdk=iphoneos*]" = 5F4RL56AYJ;
				ENABLE_USER_SCRIPT_SANDBOXING = NO;
				GENERATE_INFOPLIST_FILE = YES;
				INFOPLIST_FILE = MJSceneryShare/Info.plist;
				INFOPLIST_KEY_CFBundleDisplayName = BeautySpots;
				INFOPLIST_KEY_NSPhotoLibraryAddUsageDescription = "BeautySpots would like to save beautiful photos to your camera roll so you can keep your favorite moments forever ✨";
				INFOPLIST_KEY_NSPhotoLibraryUsageDescription = "BeautySpots needs access to your photo library to save and share beautiful lifestyle moments";
				INFOPLIST_KEY_UIApplicationSupportsIndirectInputEvents = YES;
				INFOPLIST_KEY_UILaunchStoryboardName = LaunchScreen;
				INFOPLIST_KEY_UIMainStoryboardFile = Main;
				INFOPLIST_KEY_UISupportedInterfaceOrientations = UIInterfaceOrientationPortrait;
				INFOPLIST_KEY_UISupportedInterfaceOrientations_iPad = "UIInterfaceOrientationLandscapeLeft UIInterfaceOrientationLandscapeRight UIInterfaceOrientationPortrait UIInterfaceOrientationPortraitUpsideDown";
				IPHONEOS_DEPLOYMENT_TARGET = 15.0;
				LD_RUNPATH_SEARCH_PATHS = (
					"$(inherited)",
					"@executable_path/Frameworks",
				);
				MARKETING_VERSION = 1.0;
				PRODUCT_BUNDLE_IDENTIFIER = com.zewen.askina;
				PRODUCT_NAME = "$(TARGET_NAME)";
				PROVISIONING_PROFILE = "";
				"PROVISIONING_PROFILE_SPECIFIER[sdk=iphoneos*]" = askina_release;
				SWIFT_EMIT_LOC_STRINGS = YES;
				SWIFT_VERSION = 5.0;
				TARGETED_DEVICE_FAMILY = "1,2";
			};
			name = Release;
		};
		7EF877F72DDB5C6A000F7644 /* Debug */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = dwarf;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_TESTABILITY = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_DYNAMIC_NO_PIC = NO;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_OPTIMIZATION_LEVEL = 0;
				GCC_PREPROCESSOR_DEFINITIONS = (
					"DEBUG=1",
					"$(inherited)",
				);
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = INCLUDE_SOURCE;
				MTL_FAST_MATH = YES;
				ONLY_ACTIVE_ARCH = YES;
				SDKROOT = iphoneos;
				SWIFT_ACTIVE_COMPILATION_CONDITIONS = "DEBUG $(inherited)";
				SWIFT_OPTIMIZATION_LEVEL = "-Onone";
			};
			name = Debug;
		};
		7EF877F82DDB5C6A000F7644 /* Release */ = {
			isa = XCBuildConfiguration;
			buildSettings = {
				ALWAYS_SEARCH_USER_PATHS = NO;
				ASSETCATALOG_COMPILER_GENERATE_SWIFT_ASSET_SYMBOL_EXTENSIONS = YES;
				CLANG_ANALYZER_NONNULL = YES;
				CLANG_ANALYZER_NUMBER_OBJECT_CONVERSION = YES_AGGRESSIVE;
				CLANG_CXX_LANGUAGE_STANDARD = "gnu++20";
				CLANG_ENABLE_MODULES = YES;
				CLANG_ENABLE_OBJC_ARC = YES;
				CLANG_ENABLE_OBJC_WEAK = YES;
				CLANG_WARN_BLOCK_CAPTURE_AUTORELEASING = YES;
				CLANG_WARN_BOOL_CONVERSION = YES;
				CLANG_WARN_COMMA = YES;
				CLANG_WARN_CONSTANT_CONVERSION = YES;
				CLANG_WARN_DEPRECATED_OBJC_IMPLEMENTATIONS = YES;
				CLANG_WARN_DIRECT_OBJC_ISA_USAGE = YES_ERROR;
				CLANG_WARN_DOCUMENTATION_COMMENTS = YES;
				CLANG_WARN_EMPTY_BODY = YES;
				CLANG_WARN_ENUM_CONVERSION = YES;
				CLANG_WARN_INFINITE_RECURSION = YES;
				CLANG_WARN_INT_CONVERSION = YES;
				CLANG_WARN_NON_LITERAL_NULL_CONVERSION = YES;
				CLANG_WARN_OBJC_IMPLICIT_RETAIN_SELF = YES;
				CLANG_WARN_OBJC_LITERAL_CONVERSION = YES;
				CLANG_WARN_OBJC_ROOT_CLASS = YES_ERROR;
				CLANG_WARN_QUOTED_INCLUDE_IN_FRAMEWORK_HEADER = YES;
				CLANG_WARN_RANGE_LOOP_ANALYSIS = YES;
				CLANG_WARN_STRICT_PROTOTYPES = YES;
				CLANG_WARN_SUSPICIOUS_MOVE = YES;
				CLANG_WARN_UNGUARDED_AVAILABILITY = YES_AGGRESSIVE;
				CLANG_WARN_UNREACHABLE_CODE = YES;
				CLANG_WARN__DUPLICATE_METHOD_MATCH = YES;
				COPY_PHASE_STRIP = NO;
				DEBUG_INFORMATION_FORMAT = "dwarf-with-dsym";
				ENABLE_NS_ASSERTIONS = NO;
				ENABLE_STRICT_OBJC_MSGSEND = YES;
				ENABLE_USER_SCRIPT_SANDBOXING = YES;
				GCC_C_LANGUAGE_STANDARD = gnu17;
				GCC_NO_COMMON_BLOCKS = YES;
				GCC_WARN_64_TO_32_BIT_CONVERSION = YES;
				GCC_WARN_ABOUT_RETURN_TYPE = YES_ERROR;
				GCC_WARN_UNDECLARED_SELECTOR = YES;
				GCC_WARN_UNINITIALIZED_AUTOS = YES_AGGRESSIVE;
				GCC_WARN_UNUSED_FUNCTION = YES;
				GCC_WARN_UNUSED_VARIABLE = YES;
				IPHONEOS_DEPLOYMENT_TARGET = 18.4;
				LOCALIZATION_PREFERS_STRING_CATALOGS = YES;
				MTL_ENABLE_DEBUG_INFO = NO;
				MTL_FAST_MATH = YES;
				SDKROOT = iphoneos;
				SWIFT_COMPILATION_MODE = wholemodule;
				VALIDATE_PRODUCT = YES;
			};
			name = Release;
		};
/* End XCBuildConfiguration section */

/* Begin XCConfigurationList section */
		7EF877DC2DDB5C69000F7644 /* Build configuration list for PBXProject "MJSceneryShare" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7EF877F72DDB5C6A000F7644 /* Debug */,
				7EF877F82DDB5C6A000F7644 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
		7EF877F42DDB5C6A000F7644 /* Build configuration list for PBXNativeTarget "MJSceneryShare" */ = {
			isa = XCConfigurationList;
			buildConfigurations = (
				7EF877F52DDB5C6A000F7644 /* Debug */,
				7EF877F62DDB5C6A000F7644 /* Release */,
			);
			defaultConfigurationIsVisible = 0;
			defaultConfigurationName = Release;
		};
/* End XCConfigurationList section */

/* Begin XCRemoteSwiftPackageReference section */
		7E87FF832E1820E5000509AD /* XCRemoteSwiftPackageReference "generative-ai-swift" */ = {
			isa = XCRemoteSwiftPackageReference;
			repositoryURL = "https://github.com/google/generative-ai-swift";
			requirement = {
				kind = upToNextMajorVersion;
				minimumVersion = 0.5.6;
			};
		};
/* End XCRemoteSwiftPackageReference section */

/* Begin XCSwiftPackageProductDependency section */
		7E87FF842E1820E5000509AD /* GoogleGenerativeAI */ = {
			isa = XCSwiftPackageProductDependency;
			package = 7E87FF832E1820E5000509AD /* XCRemoteSwiftPackageReference "generative-ai-swift" */;
			productName = GoogleGenerativeAI;
		};
/* End XCSwiftPackageProductDependency section */
	};
	rootObject = 7EF877D92DDB5C69000F7644 /* Project object */;
}
