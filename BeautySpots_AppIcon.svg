<?xml version="1.0" encoding="UTF-8"?>
<svg width="1024" height="1024" viewBox="0 0 1024 1024" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Main gradient background -->
    <radialGradient id="bgGradient" cx="50%" cy="30%" r="80%">
      <stop offset="0%" style="stop-color:#FFE4E6;stop-opacity:1" />
      <stop offset="40%" style="stop-color:#FF69B4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#E91E63;stop-opacity:1" />
    </radialGradient>
    
    <!-- Mountain gradient -->
    <linearGradient id="mountainGrad" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#F8BBD9;stop-opacity:0.7" />
    </linearGradient>
    
    <!-- Sun gradient -->
    <radialGradient id="sunGrad" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFFFFF;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFE4E6;stop-opacity:0.8" />
    </radialGradient>
  </defs>
  
  <!-- Background with rounded corners for iOS -->
  <rect width="1024" height="1024" rx="180" ry="180" fill="url(#bgGradient)"/>
  
  <!-- Mountain silhouette -->
  <g transform="translate(512, 512)">
    <!-- Back mountains -->
    <path d="M -400 100 L -250 -150 L -100 -50 L 50 -200 L 200 -100 L 350 -50 L 400 100 Z" 
          fill="url(#mountainGrad)" opacity="0.4"/>
    
    <!-- Front mountains -->
    <path d="M -450 150 L -300 -100 L -150 0 L 0 -180 L 150 -80 L 300 -20 L 450 150 Z" 
          fill="url(#mountainGrad)" opacity="0.6"/>
    
    <!-- Sun -->
    <circle cx="200" cy="-120" r="80" fill="url(#sunGrad)" opacity="0.9"/>
    
    <!-- Stylized "B" logo -->
    <g transform="scale(3)">
      <path d="M -40 -60 L -40 60 L -15 60 L 15 60 Q 35 60 35 35 Q 35 10 15 5 Q 30 5 30 -20 Q 30 -60 5 -60 Z
               M -15 -35 L 5 -35 Q 15 -35 15 -25 Q 15 -15 5 -15 L -15 -15 Z
               M -15 20 L 15 20 Q 25 20 25 30 Q 25 40 15 40 L -15 40 Z" 
            fill="#FFFFFF" opacity="0.95"/>
    </g>
    
    <!-- Decorative sparkles -->
    <g fill="#FFFFFF" opacity="0.8">
      <!-- Large sparkle -->
      <path d="M -200 -200 L -190 -170 L -160 -160 L -190 -150 L -200 -120 L -210 -150 L -240 -160 L -210 -170 Z"/>
      <!-- Medium sparkles -->
      <circle cx="250" cy="-250" r="8"/>
      <circle cx="-300" cy="-80" r="6"/>
      <circle cx="320" cy="50" r="5"/>
      <!-- Small sparkles -->
      <circle cx="-350" cy="-200" r="4"/>
      <circle cx="380" cy="-150" r="3"/>
    </g>
  </g>
</svg>
