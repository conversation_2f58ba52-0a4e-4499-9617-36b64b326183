# 直接AI购买系统实现

## 概述
根据用户要求，实现了直接AI购买系统，不再使用金币计算AI次数，而是在购买包中直接定义AI分析次数，购买后直接增加相应的次数。

## 核心变更

### 1. 恢复初始金币设置
```swift
// MJSceneryShare/Models/CoinSystem.swift
static let newUserStartingCoins = 100  // 恢复为100
```

### 2. 购买包增加AI次数属性
```swift
// MJSceneryShare/Models/CoinPurchasePackage.swift
struct CoinPurchasePackage {
    let aiAnalysisCount: Int  // 新增：直接定义AI分析次数
    // ... 其他属性
}
```

### 3. 用户模型增加购买AI次数属性
```swift
// MJSceneryShare/Models/User.swift
struct User {
    var purchasedProcessingCount: Int  // 新增：购买获得的AI分析次数
    // ... 其他属性
}
```

### 4. 更新计算逻辑
```swift
// 不再基于金币计算，直接使用购买的AI次数
var totalAvailableProcessing: Int {
    if isGuestUser {
        return freeProcessingCount
    }
    return freeProcessingCount + purchasedProcessingCount
}

var paidProcessingCount: Int {
    if isGuestUser {
        return 0
    }
    return purchasedProcessingCount
}
```

### 5. 更新购买处理逻辑
```swift
// MJSceneryShare/Services/IAPManager.swift
private func addCoinsAndAIAnalysisToUser(_ package: CoinPurchasePackage) {
    // 同时增加金币和AI分析次数
    currentUser.addPurchasedProcessing(package.aiAnalysisCount)
    // ... 金币处理逻辑
}
```

## 购买包配置

| 包名 | 金币 | AI分析次数 | 价格 |
|------|------|------------|------|
| Starter Pack | 100 | 5 | $0.99 |
| Popular Choice | 250 | 15 | $1.99 |
| Best Value | 500 | 32 | $3.99 |
| Premium Pack | 1000 | 70 | $6.99 |
| Ultimate Pack | 2000 | 150 | $11.99 |
| Mega Pack | 5000 | 400 | $24.99 |

## 用户体验

### 新用户（Apple登录）
- **初始状态**：100金币 + 3次免费AI分析
- **界面显示**：`🆓 3 free AI analyses remaining`

### 购买Starter Pack后
- **获得**：+100金币 + 5次AI分析
- **总计**：200金币 + 8次AI分析（3免费 + 5购买）
- **界面显示**：`🤖 8 AI analyses available (3 free + 5 paid)`

### 游客用户
- **始终**：0金币 + 3次免费AI分析
- **界面显示**：`🆓 3 free AI analyses remaining (Guest mode)`

## 技术实现细节

### 1. 处理优先级
1. 优先使用免费次数（3次）
2. 然后使用购买的次数
3. 游客用户无法使用购买的次数

### 2. 数据持久化
- 用户的`purchasedProcessingCount`会被保存到UserDefaults
- Apple登录用户的数据会持久化保存

### 3. 界面更新
- 购买成功后自动更新UI显示
- 通过NotificationCenter通知界面刷新

## 测试验证

✅ **Apple登录用户（新用户）**：100金币 + 3次免费AI分析  
✅ **购买Starter Pack后**：200金币 + 8次AI分析（3免费 + 5购买）  
✅ **购买Popular Pack后**：450金币 + 23次AI分析（3免费 + 20购买）  
✅ **游客用户**：0金币 + 3次免费AI分析  

## 优势

1. **直观明确**：购买包直接显示获得的AI分析次数
2. **灵活定价**：可以独立调整金币和AI次数的比例
3. **用户友好**：用户清楚知道购买后能获得多少次AI分析
4. **系统分离**：金币可用于其他功能，AI分析次数独立管理

## 界面显示修复

### 问题
用户反馈：个人中心右上角的AI次数展示错误，金币页面展示的是"3 + 5"，个人中心页面还是展示的"3"。

### 原因
多个页面的AI次数显示逻辑不一致：
- `PurchaseViewController.swift` - 已正确显示总AI次数
- `ProfileViewController.swift` - 只显示免费AI次数 ❌
- `ShareViewController.swift` - 只显示免费AI次数 ❌

### 修复
更新了所有页面的显示逻辑，统一显示总AI次数：

```swift
// 修复前（只显示免费次数）
let freeText = user.freeProcessingCount > 0 ? " (+\(user.freeProcessingCount) free)" : ""

// 修复后（显示总AI次数）
let totalAI = user.totalAvailableProcessing
let aiText = totalAI > 0 ? " (🤖\(totalAI))" : ""
```

### 修复的页面
1. ✅ **ProfileViewController.swift** - 个人中心右上角显示
2. ✅ **ShareViewController.swift** - 主页右上角显示

## 总结

现在系统完全按照您的要求工作：
- **登录用户都会获得100初始金币和3次免费AI分析**
- **不使用金币计算AI次数**
- **购买包直接定义AI分析次数**
- **购买后直接增加相应的AI使用次数**
- **所有页面统一显示总AI次数（免费+购买）**

### 显示效果
- **个人中心右上角**：`💰 310 (🤖13)`
- **主页右上角**：`💰 310 (🤖13)`
- **购买页面**：`🤖 13 AI analyses available (3 free + 10 paid)`

这样的设计更加直观和一致，用户在任何页面都能看到正确的总AI分析次数。
