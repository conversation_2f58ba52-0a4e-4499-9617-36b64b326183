<?xml version="1.0" encoding="UTF-8"?>
<svg width="200" height="200" viewBox="0 0 200 200" xmlns="http://www.w3.org/2000/svg">
  <defs>
    <!-- Gradient for the main background circle -->
    <radialGradient id="mainGradient" cx="50%" cy="30%" r="70%">
      <stop offset="0%" style="stop-color:#FFB6C1;stop-opacity:1" />
      <stop offset="50%" style="stop-color:#FF69B4;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FF1493;stop-opacity:1" />
    </radialGradient>
    
    <!-- Gradient for mountains -->
    <linearGradient id="mountainGradient" x1="0%" y1="0%" x2="0%" y2="100%">
      <stop offset="0%" style="stop-color:#E6E6FA;stop-opacity:0.9" />
      <stop offset="100%" style="stop-color:#DDA0DD;stop-opacity:0.7" />
    </linearGradient>
    
    <!-- Gradient for the sun -->
    <radialGradient id="sunGradient" cx="50%" cy="50%" r="50%">
      <stop offset="0%" style="stop-color:#FFE4E1;stop-opacity:1" />
      <stop offset="100%" style="stop-color:#FFC0CB;stop-opacity:0.8" />
    </radialGradient>
    
    <!-- Shadow filter -->
    <filter id="shadow" x="-20%" y="-20%" width="140%" height="140%">
      <feDropShadow dx="2" dy="4" stdDeviation="3" flood-color="#000000" flood-opacity="0.2"/>
    </filter>
  </defs>
  
  <!-- Main background circle -->
  <circle cx="100" cy="100" r="95" fill="url(#mainGradient)" filter="url(#shadow)"/>
  
  <!-- Inner decorative circle -->
  <circle cx="100" cy="100" r="85" fill="none" stroke="#FFFFFF" stroke-width="1" opacity="0.3"/>
  
  <!-- Mountain landscape -->
  <!-- Back mountain -->
  <path d="M 30 120 L 60 80 L 90 100 L 120 70 L 150 90 L 170 120 Z" 
        fill="url(#mountainGradient)" opacity="0.6"/>
  
  <!-- Front mountain -->
  <path d="M 20 130 L 50 90 L 80 110 L 110 75 L 140 95 L 180 130 Z" 
        fill="url(#mountainGradient)" opacity="0.8"/>
  
  <!-- Sun/Moon -->
  <circle cx="140" cy="60" r="18" fill="url(#sunGradient)" opacity="0.9"/>
  
  <!-- Decorative stars/sparkles -->
  <g fill="#FFFFFF" opacity="0.8">
    <!-- Large sparkle -->
    <path d="M 60 50 L 62 56 L 68 58 L 62 60 L 60 66 L 58 60 L 52 58 L 58 56 Z"/>
    <!-- Medium sparkle -->
    <path d="M 160 100 L 161.5 104 L 165.5 105.5 L 161.5 107 L 160 111 L 158.5 107 L 154.5 105.5 L 158.5 104 Z"/>
    <!-- Small sparkles -->
    <circle cx="45" cy="70" r="1.5"/>
    <circle cx="155" cy="45" r="1"/>
    <circle cx="170" cy="80" r="1.5"/>
    <circle cx="35" cy="90" r="1"/>
  </g>
  
  <!-- Stylized "B" for BeautySpots -->
  <g transform="translate(100, 100)">
    <!-- Letter B design with curves -->
    <path d="M -25 -35 L -25 35 L -10 35 L 5 35 Q 20 35 20 20 Q 20 5 5 0 Q 15 0 15 -15 Q 15 -35 0 -35 Z
             M -10 -20 L 0 -20 Q 5 -20 5 -15 Q 5 -10 0 -10 L -10 -10 Z
             M -10 10 L 5 10 Q 10 10 10 15 Q 10 20 5 20 L -10 20 Z" 
          fill="#FFFFFF" opacity="0.9" stroke="#FF69B4" stroke-width="1"/>
  </g>
  
  <!-- Decorative dots around the logo -->
  <g fill="#FFFFFF" opacity="0.6">
    <circle cx="100" cy="25" r="2"/>
    <circle cx="175" cy="100" r="2"/>
    <circle cx="100" cy="175" r="2"/>
    <circle cx="25" cy="100" r="2"/>
  </g>
  
  <!-- Subtle outer glow -->
  <circle cx="100" cy="100" r="95" fill="none" stroke="#FF69B4" stroke-width="2" opacity="0.3"/>
</svg>
