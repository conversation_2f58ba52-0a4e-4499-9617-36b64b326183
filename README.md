# BeautySpots - Lifestyle & Travel Sharing App

## Overview
BeautySpots is a beautiful iOS mobile app designed for women to discover and share stunning lifestyle moments. The app features elegant design with Discover and Profile tabs for a social experience, using local JSON files to simulate network data for demonstration.

## Tech Stack
- Swift
- MVVM Architecture
- Modern iOS Design Patterns
- SnapKit for Auto Layout
- StoreKit for In-App Purchases

## Project Structure
- **Models**: Data models for posts and users
- **Views**: Beautiful UI components and view controllers
- **ViewModels**: Business logic and data management
- **Resources**: Assets and data files

## Core Features

### 🌟 Discover Feed
- **Beautiful Content Display**: Showcases stunning lifestyle and travel photos from the community
- **Rich Content Data**: Each post includes image, title, description, likes, comments, shares, saves, author info, avatar, username, location, and timestamp
- **Interactive Engagement**: 
  - Tap photos to view full-screen details and save to camera roll
  - Like posts with animated heart interactions
  - Save posts to personal collection
  - Share content with friends via native iOS sharing
  - View and add comments (coming soon)
- **Real-time Updates**: All interactions update counts immediately and sync with local JSON storage
- **Elegant Design**: Clean, Instagram-inspired layout optimized for female users

### 👤 Profile & Settings
- **Personal Profile**: Beautiful profile display with avatar, username, and stats
- **Account Management**: 
  - View and edit personal information
  - Access app settings and preferences
  - Share the app with friends
- **App Information**:
  - About section with app details, version, and contact information
  - Feedback system for user suggestions
  - Privacy policy and terms of service (displayed via SFSafariViewController)
- **Account Actions**: Secure logout with confirmation dialog

### 🔐 Authentication
- **Guest Access**: Simple guest login to explore the app
- **Seamless Experience**: Quick access to main features after login

### 💎 Premium Features
- **Coin System**: In-app currency for premium features
- **Multiple Purchase Options**: 
  - 100 coins ($0.99)
  - 200 coins ($1.99) 
  - 500 coins ($4.99)
  - 1000 coins ($9.99)
- **Apple IAP Integration**: Secure purchases using Apple's In-App Purchase system
- **Real-time Balance**: Coin balance displayed prominently in the navigation bar
- **Purchase Verification**: Proper receipt validation and secure transaction handling

## Design Philosophy
- **Female-Oriented**: Elegant pink color scheme and feminine design elements
- **Social-First**: Focus on community interaction and sharing
- **Modern iOS**: Uses latest iOS design patterns and SF Symbols
- **Accessibility**: Supports dynamic type and accessibility features

## Installation
1. Clone the repository
2. Run `pod install` to install dependencies
3. Open `MJSceneryShare.xcworkspace` in Xcode
4. Build and run the project

## Dependencies
- SnapKit: Auto Layout made easy
- StoreKit: In-App Purchases

## Contact
For questions or support, please contact: <EMAIL>

---

*BeautySpots - Where beautiful moments are shared ✨*
