# IAP Implementation Checklist

## Pre-Launch Setup

### App Store Connect Configuration
- [ ] Create in-app purchase products:
  - [ ] `com.askina.coins.100` - Small Coin Pack - $0.99 - 300 coins
  - [ ] `com.askina.coins.500` - Medium Coin Pack - $3.99 - 1500 coins  
  - [ ] `com.askina.coins.1000` - Large Coin Pack - $6.99 - 3000 coins
  - [ ] `com.askina.coins.2500` - Mega Coin Pack - $12.99 - 8000 coins
- [ ] Set all products as "Consumable" type
- [ ] Add product descriptions and screenshots
- [ ] Submit products for review
- [ ] Wait for "Ready to Submit" status

### Xcode Project
- [ ] Verify StoreKit framework is linked (already done)
- [ ] Check app entitlements include In-App Purchase capability
- [ ] Ensure bundle identifier matches App Store Connect
- [ ] Test with sandbox environment

## Testing Phase

### Development Testing
- [ ] Build and run on device
- [ ] Open Purchase page (💰 Get Coins)
- [ ] Tap gear icon (debug menu) in navigation bar
- [ ] Test "Toggle Test Mode" - should show "Test mode is now ON"
- [ ] Try purchasing a package - should show simulation dialog
- [ ] Verify coins are added to balance
- [ ] Test "Clear Coin Balance" and "Add Test Coins" functions

### Sandbox Testing
- [ ] Create sandbox test account in App Store Connect
- [ ] Sign out of App Store on test device
- [ ] Build app in Release configuration
- [ ] Sign in with sandbox account when prompted
- [ ] Test real purchase flow (will use sandbox, no real money)
- [ ] Verify products load with real prices
- [ ] Complete test purchase and verify coins are added

### Authentication Flow Testing
- [ ] Test purchase when not logged in:
  - [ ] Should show "Login Required" alert with 3 options
  - [ ] Test "Continue as Guest" - should auto-login and proceed
  - [ ] Test "Sign in with Apple" - should navigate to login
  - [ ] Test "Cancel" - should dismiss alert
- [ ] Test purchase when already logged in - should proceed directly

## Production Deployment

### Final Checks
- [ ] Disable test mode in production builds
- [ ] Verify all debug logging is properly gated with #if DEBUG
- [ ] Test on multiple devices and iOS versions
- [ ] Verify purchase flow works end-to-end
- [ ] Check coin balance updates correctly across app

### App Store Submission
- [ ] Include in-app purchase information in app review notes
- [ ] Provide test account credentials if needed
- [ ] Ensure all IAP products are "Ready to Submit"
- [ ] Submit app for review

## Post-Launch Monitoring

### Analytics to Track
- [ ] Purchase success/failure rates
- [ ] Most popular coin packages
- [ ] Authentication flow completion rates
- [ ] Error rates and types

### User Support
- [ ] Monitor for purchase-related support requests
- [ ] Have process for handling failed purchases
- [ ] Document common troubleshooting steps

## Troubleshooting Guide

### Common Issues
1. **Products not loading**
   - Check internet connection
   - Verify product IDs match App Store Connect
   - Check if products are approved

2. **Purchases failing**
   - Verify user can make purchases (not restricted)
   - Check sandbox account setup
   - Review error messages in debug logs

3. **Coins not added**
   - Check transaction completion
   - Verify CoinTransactionManager integration
   - Review debug logs for errors

### Debug Tools
- Use debug menu (gear icon) in development builds
- Check "Show Product Info" for loaded products
- Review Xcode console for "IAP Debug:" messages
- Use "Request Products" to manually refresh product list

## Support Information

### For Users
- Purchases are processed through Apple's App Store
- Coins are added immediately after successful purchase
- Contact support if coins don't appear within a few minutes
- Purchases are tied to Apple ID, not app account

### For Developers
- All IAP logic is in `IAPManager.swift`
- Purchase flow starts in `PurchaseViewController.swift`
- Coin management handled by `CoinTransactionManager.swift`
- Debug features available in development builds only
