//
//  PostStorageManager.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-04.
//

import Foundation
import UIKit

class PostStorageManager {
    static let shared = PostStorageManager()
    
    // MARK: - Properties
    private let userDefaults = UserDefaults.standard
    private let fileManager = FileManager.default
    private let postsKey = "SavedPosts"
    private let statisticsKey = "PostStatistics"
    
    private var documentsDirectory: URL {
        return fileManager.urls(for: .documentDirectory, in: .userDomainMask).first!
    }
    
    private var imagesDirectory: URL {
        let url = documentsDirectory.appendingPathComponent("PostImages")
        createDirectoryIfNeeded(url)
        return url
    }
    
    private var postsDirectory: URL {
        let url = documentsDirectory.appendingPathComponent("Posts")
        createDirectoryIfNeeded(url)
        return url
    }
    
    private init() {
        createDirectoriesIfNeeded()
    }
    
    // MARK: - Directory Management
    private func createDirectoriesIfNeeded() {
        createDirectoryIfNeeded(imagesDirectory)
        createDirectoryIfNeeded(postsDirectory)
    }
    
    private func createDirectoryIfNeeded(_ url: URL) {
        if !fileManager.fileExists(atPath: url.path) {
            try? fileManager.createDirectory(at: url, withIntermediateDirectories: true, attributes: nil)
        }
    }
    
    // MARK: - Post Management
    func savePost(_ postData: PostData) {
        var posts = loadAllPosts()
        posts.append(postData)
        savePosts(posts)
        updateStatistics(posts)
        
        // Post notification for UI updates
        NotificationCenter.default.post(name: .postsDidUpdate, object: nil)
    }
    
    func loadAllPosts() -> [PostData] {
        guard let data = userDefaults.data(forKey: postsKey) else {
            return []
        }
        
        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            return try decoder.decode([PostData].self, from: data)
        } catch {
            print("Error loading posts: \(error)")
            return []
        }
    }
    
    func deletePost(withId id: String) {
        var posts = loadAllPosts()
        
        // Find and remove the post
        if let index = posts.firstIndex(where: { $0.id == id }) {
            let post = posts[index]
            
            // Delete associated image
            deleteImage(fileName: post.imageFileName)
            
            // Remove from array
            posts.remove(at: index)
            
            // Save updated posts
            savePosts(posts)
            updateStatistics(posts)
            
            // Post notification
            NotificationCenter.default.post(name: .postsDidUpdate, object: nil)
        }
    }
    
    func getPost(withId id: String) -> PostData? {
        return loadAllPosts().first { $0.id == id }
    }
    
    func updatePost(_ postData: PostData) {
        var posts = loadAllPosts()
        
        if let index = posts.firstIndex(where: { $0.id == postData.id }) {
            posts[index] = postData
            savePosts(posts)
            updateStatistics(posts)
            
            NotificationCenter.default.post(name: .postsDidUpdate, object: nil)
        }
    }
    
    private func savePosts(_ posts: [PostData]) {
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(posts)
            userDefaults.set(data, forKey: postsKey)
        } catch {
            print("Error saving posts: \(error)")
        }
    }
    
    // MARK: - Image Management
    func saveImage(_ image: UIImage, fileName: String) {
        let imageURL = imagesDirectory.appendingPathComponent(fileName)
        
        if let imageData = image.jpegData(compressionQuality: 0.8) {
            try? imageData.write(to: imageURL)
        }
    }
    
    func loadImage(fileName: String) -> UIImage? {
        let imageURL = imagesDirectory.appendingPathComponent(fileName)
        return UIImage(contentsOfFile: imageURL.path)
    }
    
    func deleteImage(fileName: String) {
        let imageURL = imagesDirectory.appendingPathComponent(fileName)
        try? fileManager.removeItem(at: imageURL)
    }
    
    // MARK: - Statistics Management
    func loadStatistics() -> PostStatistics {
        guard let data = userDefaults.data(forKey: statisticsKey) else {
            return PostStatistics()
        }
        
        do {
            return try JSONDecoder().decode(PostStatistics.self, from: data)
        } catch {
            print("Error loading statistics: \(error)")
            return PostStatistics()
        }
    }
    
    private func updateStatistics(_ posts: [PostData]) {
        let statistics = posts.generateStatistics()
        saveStatistics(statistics)
    }
    
    private func saveStatistics(_ statistics: PostStatistics) {
        do {
            let data = try JSONEncoder().encode(statistics)
            userDefaults.set(data, forKey: statisticsKey)
        } catch {
            print("Error saving statistics: \(error)")
        }
    }
    
    // MARK: - Search and Filter
    func searchPosts(query: String) -> [PostSearchResult] {
        let posts = loadAllPosts()
        return posts.search(text: query)
    }
    
    func filterPosts(by filter: PostFilterOption, sortBy sort: PostSortOption) -> [PostData] {
        let posts = loadAllPosts()
        return posts.filtered(by: filter).sorted(by: sort)
    }
    
    func getRecentPosts(limit: Int = 10) -> [PostData] {
        let posts = loadAllPosts()
        return Array(posts.sorted(by: .newest).prefix(limit))
    }
    
    func getPostsByContentType(_ contentType: String) -> [PostData] {
        let posts = loadAllPosts()
        return posts.filter { $0.contentType == contentType }
    }
    
    // MARK: - Data Export/Import
    func exportAllData() -> PostExportData {
        let posts = loadAllPosts()
        let statistics = loadStatistics()
        return PostExportData(posts: posts, statistics: statistics)
    }
    
    func exportDataAsJSON() -> Data? {
        let exportData = exportAllData()
        
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            encoder.outputFormatting = .prettyPrinted
            return try encoder.encode(exportData)
        } catch {
            print("Error exporting data: \(error)")
            return nil
        }
    }
    
    func clearAllData() {
        // Remove all posts
        userDefaults.removeObject(forKey: postsKey)
        userDefaults.removeObject(forKey: statisticsKey)
        
        // Remove all images
        try? fileManager.removeItem(at: imagesDirectory)
        createDirectoryIfNeeded(imagesDirectory)
        
        // Post notification
        NotificationCenter.default.post(name: .postsDidUpdate, object: nil)
    }
    
    // MARK: - Storage Info
    func getStorageInfo() -> (postCount: Int, imageCount: Int, totalSize: String) {
        let posts = loadAllPosts()
        let postCount = posts.count
        
        // Count images in directory
        let imageFiles = (try? fileManager.contentsOfDirectory(at: imagesDirectory, includingPropertiesForKeys: nil)) ?? []
        let imageCount = imageFiles.count
        
        // Calculate total size
        var totalSize: Int64 = 0
        
        // Size of images
        for imageFile in imageFiles {
            if let attributes = try? fileManager.attributesOfItem(atPath: imageFile.path),
               let fileSize = attributes[.size] as? Int64 {
                totalSize += fileSize
            }
        }
        
        // Size of posts data
        if let postsData = userDefaults.data(forKey: postsKey) {
            totalSize += Int64(postsData.count)
        }
        
        let formatter = ByteCountFormatter()
        formatter.allowedUnits = [.useMB, .useKB]
        formatter.countStyle = .file
        
        return (postCount, imageCount, formatter.string(fromByteCount: totalSize))
    }
    
    // MARK: - Backup and Restore
    func createBackup() -> URL? {
        let backupURL = documentsDirectory.appendingPathComponent("backup_\(Date().timeIntervalSince1970).json")
        
        guard let jsonData = exportDataAsJSON() else {
            return nil
        }
        
        do {
            try jsonData.write(to: backupURL)
            return backupURL
        } catch {
            print("Error creating backup: \(error)")
            return nil
        }
    }
    
    func restoreFromBackup(url: URL) -> Bool {
        do {
            let data = try Data(contentsOf: url)
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            
            let exportData = try decoder.decode(PostExportData.self, from: data)
            
            // Clear existing data
            clearAllData()
            
            // Restore posts (note: images won't be restored from JSON backup)
            savePosts(exportData.posts)
            saveStatistics(exportData.statistics)
            
            return true
        } catch {
            print("Error restoring backup: \(error)")
            return false
        }
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let postsDidUpdate = Notification.Name("postsDidUpdate")
}
