import Foundation

// MARK: - Chat Storage Manager
class ChatStorageManager {
    static let shared = ChatStorageManager()
    
    private let userDefaults = UserDefaults.standard
    private let chatMessagesKey = "chatMessages"
    private let conversationsKey = "conversations"
    
    private init() {}
    
    // MARK: - Message Management
    
    /// Save a message to local storage
    func saveMessage(_ message: ChatMessage) {
        var allMessages = loadAllMessages()
        
        // Remove existing message with same ID if exists (for updates)
        allMessages.removeAll { $0.id == message.id }
        
        // Add the new/updated message
        allMessages.append(message)
        
        // Sort messages by timestamp
        allMessages.sort { $0.timestamp < $1.timestamp }
        
        saveAllMessages(allMessages)
        
        // Update conversation info
        updateConversationInfo(for: message)
        
        print("💬 [ChatStorage] Saved message: \(message.id) for conversation: \(message.conversationId)")
    }
    
    /// Load messages for a specific conversation
    func loadMessages(for conversationId: String) -> [ChatMessage] {
        let allMessages = loadAllMessages()
        let conversationMessages = allMessages.filter { $0.conversationId == conversationId }
        
        print("💬 [ChatStorage] Loaded \(conversationMessages.count) messages for conversation: \(conversationId)")
        return conversationMessages.sorted { $0.timestamp < $1.timestamp }
    }
    
    /// Delete all messages for a conversation
    func deleteMessages(for conversationId: String) {
        var allMessages = loadAllMessages()
        allMessages.removeAll { $0.conversationId == conversationId }
        saveAllMessages(allMessages)
        
        // Also remove conversation info
        deleteConversationInfo(for: conversationId)
        
        print("💬 [ChatStorage] Deleted all messages for conversation: \(conversationId)")
    }
    
    /// Check if conversation has any messages
    func hasMessages(for conversationId: String) -> Bool {
        let messages = loadMessages(for: conversationId)
        return !messages.isEmpty
    }
    
    /// Get the last message for a conversation
    func getLastMessage(for conversationId: String) -> ChatMessage? {
        let messages = loadMessages(for: conversationId)
        return messages.last
    }
    
    // MARK: - Conversation Management
    
    /// Save conversation info
    func saveConversation(_ conversation: Conversation) {
        var allConversations = loadAllConversations()
        
        // Remove existing conversation with same ID if exists
        allConversations.removeAll { $0.id == conversation.id }
        
        // Add the new/updated conversation
        allConversations.append(conversation)
        
        saveAllConversations(allConversations)
        
        print("💬 [ChatStorage] Saved conversation: \(conversation.id)")
    }
    
    /// Load all conversations
    func loadAllConversations() -> [Conversation] {
        guard let data = userDefaults.data(forKey: conversationsKey) else {
            return []
        }
        
        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            return try decoder.decode([Conversation].self, from: data)
        } catch {
            print("💬 [ChatStorage] Error loading conversations: \(error)")
            return []
        }
    }
    
    /// Get conversation by ID
    func getConversation(by id: String) -> Conversation? {
        let allConversations = loadAllConversations()
        return allConversations.first { $0.id == id }
    }
    
    /// Delete conversation info
    func deleteConversationInfo(for conversationId: String) {
        var allConversations = loadAllConversations()
        allConversations.removeAll { $0.id == conversationId }
        saveAllConversations(allConversations)
        
        print("💬 [ChatStorage] Deleted conversation info: \(conversationId)")
    }
    
    // MARK: - Private Helper Methods
    
    private func loadAllMessages() -> [ChatMessage] {
        guard let data = userDefaults.data(forKey: chatMessagesKey) else {
            return []
        }
        
        do {
            let decoder = JSONDecoder()
            decoder.dateDecodingStrategy = .iso8601
            return try decoder.decode([ChatMessage].self, from: data)
        } catch {
            print("💬 [ChatStorage] Error loading messages: \(error)")
            return []
        }
    }
    
    private func saveAllMessages(_ messages: [ChatMessage]) {
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(messages)
            userDefaults.set(data, forKey: chatMessagesKey)
        } catch {
            print("💬 [ChatStorage] Error saving messages: \(error)")
        }
    }
    
    private func saveAllConversations(_ conversations: [Conversation]) {
        do {
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            let data = try encoder.encode(conversations)
            userDefaults.set(data, forKey: conversationsKey)
        } catch {
            print("💬 [ChatStorage] Error saving conversations: \(error)")
        }
    }
    
    private func updateConversationInfo(for message: ChatMessage) {
        var allConversations = loadAllConversations()
        
        // Find existing conversation or create new one
        if let index = allConversations.firstIndex(where: { $0.id == message.conversationId }) {
            // Update existing conversation
            var conversation = allConversations[index]
            
            // Create updated conversation with new last message
            let updatedConversation = Conversation(
                id: conversation.id,
                participants: conversation.participants,
                lastMessage: message,
                lastActivity: message.timestamp,
                unreadCount: message.isSentByCurrentUser ? conversation.unreadCount : conversation.unreadCount + 1,
                createdDate: conversation.createdDate,
                isArchived: conversation.isArchived,
                isMuted: conversation.isMuted
            )
            
            allConversations[index] = updatedConversation
        } else {
            // Create new conversation
            let newConversation = Conversation(
                id: message.conversationId,
                participants: [message.senderId, message.receiverId],
                lastMessage: message,
                lastActivity: message.timestamp,
                unreadCount: message.isSentByCurrentUser ? 0 : 1,
                createdDate: message.timestamp,
                isArchived: false,
                isMuted: false
            )
            
            allConversations.append(newConversation)
        }
        
        saveAllConversations(allConversations)
    }
    
    // MARK: - Utility Methods
    
    /// Clear all chat data (for logout/reset)
    func clearAllChatData() {
        userDefaults.removeObject(forKey: chatMessagesKey)
        userDefaults.removeObject(forKey: conversationsKey)
        
        print("💬 [ChatStorage] Cleared all chat data")
    }
    
    /// Get storage statistics
    func getStorageStatistics() -> (messageCount: Int, conversationCount: Int) {
        let messageCount = loadAllMessages().count
        let conversationCount = loadAllConversations().count
        
        return (messageCount: messageCount, conversationCount: conversationCount)
    }
}
