//
//  ImageTaggingService.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-04.
//

import Foundation
import UIKit

// MARK: - Image Tag
struct ImageTag {
    let id: String
    let name: String
    let category: ImageContentType
    let popularity: Int
    let isUserGenerated: Bool
    
    init(name: String, category: ImageContentType, popularity: Int = 0, isUserGenerated: Bool = false) {
        self.id = UUID().uuidString
        self.name = name
        self.category = category
        self.popularity = popularity
        self.isUserGenerated = isUserGenerated
    }
}

// MARK: - Tagged Image
struct TaggedImage {
    let imageId: String
    let tags: [ImageTag]
    let contentType: ImageContentType
    let confidence: Double
    let timestamp: Date
    let userId: String?
    
    var displayTags: [String] {
        return tags.map { $0.name }
    }
    
    var categoryTags: [String] {
        return tags.filter { $0.category == contentType }.map { $0.name }
    }
}

// MARK: - Image Tagging Service
class ImageTaggingService {
    static let shared = ImageTaggingService()
    
    private var predefinedTags: [ImageContentType: [ImageTag]] = [:]
    private var userTags: [ImageTag] = []
    private var taggedImages: [String: TaggedImage] = [:]
    
    private init() {
        setupPredefinedTags()
        loadUserTags()
    }
    
    // MARK: - Public Methods
    
    /// Gets suggested tags for an image based on its analysis result
    /// - Parameter result: The image recognition result
    /// - Returns: Array of suggested tags
    func getSuggestedTags(for result: ImageRecognitionResult) -> [ImageTag] {
        var suggestedTags: [ImageTag] = []
        
        // Add category-specific tags
        if let categoryTags = predefinedTags[result.contentType] {
            suggestedTags.append(contentsOf: categoryTags.prefix(5))
        }
        
        // Add tags based on description keywords
        let descriptionTags = extractTagsFromDescription(result.description, contentType: result.contentType)
        suggestedTags.append(contentsOf: descriptionTags)
        
        // Add color-based tags
        let colorTags = createColorTags(from: result.colors, contentType: result.contentType)
        suggestedTags.append(contentsOf: colorTags)
        
        // Add mood-based tags
        if let mood = result.mood {
            let moodTag = ImageTag(name: mood, category: result.contentType, popularity: 1)
            suggestedTags.append(moodTag)
        }
        
        // Remove duplicates and sort by popularity
        let uniqueTags = Array(Set(suggestedTags.map { $0.name }))
            .compactMap { tagName in
                suggestedTags.first { $0.name == tagName }
            }
            .sorted { $0.popularity > $1.popularity }
        
        return Array(uniqueTags.prefix(10))
    }
    
    /// Tags an image with the provided tags
    /// - Parameters:
    ///   - imageId: The unique identifier for the image
    ///   - tags: The tags to apply to the image
    ///   - result: The recognition result for the image
    ///   - userId: Optional user ID who tagged the image
    func tagImage(imageId: String, with tags: [ImageTag], result: ImageRecognitionResult, userId: String? = nil) {
        let taggedImage = TaggedImage(
            imageId: imageId,
            tags: tags,
            contentType: result.contentType,
            confidence: result.confidence,
            timestamp: Date(),
            userId: userId
        )
        
        taggedImages[imageId] = taggedImage
        
        // Update tag popularity
        updateTagPopularity(tags)
        
        // Save to persistent storage
        saveTaggedImages()
    }
    
    /// Gets tags for a specific image
    /// - Parameter imageId: The image identifier
    /// - Returns: TaggedImage if found, nil otherwise
    func getTags(for imageId: String) -> TaggedImage? {
        return taggedImages[imageId]
    }
    
    /// Searches for images by tags
    /// - Parameter tagNames: Array of tag names to search for
    /// - Returns: Array of tagged images that match the search criteria
    func searchImages(by tagNames: [String]) -> [TaggedImage] {
        return taggedImages.values.filter { taggedImage in
            let imageTags = taggedImage.displayTags
            return tagNames.allSatisfy { searchTag in
                imageTags.contains { imageTag in
                    imageTag.lowercased().contains(searchTag.lowercased())
                }
            }
        }
    }
    
    /// Gets popular tags for a specific content type
    /// - Parameter contentType: The content type to get tags for
    /// - Returns: Array of popular tags
    func getPopularTags(for contentType: ImageContentType) -> [ImageTag] {
        let allTags = (predefinedTags[contentType] ?? []) + userTags.filter { $0.category == contentType }
        return allTags.sorted { $0.popularity > $1.popularity }.prefix(20).map { $0 }
    }
    
    /// Adds a custom user tag
    /// - Parameters:
    ///   - name: The tag name
    ///   - category: The content type category
    func addUserTag(name: String, category: ImageContentType) {
        let newTag = ImageTag(name: name, category: category, popularity: 1, isUserGenerated: true)
        userTags.append(newTag)
        saveUserTags()
    }
    
    /// Gets trending tags across all categories
    /// - Returns: Array of trending tags
    func getTrendingTags() -> [ImageTag] {
        let allTags = predefinedTags.values.flatMap { $0 } + userTags
        return allTags.sorted { $0.popularity > $1.popularity }.prefix(15).map { $0 }
    }
}

// MARK: - Private Methods
private extension ImageTaggingService {
    
    func setupPredefinedTags() {
        predefinedTags = [
            .landscape: [
                ImageTag(name: "mountain", category: .landscape, popularity: 100),
                ImageTag(name: "ocean", category: .landscape, popularity: 95),
                ImageTag(name: "sunset", category: .landscape, popularity: 90),
                ImageTag(name: "sunrise", category: .landscape, popularity: 85),
                ImageTag(name: "forest", category: .landscape, popularity: 80),
                ImageTag(name: "lake", category: .landscape, popularity: 75),
                ImageTag(name: "meadow", category: .landscape, popularity: 70),
                ImageTag(name: "desert", category: .landscape, popularity: 65),
                ImageTag(name: "snow", category: .landscape, popularity: 60),
                ImageTag(name: "waterfall", category: .landscape, popularity: 55)
            ],
            .portrait: [
                ImageTag(name: "selfie", category: .portrait, popularity: 100),
                ImageTag(name: "friends", category: .portrait, popularity: 90),
                ImageTag(name: "family", category: .portrait, popularity: 85),
                ImageTag(name: "couple", category: .portrait, popularity: 80),
                ImageTag(name: "party", category: .portrait, popularity: 75),
                ImageTag(name: "wedding", category: .portrait, popularity: 70),
                ImageTag(name: "graduation", category: .portrait, popularity: 65),
                ImageTag(name: "birthday", category: .portrait, popularity: 60),
                ImageTag(name: "travel", category: .portrait, popularity: 55),
                ImageTag(name: "work", category: .portrait, popularity: 50)
            ],
            .food: [
                ImageTag(name: "delicious", category: .food, popularity: 100),
                ImageTag(name: "dessert", category: .food, popularity: 90),
                ImageTag(name: "coffee", category: .food, popularity: 85),
                ImageTag(name: "breakfast", category: .food, popularity: 80),
                ImageTag(name: "lunch", category: .food, popularity: 75),
                ImageTag(name: "dinner", category: .food, popularity: 70),
                ImageTag(name: "baking", category: .food, popularity: 65),
                ImageTag(name: "healthy", category: .food, popularity: 60),
                ImageTag(name: "vegetarian", category: .food, popularity: 55),
                ImageTag(name: "hotpot", category: .food, popularity: 50)
            ],
            .travel: [
                ImageTag(name: "travel", category: .travel, popularity: 100),
                ImageTag(name: "vacation", category: .travel, popularity: 90),
                ImageTag(name: "adventure", category: .travel, popularity: 85),
                ImageTag(name: "city", category: .travel, popularity: 80),
                ImageTag(name: "beach", category: .travel, popularity: 75),
                ImageTag(name: "museum", category: .travel, popularity: 70),
                ImageTag(name: "historic", category: .travel, popularity: 65),
                ImageTag(name: "shopping", category: .travel, popularity: 60),
                ImageTag(name: "nightlife", category: .travel, popularity: 55),
                ImageTag(name: "transport", category: .travel, popularity: 50)
            ],
            .lifestyle: [
                ImageTag(name: "lifestyle", category: .lifestyle, popularity: 100),
                ImageTag(name: "fashion", category: .lifestyle, popularity: 90),
                ImageTag(name: "fitness", category: .lifestyle, popularity: 85),
                ImageTag(name: "reading", category: .lifestyle, popularity: 80),
                ImageTag(name: "music", category: .lifestyle, popularity: 75),
                ImageTag(name: "art", category: .lifestyle, popularity: 70),
                ImageTag(name: "pets", category: .lifestyle, popularity: 65),
                ImageTag(name: "home", category: .lifestyle, popularity: 60),
                ImageTag(name: "flowers", category: .lifestyle, popularity: 55),
                ImageTag(name: "handmade", category: .lifestyle, popularity: 50)
            ]
        ]
    }
    
    func extractTagsFromDescription(_ description: String, contentType: ImageContentType) -> [ImageTag] {
        let keywords = description.lowercased().components(separatedBy: .whitespacesAndNewlines)
        var extractedTags: [ImageTag] = []
        
        // Define keyword mappings
        let keywordMappings: [String: String] = [
            "beautiful": "beautiful",
            "sunset": "sunset",
            "sunrise": "sunrise",
            "mountain": "mountain",
            "ocean": "ocean",
            "forest": "forest",
            "people": "people",
            "person": "person",
            "food": "food",
            "building": "building",
            "nature": "nature"
        ]
        
        for keyword in keywords {
            if let tagName = keywordMappings[keyword] {
                let tag = ImageTag(name: tagName, category: contentType, popularity: 1)
                extractedTags.append(tag)
            }
        }
        
        return extractedTags
    }
    
    func createColorTags(from colors: [String], contentType: ImageContentType) -> [ImageTag] {
        return colors.compactMap { color in
            let colorName = translateColorName(color)
            return ImageTag(name: colorName, category: contentType, popularity: 1)
        }
    }
    
    func translateColorName(_ color: String) -> String {
        // Return color names in English
        return color
    }
    
    func updateTagPopularity(_ tags: [ImageTag]) {
        // In a real app, this would update the database
        // For now, we'll just increment popularity in memory
        for tag in tags {
            if let index = userTags.firstIndex(where: { $0.id == tag.id }) {
                userTags[index] = ImageTag(
                    name: tag.name,
                    category: tag.category,
                    popularity: tag.popularity + 1,
                    isUserGenerated: tag.isUserGenerated
                )
            }
        }
    }
    
    func saveTaggedImages() {
        // TODO: Implement persistent storage
        print("Saving tagged images to persistent storage")
    }
    
    func loadUserTags() {
        // TODO: Load user tags from persistent storage
        print("Loading user tags from persistent storage")
    }
    
    func saveUserTags() {
        // TODO: Save user tags to persistent storage
        print("Saving user tags to persistent storage")
    }
}
