//
//  CoinTransactionManager.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-04.
//

import Foundation

class CoinTransactionManager {
    static let shared = CoinTransactionManager()
    
    // MARK: - Properties
    private let userDefaults = UserDefaults.standard
    private let transactionsKey = "CoinTransactions"
    private let balanceKey = "CoinBalance"
    private let statisticsKey = "CoinStatistics"
    
    private init() {}
    
    // MARK: - Balance Management
    func getCurrentBalance(for userId: String) -> CoinBalance {
        if let data = userDefaults.data(forKey: "\(balanceKey)_\(userId)"),
           let balance = try? JSONDecoder().decode(CoinBalance.self, from: data) {
            return balance
        }

        // Create new balance for new user
        let newBalance = CoinBalance(userId: userId, initialCoins: CoinSystemConfig.newUserStartingCoins)
        saveBalance(newBalance)
        return newBalance
    }

    /// Get balance for existing user, preserving their current free processing count
    func getCurrentBalance(for userId: String, preservingFreeCount currentFreeCount: Int) -> CoinBalance {
        if let data = userDefaults.data(forKey: "\(balanceKey)_\(userId)"),
           var balance = try? JSONDecoder().decode(CoinBalance.self, from: data) {
            // For existing users, preserve their current free processing count
            balance.freeProcessingCount = currentFreeCount
            saveBalance(balance)
            return balance
        }

        // Create new balance for new user
        let newBalance = CoinBalance(userId: userId, initialCoins: CoinSystemConfig.newUserStartingCoins)
        saveBalance(newBalance)
        return newBalance
    }
    
    private func saveBalance(_ balance: CoinBalance) {
        if let data = try? JSONEncoder().encode(balance) {
            userDefaults.set(data, forKey: "\(balanceKey)_\(balance.userId)")
        }
    }
    
    func updateUserCoins(_ user: inout User) {
        // Check if this is a truly new user (never used AI processing)
        let isNewUser = user.totalProcessingCount == 0 && user.freeProcessingCount == AIProcessingPricing.freeProcessingLimit

        if isNewUser {
            // For new users, use the standard balance initialization
            let balance = getCurrentBalance(for: user.id)
            user.coins = balance.totalCoins
            user.freeProcessingCount = balance.freeProcessingCount
        } else {
            // For existing users, preserve their current free processing count
            let balance = getCurrentBalance(for: user.id, preservingFreeCount: user.freeProcessingCount)
            user.coins = balance.totalCoins
            // freeProcessingCount is already preserved in the balance

            // Migration: Convert existing coins to purchased AI processing count if needed
            migrateCoinsToAIProcessing(&user)
        }
    }

    /// Migrate existing coins to AI processing count for users who have coins but no purchased processing count
    private func migrateCoinsToAIProcessing(_ user: inout User) {
        // Only migrate if user has coins but no purchased processing count
        // and they're not a guest user (guest users shouldn't have purchased processing)
        guard !user.isGuestUser,
              user.purchasedProcessingCount == 0,
              user.coins >= AIProcessingPricing.fullProcessingCost else {
            return
        }

        // Calculate how many AI processing attempts the user can afford with their coins
        let affordableProcessing = user.coins / AIProcessingPricing.fullProcessingCost

        if affordableProcessing > 0 {
            print("🔄 [CoinTransaction] Migrating \(user.coins) coins to \(affordableProcessing) AI processing attempts for user: \(user.id)")

            // Convert coins to purchased processing count
            user.purchasedProcessingCount = affordableProcessing

            // Keep remaining coins (if any)
            user.coins = user.coins % AIProcessingPricing.fullProcessingCost

            print("✅ [CoinTransaction] Migration complete: \(user.purchasedProcessingCount) AI processing, \(user.coins) coins remaining")
        }
    }
    
    // MARK: - AI Processing Transactions
    func processAIImageAnalysis(for userId: String, user: inout User) -> Result<CoinTransaction, CoinSystemError> {
        print("🚀 [CoinTransaction] Processing AI image analysis for user: \(userId)")

        print("📊 [CoinTransaction] User before processing:")
        print("   - Free processing: \(user.freeProcessingCount)")
        print("   - Purchased processing: \(user.purchasedProcessingCount)")
        print("   - Can afford processing: \(user.canAffordProcessing)")

        // Check if user has available AI processing (free or purchased)
        guard user.canAffordProcessing else {
            print("❌ [CoinTransaction] User cannot afford processing")
            print("   - Free processing: \(user.freeProcessingCount)")
            print("   - Purchased processing: \(user.purchasedProcessingCount)")
            print("   - Total available: \(user.totalAvailableProcessing)")
            print("   - Is guest user: \(user.isGuestUser)")

            // Create a more appropriate error for AI processing
            return .failure(.processingLimitReached)
        }

        let cost = 0 // No coin cost, using direct AI count
        let description: String

        if user.freeProcessingCount > 0 {
            // Use free processing
            user.freeProcessingCount -= 1
            description = "Free AI Image Processing (Remaining: \(user.freeProcessingCount))"
            print("✅ [CoinTransaction] Used free processing, remaining: \(user.freeProcessingCount)")
        } else if user.purchasedProcessingCount > 0 {
            // Use purchased processing
            user.purchasedProcessingCount -= 1
            description = "AI Image Processing (Purchased count used)"
            print("✅ [CoinTransaction] Used purchased processing, remaining: \(user.purchasedProcessingCount)")
        } else {
            print("❌ [CoinTransaction] No processing available")
            return .failure(.insufficientFunds(required: 1, available: 0))
        }

        // Create transaction record (for tracking purposes, no coin cost)
        let transaction = CoinTransaction(
            userId: userId,
            type: .aiProcessing,
            amount: cost,
            balanceAfter: user.coins, // Coins remain unchanged
            description: description,
            metadata: [
                "processing_type": "full_analysis",
                "free_processing_used": user.freeProcessingCount > 0 ? "true" : "false",
                "purchased_processing_used": user.purchasedProcessingCount > 0 ? "true" : "false"
            ]
        )

        // Update user in AppViewModel
        AppViewModel.shared.currentUser = user
        user.totalProcessingCount += 1

        // Update CoinBalance to reflect the new free processing count
        var balance = getCurrentBalance(for: userId, preservingFreeCount: user.freeProcessingCount)
        saveBalance(balance)

        // Persist the updated user data
        UserAuthenticationManager.shared.updateUserData(user)

        // Record transaction for tracking
        recordTransaction(transaction)

        // Update statistics
        updateStatistics(for: userId, transaction: transaction)
        
        // Post notification for UI updates
        NotificationCenter.default.post(name: .coinBalanceDidUpdate, object: nil)
        
        return .success(transaction)
    }
    
    // MARK: - Purchase Transactions
    func processCoinPurchase(package: CoinPurchasePackage, for userId: String) -> Result<CoinTransaction, CoinSystemError> {
        var balance = getCurrentBalance(for: userId)

        let totalCoins = package.totalCoins
        balance.totalCoins += totalCoins

        let description = "Purchased \(package.title)"
        let transaction = CoinTransaction(
            userId: userId,
            type: .purchase,
            amount: totalCoins,
            balanceAfter: balance.totalCoins,
            description: description,
            metadata: [
                "package_id": package.id,
                "product_id": package.productId,
                "base_coins": "\(package.coins)",
                "bonus_coins": "\(package.bonusCoins)",
                "price": package.price,
                "source": "simulation"
            ]
        )

        // Save updated balance
        saveBalance(balance)

        // Record transaction
        recordTransaction(transaction)

        // Update statistics
        updateStatistics(for: userId, transaction: transaction)

        // Update user in AppViewModel
        if AppViewModel.shared.currentUser.id == userId {
            AppViewModel.shared.currentUser.coins = balance.totalCoins
        }

        // Post notification for UI updates
        NotificationCenter.default.post(name: .coinBalanceDidUpdate, object: nil)

        return .success(transaction)
    }

    // MARK: - IAP Integration
    func processIAPPurchase(coinAmount: Int, productId: String, for userId: String) -> Result<CoinTransaction, CoinSystemError> {
        var balance = getCurrentBalance(for: userId)

        balance.totalCoins += coinAmount

        let description = "In-App Purchase"
        let transaction = CoinTransaction(
            userId: userId,
            type: .purchase,
            amount: coinAmount,
            balanceAfter: balance.totalCoins,
            description: description,
            metadata: [
                "product_id": productId,
                "source": "storekit"
            ]
        )

        // Save updated balance
        saveBalance(balance)

        // Record transaction
        recordTransaction(transaction)

        // Update statistics
        updateStatistics(for: userId, transaction: transaction)

        // Update user in AppViewModel
        if AppViewModel.shared.currentUser.id == userId {
            AppViewModel.shared.currentUser.coins = balance.totalCoins
        }

        // Post notification for UI updates
        NotificationCenter.default.post(name: .coinBalanceDidUpdate, object: nil)

        return .success(transaction)
    }

    // MARK: - Content Publishing
    func processContentPublishing(for userId: String) -> Result<CoinTransaction, CoinSystemError> {
        // Use AppViewModel's current user directly for consistency
        guard AppViewModel.shared.currentUser.id == userId else {
            return .failure(.userNotFound)
        }

        var user = AppViewModel.shared.currentUser
        let publishingCost = CoinSystemConfig.contentPublishingCost

        // Check if user can afford publishing
        guard user.coins >= publishingCost else {
            return .failure(.insufficientFunds(
                required: publishingCost,
                available: user.coins
            ))
        }

        // Deduct coins for publishing
        user.coins -= publishingCost

        let transaction = CoinTransaction(
            userId: userId,
            type: .contentPublishing,
            amount: publishingCost,
            balanceAfter: user.coins,
            description: "Published content to Discover",
            metadata: [
                "publishing_cost": "\(publishingCost)",
                "platform": "discover_feed"
            ]
        )

        // Update user in AppViewModel
        AppViewModel.shared.currentUser = user

        // Persist the updated user data to storage
        UserAuthenticationManager.shared.updateUserData(user)
        print("💾 [CoinTransaction] Persisted content publishing transaction for user: \(userId)")

        // Record transaction
        recordTransaction(transaction)

        // Update statistics
        updateStatistics(for: userId, transaction: transaction)

        // Post notification for UI updates
        NotificationCenter.default.post(name: .coinBalanceDidUpdate, object: nil)

        return .success(transaction)
    }

    // MARK: - Daily Bonus
    func claimDailyBonus(for userId: String) -> Result<CoinTransaction, CoinSystemError> {
        guard AppViewModel.shared.currentUser.canClaimDailyBonus else {
            return .failure(.invalidTransaction)
        }
        
        var balance = getCurrentBalance(for: userId)
        balance.totalCoins += CoinSystemConfig.dailyBonusAmount
        
        let transaction = CoinTransaction(
            userId: userId,
            type: .dailyBonus,
            amount: CoinSystemConfig.dailyBonusAmount,
            balanceAfter: balance.totalCoins,
            description: "Daily Login Bonus",
            metadata: ["bonus_day": "\(AppViewModel.shared.currentUser.daysSinceRegistration)"]
        )
        
        // Save updated balance
        saveBalance(balance)
        
        // Record transaction
        recordTransaction(transaction)
        
        // Update statistics
        updateStatistics(for: userId, transaction: transaction)
        
        // Update user in AppViewModel and persist the changes
        if AppViewModel.shared.currentUser.id == userId {
            AppViewModel.shared.currentUser.coins = balance.totalCoins
            // Update daily bonus status without adding coins again
            AppViewModel.shared.currentUser.hasClaimedDailyBonus = true
            AppViewModel.shared.currentUser.lastDailyBonusDate = Date()

            // Persist the updated user data to storage
            UserAuthenticationManager.shared.updateUserData(AppViewModel.shared.currentUser)
            print("💾 [CoinTransaction] Persisted daily bonus claim for user: \(userId)")
        }
        
        // Post notification for UI updates
        NotificationCenter.default.post(name: .coinBalanceDidUpdate, object: nil)
        
        return .success(transaction)
    }
    
    // MARK: - Transaction History
    func getTransactionHistory(for userId: String, limit: Int = 50) -> [CoinTransaction] {
        let allTransactions = loadAllTransactions()
        return Array(allTransactions
            .filter { $0.userId == userId }
            .sorted { $0.timestamp > $1.timestamp }
            .prefix(limit))
    }
    
    private func recordTransaction(_ transaction: CoinTransaction) {
        var transactions = loadAllTransactions()
        transactions.append(transaction)
        saveTransactions(transactions)
    }
    
    private func loadAllTransactions() -> [CoinTransaction] {
        guard let data = userDefaults.data(forKey: transactionsKey),
              let transactions = try? JSONDecoder().decode([CoinTransaction].self, from: data) else {
            return []
        }
        return transactions
    }
    
    private func saveTransactions(_ transactions: [CoinTransaction]) {
        if let data = try? JSONEncoder().encode(transactions) {
            userDefaults.set(data, forKey: transactionsKey)
        }
    }
    
    // MARK: - Statistics
    func getStatistics(for userId: String) -> CoinUsageStatistics {
        if let data = userDefaults.data(forKey: "\(statisticsKey)_\(userId)"),
           let stats = try? JSONDecoder().decode(CoinUsageStatistics.self, from: data) {
            return stats
        }
        
        // Create new statistics
        let newStats = CoinUsageStatistics(userId: userId)
        saveStatistics(newStats)
        return newStats
    }
    
    private func updateStatistics(for userId: String, transaction: CoinTransaction) {
        var stats = getStatistics(for: userId)
        
        stats.totalTransactions += 1
        
        if transaction.type.isDebit {
            stats.totalSpent += transaction.amount
            if transaction.type == .aiProcessing {
                stats.aiProcessingCount += 1
                stats.lastProcessingDate = transaction.timestamp
            }
        } else {
            stats.totalEarned += transaction.amount
        }
        
        // Calculate average spending per day
        let daysSinceStart = Calendar.current.dateComponents([.day], from: stats.trackingStartDate, to: Date()).day ?? 1
        stats.averageSpendingPerDay = Double(stats.totalSpent) / Double(max(daysSinceStart, 1))
        
        saveStatistics(stats)
    }
    
    private func saveStatistics(_ statistics: CoinUsageStatistics) {
        if let data = try? JSONEncoder().encode(statistics) {
            userDefaults.set(data, forKey: "\(statisticsKey)_\(statistics.userId)")
        }
    }
    
    // MARK: - Validation
    func validateProcessingRequest(for userId: String) -> (canProcess: Bool, cost: Int, message: String) {
        print("🔍 [CoinTransaction] Validating processing request for user: \(userId)")

        guard AppViewModel.shared.currentUser.id == userId else {
            print("❌ [CoinTransaction] User ID mismatch: expected \(userId), got \(AppViewModel.shared.currentUser.id)")
            return (false, 0, "User not found")
        }

        let user = AppViewModel.shared.currentUser
        let totalAvailable = user.totalAvailableProcessing

        print("📊 [CoinTransaction] User status:")
        print("   - Free processing: \(user.freeProcessingCount)")
        print("   - Purchased processing: \(user.purchasedProcessingCount)")
        print("   - Total available: \(totalAvailable)")
        print("   - Is guest: \(user.isGuestUser)")
        print("   - Coins: \(user.coins)")

        if user.freeProcessingCount > 0 {
            // Has free processing available
            print("✅ [CoinTransaction] Free processing available")
            if user.purchasedProcessingCount > 0 {
                return (true, 0, "Free processing available (\(user.freeProcessingCount) free + \(user.purchasedProcessingCount) purchased = \(totalAvailable) total)")
            } else {
                return (true, 0, "Free processing available (\(user.freeProcessingCount) remaining)")
            }
        } else if user.purchasedProcessingCount > 0 {
            // Only purchased processing available
            print("✅ [CoinTransaction] Purchased processing available")
            return (true, 0, "Purchased AI processing available (\(user.purchasedProcessingCount) analyses remaining)")
        } else {
            // No processing available
            print("❌ [CoinTransaction] No processing available")
            if user.isGuestUser {
                return (false, 0, "No free processing left. Sign in to purchase more AI analyses")
            } else {
                return (false, 0, "No AI analyses available. Purchase more to continue using AI features")
            }
        }
    }
    
    // MARK: - Cleanup
    func clearUserData(for userId: String) {
        userDefaults.removeObject(forKey: "\(balanceKey)_\(userId)")
        userDefaults.removeObject(forKey: "\(statisticsKey)_\(userId)")
        
        // Remove user transactions
        let allTransactions = loadAllTransactions()
        let filteredTransactions = allTransactions.filter { $0.userId != userId }
        saveTransactions(filteredTransactions)
    }
}

// MARK: - Notification Names
extension Notification.Name {
    static let coinBalanceDidUpdate = Notification.Name("coinBalanceDidUpdate")
    static let coinTransactionCompleted = Notification.Name("coinTransactionCompleted")
}
