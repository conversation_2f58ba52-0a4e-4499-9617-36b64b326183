import Foundation

// MARK: - Friends Manager
class FriendsManager {
    static let shared = FriendsManager()
    
    private let userDefaults = UserDefaults.standard
    private let friendsKey = "user_friends_list"
    
    private init() {}
    
    // MARK: - Friends Management
    
    /// Get all friends (only added friends, no default friends for new users)
    func getAllFriends() -> [Friend] {
        // Only return friends that have been added through chat interactions
        return getAddedFriends()
    }
    
    /// Add a new friend to the list
    func addFriend(_ friend: Friend) {
        var addedFriends = getAddedFriends()

        // Check if friend already exists
        if !addedFriends.contains(where: { $0.userId == friend.userId }) {
            addedFriends.append(friend)
            saveAddedFriends(addedFriends)

            // Post notification for UI updates
            NotificationCenter.default.post(
                name: .friendsListDidUpdate,
                object: nil,
                userInfo: ["addedFriend": friend]
            )
        }
    }
    
    /// Remove a friend from the list
    func removeFriend(userId: String) {
        var addedFriends = getAddedFriends()
        addedFriends.removeAll { $0.userId == userId }
        saveAddedFriends(addedFriends)
        
        // Post notification for UI updates
        NotificationCenter.default.post(
            name: .friendsListDidUpdate,
            object: nil,
            userInfo: ["removedFriendId": userId]
        )
    }
    
    /// Check if a user is already a friend
    func isFriend(userId: String) -> Bool {
        let allFriends = getAllFriends()
        return allFriends.contains { $0.userId == userId }
    }
    
    /// Get a friend by userId
    func getFriend(by userId: String) -> Friend? {
        let allFriends = getAllFriends()
        return allFriends.first { $0.userId == userId }
    }
    
    /// Add friend from Share.Author (used when commenting)
    func addFriendFromShareAuthor(_ author: Share.Author) {
        // Check if already a friend
        if !isFriend(userId: author.id) {
            let newFriend = Friend.fromShareAuthor(author)
            addFriend(newFriend)
        }
    }
    
    // MARK: - Data Persistence
    
    private func getAddedFriends() -> [Friend] {
        guard let data = userDefaults.data(forKey: friendsKey),
              let friends = try? JSONDecoder().decode([Friend].self, from: data) else {
            return []
        }
        return friends
    }
    
    private func saveAddedFriends(_ friends: [Friend]) {
        if let data = try? JSONEncoder().encode(friends) {
            userDefaults.set(data, forKey: friendsKey)
        }
    }
    
    // MARK: - Statistics
    
    /// Get friends statistics
    func getFriendsStatistics() -> FriendsStatistics {
        let allFriends = getAllFriends()

        let onlineFriends = allFriends.filter { $0.isOnline }
        let recentlyActiveFriends = allFriends.filter { friend in
            FriendActivityManager.shared.hasRecentActivity(with: friend.userId)
        }

        return FriendsStatistics(
            totalFriends: allFriends.count,
            defaultFriends: 0, // No default friends anymore
            addedFriends: allFriends.count,
            onlineFriends: onlineFriends.count,
            recentlyActiveFriends: recentlyActiveFriends.count
        )
    }
    
    /// Clear all added friends (keep only default friends)
    func clearAddedFriends() {
        userDefaults.removeObject(forKey: friendsKey)
        
        // Post notification for UI updates
        NotificationCenter.default.post(
            name: .friendsListDidUpdate,
            object: nil
        )
    }
}

// MARK: - Friends Statistics
struct FriendsStatistics {
    let totalFriends: Int
    let defaultFriends: Int
    let addedFriends: Int
    let onlineFriends: Int
    let recentlyActiveFriends: Int
}

// MARK: - Notification Names
extension Notification.Name {
    static let friendsListDidUpdate = Notification.Name("friendsListDidUpdate")
}
