//
//  IAPManager.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-06.
//

import Foundation
import StoreKit

// MARK: - IAP Manager
class IAPManager: NSObject, ObservableObject {
    static let shared = IAPManager()
    
    private var products: [SKProduct] = []
    private var productRequest: SKProductsRequest?
    
    // Completion handlers
    private var purchaseCompletionHandler: ((Bo<PERSON>, Error?) -> Void)?
    private var productsCompletionHandler: (([SKProduct]) -> Void)?
    
    // Debug mode
    #if DEBUG
    var isTestMode: Bool = false
    var debugLoggingEnabled: Bool = true
    #endif

    private func debugLog(_ message: String) {
        #if DEBUG
        if debugLoggingEnabled {
            print("IAP Debug: \(message)")
        }
        #endif
    }
    
    override init() {
        super.init()
        SKPaymentQueue.default().add(self)
        requestProducts()
    }
    
    deinit {
        SKPaymentQueue.default().remove(self)
    }
    
    // MARK: - Product Request
    func requestProducts() {
        let productIdentifiers = CoinPurchasePackage.productIdSet
        productRequest = SKProductsRequest(productIdentifiers: productIdentifiers)
        productRequest?.delegate = self
        productRequest?.start()

        debugLog("Requesting products: \(productIdentifiers)")
    }
    
    func getProducts(completion: @escaping ([SKProduct]) -> Void) {
        if !products.isEmpty {
            completion(products)
        } else {
            productsCompletionHandler = completion
            requestProducts()
        }
    }
    
    // MARK: - Purchase
    func purchaseProduct(_ product: SKProduct, completion: @escaping (Bool, Error?) -> Void) {
        guard SKPaymentQueue.canMakePayments() else {
            debugLog("Cannot make payments")
            completion(false, IAPError.cannotMakePayments)
            return
        }

        debugLog("Starting purchase for product: \(product.productIdentifier)")
        purchaseCompletionHandler = completion
        let payment = SKPayment(product: product)
        SKPaymentQueue.default().add(payment)
    }
    
    func purchaseCoinPackage(_ package: CoinPurchasePackage, completion: @escaping (Bool, Error?) -> Void) {
        #if DEBUG
        if isTestMode {
            // Simulate purchase for testing
            DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
                self.simulatePurchaseSuccess(package)
                completion(true, nil)
            }
            return
        }
        #endif

        guard let product = products.first(where: { $0.productIdentifier == package.productId }) else {
            debugLog("Product not found for package: \(package.productId)")
            completion(false, IAPError.productNotFound)
            return
        }

        purchaseProduct(product, completion: completion)
    }
    
    // MARK: - Helper Methods
    func getProduct(for package: CoinPurchasePackage) -> SKProduct? {
        return products.first { $0.productIdentifier == package.productId }
    }

    func formatPrice(for product: SKProduct) -> String {
        let formatter = NumberFormatter()
        formatter.numberStyle = .currency
        formatter.locale = product.priceLocale
        return formatter.string(from: product.price) ?? package(for: product.productIdentifier)?.fallbackPrice ?? "$0.00"
    }

    func package(for productIdentifier: String) -> CoinPurchasePackage? {
        return CoinPurchasePackage.package(for: productIdentifier)
    }
    
    #if DEBUG
    private func simulatePurchaseSuccess(_ package: CoinPurchasePackage) {
        print("IAP Debug: Simulating purchase success for: \(package.displayName)")
        addCoinsAndAIAnalysisToUser(package)
    }
    #endif
}

// MARK: - SKProductsRequestDelegate
extension IAPManager: SKProductsRequestDelegate {
    func productsRequest(_ request: SKProductsRequest, didReceive response: SKProductsResponse) {
        print("IAP Debug: Products request completed")
        print("IAP Debug: Valid products count: \(response.products.count)")
        
        for product in response.products {
            print("IAP Debug: Product - ID: \(product.productIdentifier), Title: \(product.localizedTitle), Price: \(product.price)")
        }
        
        if !response.invalidProductIdentifiers.isEmpty {
            print("IAP Debug: Invalid product IDs: \(response.invalidProductIdentifiers)")
        }

        DispatchQueue.main.async {
            self.products = response.products
            self.productsCompletionHandler?(self.products)
            self.productsCompletionHandler = nil
        }
    }
    
    func request(_ request: SKRequest, didFailWithError error: Error) {
        print("IAP Debug: Products request failed: \(error.localizedDescription)")
        DispatchQueue.main.async {
            self.productsCompletionHandler?([])
            self.productsCompletionHandler = nil
        }
    }
}

// MARK: - SKPaymentTransactionObserver
extension IAPManager: SKPaymentTransactionObserver {
    func paymentQueue(_ queue: SKPaymentQueue, updatedTransactions transactions: [SKPaymentTransaction]) {
        for transaction in transactions {
            print("IAP Debug: Transaction state updated: \(transaction.transactionState.rawValue) for \(transaction.payment.productIdentifier)")
            
            switch transaction.transactionState {
            case .purchased:
                handlePurchaseSuccess(transaction)
            case .failed:
                handlePurchaseFailure(transaction)
            case .restored:
                // Consumable products don't need restore functionality
                SKPaymentQueue.default().finishTransaction(transaction)
            case .deferred, .purchasing:
                break
            @unknown default:
                break
            }
        }
    }
    
    private func handlePurchaseSuccess(_ transaction: SKPaymentTransaction) {
        print("IAP Debug: Purchase successful for: \(transaction.payment.productIdentifier)")

        // Add coins and AI analysis count to user account
        if let package = CoinPurchasePackage.package(for: transaction.payment.productIdentifier) {
            addCoinsAndAIAnalysisToUser(package)
        }

        SKPaymentQueue.default().finishTransaction(transaction)

        DispatchQueue.main.async {
            self.purchaseCompletionHandler?(true, nil)
            self.purchaseCompletionHandler = nil
        }
    }
    
    private func handlePurchaseFailure(_ transaction: SKPaymentTransaction) {
        print("IAP Debug: Purchase failed for: \(transaction.payment.productIdentifier)")
        if let error = transaction.error {
            print("IAP Debug: Error: \(error.localizedDescription)")
        }
        
        SKPaymentQueue.default().finishTransaction(transaction)
        
        DispatchQueue.main.async {
            self.purchaseCompletionHandler?(false, transaction.error)
            self.purchaseCompletionHandler = nil
        }
    }
    
    private func addCoinsAndAIAnalysisToUser(_ package: CoinPurchasePackage) {
        guard var currentUser = UserAuthenticationManager.shared.getCurrentUser() else {
            print("IAP Debug: No current user found")
            return
        }

        print("IAP Debug: Starting purchase processing for package: \(package.title)")
        print("IAP Debug: Package details - Coins: \(package.totalCoins), AI Analyses: \(package.aiAnalysisCount)")
        print("IAP Debug: User before purchase - ID: \(currentUser.id), Coins: \(currentUser.coins), Purchased AI: \(currentUser.purchasedProcessingCount)")

        // Add coins using existing method
        let result = CoinTransactionManager.shared.processIAPPurchase(
            coinAmount: package.totalCoins,
            productId: package.productId,
            for: currentUser.id
        )

        switch result {
        case .success(let transaction):
            print("IAP Debug: Successfully added coins. New balance: \(transaction.balanceAfter)")
            print("IAP Debug: AppViewModel user before AI update - Purchased AI: \(AppViewModel.shared.currentUser.purchasedProcessingCount)")

            // Add AI analysis count directly to AppViewModel's current user
            AppViewModel.shared.currentUser.addPurchasedProcessing(package.aiAnalysisCount)
            AppViewModel.shared.currentUser.coins = transaction.balanceAfter

            print("IAP Debug: AppViewModel user after AI update - Purchased AI: \(AppViewModel.shared.currentUser.purchasedProcessingCount)")
            print("IAP Debug: Total available AI analyses: \(AppViewModel.shared.currentUser.totalAvailableProcessing)")
            print("IAP Debug: User details - Free: \(AppViewModel.shared.currentUser.freeProcessingCount), Purchased: \(AppViewModel.shared.currentUser.purchasedProcessingCount)")

            // Persist the updated user data
            UserAuthenticationManager.shared.updateUserData(AppViewModel.shared.currentUser)
            print("IAP Debug: User data persisted successfully")

            // Post notification to update UI
            DispatchQueue.main.async {
                print("IAP Debug: Posting coinBalanceDidUpdate notification")
                NotificationCenter.default.post(name: .coinBalanceDidUpdate, object: nil)
            }

        case .failure(let error):
            print("IAP Debug: Failed to add coins: \(error.localizedDescription)")
        }
    }
}

// MARK: - IAP Errors
enum IAPError: Error, LocalizedError {
    case cannotMakePayments
    case productNotFound
    case purchaseFailed
    
    var errorDescription: String? {
        switch self {
        case .cannotMakePayments:
            return "In-app purchases are not allowed on this device."
        case .productNotFound:
            return "The requested product was not found."
        case .purchaseFailed:
            return "The purchase failed. Please try again."
        }
    }
}
