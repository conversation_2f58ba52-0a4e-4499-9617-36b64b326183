//
//  ImageContentRecognitionService.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-04.
//

import Foundation
import UIKit
import GoogleGenerativeAI
import Vision

// MARK: - Image Content Types
enum ImageContentType: String, CaseIterable {
    case landscape = "landscape"
    case portrait = "portrait"
    case food = "food"
    case architecture = "architecture"
    case nature = "nature"
    case animal = "animal"
    case object = "object"
    case travel = "travel"
    case lifestyle = "lifestyle"
    case unknown = "unknown"
    
    var displayName: String {
        switch self {
        case .landscape: return "Landscape"
        case .portrait: return "Portrait"
        case .food: return "Food"
        case .architecture: return "Architecture"
        case .nature: return "Nature"
        case .animal: return "Animal"
        case .object: return "Object"
        case .travel: return "Travel"
        case .lifestyle: return "Lifestyle"
        case .unknown: return "Unknown"
        }
    }
    
    var emoji: String {
        switch self {
        case .landscape: return "🏞️"
        case .portrait: return "👤"
        case .food: return "🍽️"
        case .architecture: return "🏛️"
        case .nature: return "🌿"
        case .animal: return "🐾"
        case .object: return "📦"
        case .travel: return "✈️"
        case .lifestyle: return "✨"
        case .unknown: return "❓"
        }
    }
}

// MARK: - Image Recognition Result
struct ImageRecognitionResult {
    let contentType: ImageContentType
    let confidence: Double
    let description: String
    let tags: [String]
    let colors: [String]
    let mood: String?
    let isAppropriate: Bool

    var displayText: String {
        return "\(contentType.emoji) \(contentType.displayName): \(description)"
    }
}

// MARK: - Vision Analysis Result
struct VisionAnalysisResult {
    let hasText: Bool
    let hasFaces: Bool
    let hasObjects: Bool
}

// MARK: - Image Content Recognition Service
class ImageContentRecognitionService {
    static let shared = ImageContentRecognitionService()
    
    // Get the API key from the configuration
    private var geminiAPIKey: String {
        return APIConfiguration.geminiAPIKey
    }
    
    private init() {}
    
    /// Analyzes an image and returns detailed content information
    /// - Parameters:
    ///   - image: The image to analyze
    ///   - useAI: Whether to use AI analysis (if false, will use fallback analysis directly)
    /// - Returns: ImageRecognitionResult containing detailed analysis
    func analyzeImageContent(_ image: UIImage, useAI: Bool = true) async -> ImageRecognitionResult {
        print("🖼️ ImageContentRecognitionService: Starting image analysis")
        print("🖼️ Image size: \(image.size)")
        print("🖼️ API Key available: \(!geminiAPIKey.isEmpty)")
        print("🖼️ Use AI: \(useAI)")

        // First try the AI-based analysis if API key is available and useAI is true
        if !geminiAPIKey.isEmpty && useAI {
            print("🤖 Attempting AI-based analysis with Gemini...")
            do {
                let result = try await performAIAnalysis(image)
                print("✅ AI analysis completed successfully")
                print("📊 Result: \(result.contentType.displayName) with confidence \(result.confidence)")
                return result
            } catch {
                print("❌ Error analyzing image with AI: \(error)")
                print("🔄 Falling back to basic analysis...")
                // Fall through to the fallback method
            }
        } else {
            if geminiAPIKey.isEmpty {
                print("⚠️ No API key available, using fallback analysis")
            } else if !useAI {
                print("⚠️ AI analysis disabled, using fallback analysis")
            }
        }

        // Fallback to basic image analysis if AI analysis fails, API key is invalid, or useAI is false
        print("🔧 Performing fallback analysis...")
        let result = await performFallbackAnalysis(image)
        print("✅ Fallback analysis completed")
        print("📊 Result: \(result.contentType.displayName) with confidence \(result.confidence)")
        return result
    }
    
    /// Analyzes multiple images and returns their content information
    /// - Parameter images: Array of images to analyze
    /// - Returns: Array of ImageRecognitionResult
    func analyzeMultipleImages(_ images: [UIImage]) async -> [ImageRecognitionResult] {
        var results: [ImageRecognitionResult] = []
        
        for image in images {
            let result = await analyzeImageContent(image)
            results.append(result)
        }
        
        return results
    }
    
    /// Gets suggested tags for an image based on its content
    /// - Parameter image: The image to get tags for
    /// - Returns: Array of suggested tags
    func getSuggestedTags(for image: UIImage) async -> [String] {
        let result = await analyzeImageContent(image)
        return result.tags
    }
    
    /// Checks if an image is appropriate for sharing
    /// - Parameter image: The image to check
    /// - Returns: Boolean indicating if the image is appropriate
    func isImageAppropriate(_ image: UIImage) async -> Bool {
        let result = await analyzeImageContent(image)
        return result.isAppropriate
    }

    /// Generates a caption for an image based on its content
    /// - Parameter image: The image to generate caption for
    /// - Returns: Generated caption string
    func generateCaption(for image: UIImage) async -> String {
        let user = AppViewModel.shared.currentUser
        let useAI = user.totalAvailableProcessing > 0

        print("🖼️ Generating caption - User has \(user.totalAvailableProcessing) AI processing available")
        print("🖼️ Will use AI: \(useAI)")

        let result = await analyzeImageContent(image, useAI: useAI)
        return generateCaptionFromResult(result)
    }

    /// Compares two images and determines their similarity
    /// - Parameters:
    ///   - image1: First image to compare
    ///   - image2: Second image to compare
    /// - Returns: Similarity score between 0.0 and 1.0
    func compareImages(_ image1: UIImage, _ image2: UIImage) async -> Double {
        let result1 = await analyzeImageContent(image1)
        let result2 = await analyzeImageContent(image2)

        return calculateSimilarity(between: result1, and: result2)
    }

    /// Gets mood and atmosphere information from an image
    /// - Parameter image: The image to analyze
    /// - Returns: Mood description and confidence
    func analyzeMood(in image: UIImage) async -> (mood: String, confidence: Double) {
        let result = await analyzeImageContent(image)
        return (result.mood ?? "neutral", result.confidence)
    }
}

// MARK: - Private Methods
private extension ImageContentRecognitionService {
    
    /// Performs AI-based image analysis using Google Gemini
    func performAIAnalysis(_ image: UIImage) async throws -> ImageRecognitionResult {
        print("🚀 Starting Gemini AI analysis...")
        print("🔑 Using API key: \(String(geminiAPIKey.prefix(10)))...")

        let systemPrompt = """
        You are an advanced image content analysis system. Your task is to analyze images and provide detailed information about their content.

        Analyze the image and provide the following information:
        1. Primary content type (landscape, portrait, food, architecture, nature, animal, object, travel, lifestyle)
        2. Confidence level (0.0 to 1.0)
        3. Detailed description of what you see
        4. Relevant tags (up to 10)
        5. Dominant colors (up to 5)
        6. Overall mood or atmosphere
        7. Content appropriateness (safe for social sharing)

        Respond with a JSON object containing:
        {
            "contentType": "string",
            "confidence": number,
            "description": "string",
            "tags": ["string"],
            "colors": ["string"],
            "mood": "string",
            "isAppropriate": boolean
        }

        Be descriptive but concise. Focus on the main elements and overall composition.
        """

        print("📝 System prompt prepared (\(systemPrompt.count) characters)")
        print("⚙️ Setting up generation config...")
        
        let config = GenerationConfig(
            temperature: 0.3,
            topP: 0.95,
            topK: 40,
            maxOutputTokens: 2048,
            responseMIMEType: "application/json"
        )

        print("🤖 Creating Gemini model with config:")
        print("   - Model: gemini-2.0-flash")
        print("   - Temperature: 0.3")
        print("   - Max tokens: 2048")
        print("   - Response type: JSON")

        let model = GenerativeModel(
            name: "gemini-2.0-flash",
            apiKey: geminiAPIKey,
            generationConfig: config,
            systemInstruction: systemPrompt
        )

        print("✅ Model created successfully")
        print("📤 Sending request to Gemini API...")

        let prompt = "Analyze this image and provide detailed content information in the specified JSON format."
        let response = try await model.generateContent(prompt, image)

        print("📥 Received response from Gemini API")

        if let jsonString = response.text {
            print("📄 Response text length: \(jsonString.count) characters")
            print("📄 Response preview: \(String(jsonString.prefix(200)))...")
            let result = parseAnalysisResponse(jsonString)
            print("✅ Successfully parsed AI response")
            return result
        } else {
            print("❌ No response text from AI")
            throw NSError(domain: "ImageAnalysis", code: 1, userInfo: [NSLocalizedDescriptionKey: "No response from AI"])
        }
    }
    
    /// Fallback method for basic image analysis using Vision framework
    func performFallbackAnalysis(_ image: UIImage) async -> ImageRecognitionResult {
        // Basic analysis using image properties and Vision framework
        let aspectRatio = image.size.width / image.size.height
        let imageSize = image.size

        // Determine basic content type based on aspect ratio and size
        var contentType: ImageContentType = .unknown
        var description = "Image content"
        var tags: [String] = []
        var confidence: Double = 0.5

        // Enhanced classification using multiple factors
        let analysisResults = await performMultipleVisionAnalyses(image)

        // Analyze aspect ratio
        if aspectRatio > 1.8 {
            contentType = .landscape
            description = "Wide landscape or panoramic view"
            tags.append(contentsOf: ["landscape", "wide", "panoramic"])
            confidence += 0.1
        } else if aspectRatio < 0.7 {
            contentType = .portrait
            description = "Portrait or vertical composition"
            tags.append(contentsOf: ["portrait", "vertical"])
            confidence += 0.1
        } else {
            contentType = .lifestyle
            description = "Standard format image"
            tags.append(contentsOf: ["standard", "square"])
        }

        // Incorporate Vision analysis results
        if analysisResults.hasText {
            tags.append("text")
            confidence += 0.05
        }

        if analysisResults.hasFaces {
            contentType = .portrait
            description = "Image with people"
            tags.append(contentsOf: ["people", "faces"])
            confidence += 0.15
        }

        if analysisResults.hasObjects {
            tags.append("objects")
            confidence += 0.05
        }

        // Basic color analysis
        let dominantColors = await extractDominantColors(from: image)

        // Determine mood based on colors and composition
        let mood = determineMoodFromColors(dominantColors, aspectRatio: aspectRatio)

        return ImageRecognitionResult(
            contentType: contentType,
            confidence: min(confidence, 0.8), // Cap confidence for fallback method
            description: description,
            tags: Array(Set(tags)), // Remove duplicates
            colors: dominantColors,
            mood: mood,
            isAppropriate: true // Assume appropriate unless we can detect otherwise
        )
    }
    
    /// Performs multiple Vision analyses on an image
    func performMultipleVisionAnalyses(_ image: UIImage) async -> VisionAnalysisResult {
        guard let cgImage = image.cgImage else {
            return VisionAnalysisResult(hasText: false, hasFaces: false, hasObjects: false)
        }

        let requestHandler = VNImageRequestHandler(cgImage: cgImage, options: [:])

        // Perform text detection
        let hasText = await detectTextInImage(image)

        // Perform face detection
        let hasFaces = await detectFacesInImage(cgImage, requestHandler: requestHandler)

        // Perform object detection
        let hasObjects = await detectObjectsInImage(cgImage, requestHandler: requestHandler)

        return VisionAnalysisResult(hasText: hasText, hasFaces: hasFaces, hasObjects: hasObjects)
    }

    /// Detects if image contains text
    func detectTextInImage(_ image: UIImage) async -> Bool {
        return await withCheckedContinuation { continuation in
            guard let cgImage = image.cgImage else {
                continuation.resume(returning: false)
                return
            }

            var hasResumed = false
            let requestHandler = VNImageRequestHandler(cgImage: cgImage, options: [:])
            let request = VNRecognizeTextRequest { request, error in
                guard !hasResumed else { return }
                hasResumed = true

                guard error == nil,
                      let observations = request.results as? [VNRecognizedTextObservation],
                      !observations.isEmpty else {
                    continuation.resume(returning: false)
                    return
                }

                continuation.resume(returning: true)
            }

            request.recognitionLevel = .fast

            do {
                try requestHandler.perform([request])
            } catch {
                guard !hasResumed else { return }
                hasResumed = true
                continuation.resume(returning: false)
            }
        }
    }

    /// Detects faces in an image
    func detectFacesInImage(_ cgImage: CGImage, requestHandler: VNImageRequestHandler) async -> Bool {
        return await withCheckedContinuation { continuation in
            var hasResumed = false

            let request = VNDetectFaceRectanglesRequest { request, error in
                guard !hasResumed else { return }
                hasResumed = true

                guard error == nil,
                      let observations = request.results as? [VNFaceObservation],
                      !observations.isEmpty else {
                    continuation.resume(returning: false)
                    return
                }

                continuation.resume(returning: true)
            }

            do {
                try requestHandler.perform([request])
            } catch {
                guard !hasResumed else { return }
                hasResumed = true
                continuation.resume(returning: false)
            }
        }
    }

    /// Detects objects in an image
    func detectObjectsInImage(_ cgImage: CGImage, requestHandler: VNImageRequestHandler) async -> Bool {
        return await withCheckedContinuation { continuation in
            var hasResumed = false

            let request = VNDetectRectanglesRequest { request, error in
                guard !hasResumed else { return }
                hasResumed = true

                guard error == nil,
                      let observations = request.results as? [VNRectangleObservation],
                      observations.count > 2 else { // Need more than 2 rectangles to consider as objects
                    continuation.resume(returning: false)
                    return
                }

                continuation.resume(returning: true)
            }

            do {
                try requestHandler.perform([request])
            } catch {
                guard !hasResumed else { return }
                hasResumed = true
                continuation.resume(returning: false)
            }
        }
    }
    
    /// Extracts dominant colors from image
    func extractDominantColors(from image: UIImage) async -> [String] {
        guard let cgImage = image.cgImage else {
            return ["unknown"]
        }

        // Sample colors from the image
        let colorSpace = CGColorSpaceCreateDeviceRGB()
        let width = 100
        let height = 100
        let bytesPerPixel = 4
        let bytesPerRow = bytesPerPixel * width
        let bitsPerComponent = 8

        var pixelData = [UInt8](repeating: 0, count: width * height * bytesPerPixel)

        guard let context = CGContext(
            data: &pixelData,
            width: width,
            height: height,
            bitsPerComponent: bitsPerComponent,
            bytesPerRow: bytesPerRow,
            space: colorSpace,
            bitmapInfo: CGImageAlphaInfo.noneSkipLast.rawValue
        ) else {
            return ["unknown"]
        }

        context.draw(cgImage, in: CGRect(x: 0, y: 0, width: width, height: height))

        // Analyze colors
        var colorCounts: [String: Int] = [:]

        for y in 0..<height {
            for x in 0..<width {
                let pixelIndex = (y * width + x) * bytesPerPixel
                let r = pixelData[pixelIndex]
                let g = pixelData[pixelIndex + 1]
                let b = pixelData[pixelIndex + 2]

                let colorName = categorizeColor(r: r, g: g, b: b)
                colorCounts[colorName, default: 0] += 1
            }
        }

        // Return top 3 colors
        let sortedColors = colorCounts.sorted { $0.value > $1.value }
        return Array(sortedColors.prefix(3).map { $0.key })
    }

    /// Categorizes RGB values into color names
    func categorizeColor(r: UInt8, g: UInt8, b: UInt8) -> String {
        let red = Double(r) / 255.0
        let green = Double(g) / 255.0
        let blue = Double(b) / 255.0

        // Simple color categorization
        if red > 0.8 && green < 0.3 && blue < 0.3 {
            return "red"
        } else if green > 0.8 && red < 0.3 && blue < 0.3 {
            return "green"
        } else if blue > 0.8 && red < 0.3 && green < 0.3 {
            return "blue"
        } else if red > 0.8 && green > 0.8 && blue < 0.3 {
            return "yellow"
        } else if red > 0.8 && green < 0.3 && blue > 0.8 {
            return "purple"
        } else if red < 0.3 && green > 0.8 && blue > 0.8 {
            return "cyan"
        } else if red > 0.7 && green > 0.7 && blue > 0.7 {
            return "white"
        } else if red < 0.3 && green < 0.3 && blue < 0.3 {
            return "black"
        } else if red > 0.4 && green > 0.4 && blue > 0.4 {
            return "gray"
        } else {
            return "mixed"
        }
    }

    /// Determines mood from colors and composition
    func determineMoodFromColors(_ colors: [String], aspectRatio: CGFloat) -> String {
        let warmColors = ["red", "yellow", "orange"]
        let coolColors = ["blue", "green", "cyan"]
        let neutralColors = ["gray", "white", "black"]

        let warmCount = colors.filter { warmColors.contains($0) }.count
        let coolCount = colors.filter { coolColors.contains($0) }.count
        let neutralCount = colors.filter { neutralColors.contains($0) }.count

        if warmCount > coolCount && warmCount > neutralCount {
            return aspectRatio > 1.5 ? "energetic" : "cozy"
        } else if coolCount > warmCount && coolCount > neutralCount {
            return aspectRatio > 1.5 ? "serene" : "calm"
        } else {
            return "balanced"
        }
    }
    
    /// Parses the AI analysis response
    func parseAnalysisResponse(_ jsonString: String) -> ImageRecognitionResult {
        print("🔍 Parsing AI analysis response...")
        print("📄 JSON string: \(jsonString)")

        guard let data = jsonString.data(using: .utf8) else {
            print("❌ Failed to convert JSON string to data")
            return createDefaultResult()
        }

        do {
            if let json = try JSONSerialization.jsonObject(with: data) as? [String: Any] {
                print("✅ Successfully parsed JSON object")
                print("📊 JSON keys: \(json.keys.sorted())")

                let contentTypeString = json["contentType"] as? String ?? "unknown"
                let contentType = ImageContentType(rawValue: contentTypeString) ?? .unknown
                let confidence = json["confidence"] as? Double ?? 0.5
                let description = json["description"] as? String ?? "Image content"
                let tags = json["tags"] as? [String] ?? []
                let colors = json["colors"] as? [String] ?? []
                let mood = json["mood"] as? String
                let isAppropriate = json["isAppropriate"] as? Bool ?? true

                print("📈 Parsed values:")
                print("   - Content Type: \(contentType.displayName)")
                print("   - Confidence: \(confidence)")
                print("   - Description: \(description)")
                print("   - Tags: \(tags)")
                print("   - Colors: \(colors)")
                print("   - Mood: \(mood ?? "nil")")
                print("   - Appropriate: \(isAppropriate)")

                let result = ImageRecognitionResult(
                    contentType: contentType,
                    confidence: confidence,
                    description: description,
                    tags: tags,
                    colors: colors,
                    mood: mood,
                    isAppropriate: isAppropriate
                )

                print("✅ Created ImageRecognitionResult successfully")
                return result
            } else {
                print("❌ Failed to cast JSON to dictionary")
            }
        } catch {
            print("❌ Error parsing analysis response: \(error)")
        }

        print("🔄 Returning default result due to parsing failure")
        return createDefaultResult()
    }
    
    /// Creates a default result when parsing fails
    func createDefaultResult() -> ImageRecognitionResult {
        print("⚠️ Creating default result due to analysis failure")
        let result = ImageRecognitionResult(
            contentType: .unknown,
            confidence: 0.3,
            description: "Unable to analyze image content",
            tags: [],
            colors: [],
            mood: nil,
            isAppropriate: true
        )
        print("📊 Default result: \(result.contentType.displayName) with confidence \(result.confidence)")
        return result
    }

    /// Generates a caption from analysis result
    func generateCaptionFromResult(_ result: ImageRecognitionResult) -> String {
        let emoji = result.contentType.emoji
        let type = result.contentType.displayName

        if result.confidence > 0.8 {
            return "\(emoji) Beautiful \(type.lowercased()): \(result.description)"
        } else if result.confidence > 0.6 {
            return "\(emoji) \(result.description)"
        } else {
            return "\(emoji) Sharing a beautiful moment"
        }
    }

    /// Calculates similarity between two analysis results
    func calculateSimilarity(between result1: ImageRecognitionResult, and result2: ImageRecognitionResult) -> Double {
        var similarity: Double = 0.0

        // Content type similarity (40% weight)
        if result1.contentType == result2.contentType {
            similarity += 0.4
        }

        // Tags similarity (30% weight)
        let commonTags = Set(result1.tags).intersection(Set(result2.tags))
        let totalTags = Set(result1.tags).union(Set(result2.tags))
        if !totalTags.isEmpty {
            similarity += 0.3 * (Double(commonTags.count) / Double(totalTags.count))
        }

        // Colors similarity (20% weight)
        let commonColors = Set(result1.colors).intersection(Set(result2.colors))
        let totalColors = Set(result1.colors).union(Set(result2.colors))
        if !totalColors.isEmpty {
            similarity += 0.2 * (Double(commonColors.count) / Double(totalColors.count))
        }

        // Mood similarity (10% weight)
        if let mood1 = result1.mood, let mood2 = result2.mood, mood1 == mood2 {
            similarity += 0.1
        }

        return min(similarity, 1.0)
    }
}
