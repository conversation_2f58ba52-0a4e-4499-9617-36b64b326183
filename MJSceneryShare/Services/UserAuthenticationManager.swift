import Foundation
import AuthenticationServices

// MARK: - Login Type Enum
enum LoginType: String, Codable {
    case guest = "guest"
    case apple = "apple"
}

// MARK: - Stored User Data
struct StoredUserData: Codable {
    let loginType: LoginType
    let user: User
    let loginDate: Date
    let isActive: Bool
    
    init(loginType: LoginType, user: User, isActive: Bool = true) {
        self.loginType = loginType
        self.user = user
        self.loginDate = Date()
        self.isActive = isActive
    }
}

// MARK: - User Authentication Manager
class UserAuthenticationManager {
    static let shared = UserAuthenticationManager()
    
    // MARK: - Properties
    private let userDefaults = UserDefaults.standard
    private let currentUserKey = "CurrentUserData"
    private let loginStateKey = "UserLoginState"
    private let guestUserHistoryKey = "GuestUserHistory"
    private let deviceGuestUserKey = "DeviceGuestUser" // 设备级游客用户
    private let appleUsersKey = "AppleUsers" // 存储所有Apple用户数据
    
    private init() {}
    
    // MARK: - Public Methods
    
    /// Load saved user state on app launch
    func loadSavedUserState() -> User? {
        guard let data = userDefaults.data(forKey: currentUserKey),
              let storedData = try? JSONDecoder().decode(StoredUserData.self, from: data),
              storedData.isActive else {
            return nil
        }
        
        // Validate Apple login state if needed
        if storedData.loginType == .apple {
            return validateAppleLoginState(storedData: storedData)
        }
        
        // For guest users, always return the stored user
        return storedData.user
    }
    
    /// Save user login state after successful authentication
    func saveUserLoginState(user: User, loginType: LoginType) {
        let storedData = StoredUserData(loginType: loginType, user: user)
        
        if let encoded = try? JSONEncoder().encode(storedData) {
            userDefaults.set(encoded, forKey: currentUserKey)
            userDefaults.set(true, forKey: loginStateKey)
            
            // For guest users, also save to history
            if loginType == .guest {
                saveGuestUserToHistory(user: user)
            }
        }
    }
    
    /// Clear login state (logout)
    func clearLoginState() {
        // Mark current user as inactive instead of deleting
        if let data = userDefaults.data(forKey: currentUserKey),
           var storedData = try? JSONDecoder().decode(StoredUserData.self, from: data) {
            storedData = StoredUserData(
                loginType: storedData.loginType,
                user: storedData.user,
                isActive: false
            )
            
            if let encoded = try? JSONEncoder().encode(storedData) {
                userDefaults.set(encoded, forKey: currentUserKey)
            }
        }
        
        userDefaults.set(false, forKey: loginStateKey)
    }
    
    /// Delete user account and all associated data
    func deleteUserAccount(user: User, loginType: LoginType) {
        print("🗑️ [UserAuth] Starting account deletion for user: \(user.id) (\(user.nickname))")

        // Clear current user data
        userDefaults.removeObject(forKey: currentUserKey)
        userDefaults.removeObject(forKey: loginStateKey)
        print("🗑️ [UserAuth] Cleared authentication data")

        // Clear user-specific data based on login type
        switch loginType {
        case .guest:
            deleteGuestUserData(userId: user.id)
        case .apple:
            deleteAppleUserData(userId: user.id)
        }

        // Clear coin transactions
        CoinTransactionManager.shared.clearUserData(for: user.id)
        print("🗑️ [UserAuth] Cleared coin transaction data")

        // Clear posts and images
        if let postStorageManager = try? PostStorageManager.shared {
            postStorageManager.clearAllData()
            print("🗑️ [UserAuth] Cleared post storage data")
        }

        // Clear user's content from Discover feed
        clearUserContentFromDiscoverFeed(userId: user.id)

        // Clear chat data for this user
        clearUserChatData(userId: user.id)

        // Clear friends data for this user
        clearUserFriendsData(userId: user.id)

        print("🗑️ [UserAuth] Account deletion completed for user: \(user.id)")
    }
    
    /// Check if user is currently logged in
    func isUserLoggedIn() -> Bool {
        return userDefaults.bool(forKey: loginStateKey)
    }
    
    /// Get current login type
    func getCurrentLoginType() -> LoginType? {
        guard let data = userDefaults.data(forKey: currentUserKey),
              let storedData = try? JSONDecoder().decode(StoredUserData.self, from: data),
              storedData.isActive else {
            return nil
        }
        return storedData.loginType
    }

    /// Get current user
    func getCurrentUser() -> User? {
        guard let data = userDefaults.data(forKey: currentUserKey),
              let storedData = try? JSONDecoder().decode(StoredUserData.self, from: data),
              storedData.isActive else {
            return nil
        }
        return storedData.user
    }
    
    // MARK: - Private Methods
    
    private func validateAppleLoginState(storedData: StoredUserData) -> User? {
        // Check if Apple ID is still valid
        let provider = ASAuthorizationAppleIDProvider()
        let request = provider.createRequest()
        
        // For now, we'll trust the stored state
        // In a production app, you might want to validate the Apple ID credential
        return storedData.user
    }
    
    private func saveGuestUserToHistory(user: User) {
        var guestHistory = loadGuestUserHistory()
        
        // Remove any existing entry for this user ID
        guestHistory.removeAll { $0.id == user.id }
        
        // Add current user to history
        guestHistory.append(user)
        
        // Keep only the last 5 guest users
        if guestHistory.count > 5 {
            guestHistory = Array(guestHistory.suffix(5))
        }
        
        if let encoded = try? JSONEncoder().encode(guestHistory) {
            userDefaults.set(encoded, forKey: guestUserHistoryKey)
        }
    }
    
    private func loadGuestUserHistory() -> [User] {
        guard let data = userDefaults.data(forKey: guestUserHistoryKey),
              let history = try? JSONDecoder().decode([User].self, from: data) else {
            return []
        }
        return history
    }
    
    private func deleteGuestUserData(userId: String) {
        // Remove from guest user history
        var guestHistory = loadGuestUserHistory()
        guestHistory.removeAll { $0.id == userId }

        if let encoded = try? JSONEncoder().encode(guestHistory) {
            userDefaults.set(encoded, forKey: guestUserHistoryKey)
        }

        // Clear device guest user data to force new account creation on next guest login
        userDefaults.removeObject(forKey: deviceGuestUserKey)
        print("🗑️ [UserAuth] Cleared device guest user data - next guest login will create new account")
    }
    
    private func deleteAppleUserData(userId: String) {
        // 删除特定Apple用户的数据
        deleteSpecificAppleUser(appleUserID: userId)
        print("🗑️ [UserAuth] Deleted Apple user data for ID: \(userId)")
    }

    /// Clear user's content from Discover feed
    private func clearUserContentFromDiscoverFeed(userId: String) {
        // Load current shares data
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601

        // Clear from allShares
        if let savedData = userDefaults.data(forKey: "allShares"),
           var allShares = try? decoder.decode([Share].self, from: savedData) {
            // Remove shares created by the deleted user
            allShares.removeAll { $0.author.id == userId }

            // Save updated data
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            if let encoded = try? encoder.encode(allShares) {
                userDefaults.set(encoded, forKey: "allShares")
            }
        }

        // Clear from userShares
        if let savedData = userDefaults.data(forKey: "userShares"),
           var userShares = try? decoder.decode([Share].self, from: savedData) {
            // Remove shares created by the deleted user
            userShares.removeAll { $0.author.id == userId }

            // Save updated data
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            if let encoded = try? encoder.encode(userShares) {
                userDefaults.set(encoded, forKey: "userShares")
            }
        }

        // Notify AppViewModel to refresh the data
        DispatchQueue.main.async {
            AppViewModel.shared.loadData()
        }

        print("🗑️ [UserAuth] Cleared user content from Discover feed for user: \(userId)")
    }

    /// Clear chat data for the deleted user
    private func clearUserChatData(userId: String) {
        // Clear chat messages involving this user
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601

        if let savedData = userDefaults.data(forKey: "chatMessages"),
           var messages = try? decoder.decode([ChatMessage].self, from: savedData) {
            // Remove messages sent by or to the deleted user
            messages.removeAll { $0.senderId == userId || $0.receiverId == userId }

            // Save updated messages
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            if let encoded = try? encoder.encode(messages) {
                userDefaults.set(encoded, forKey: "chatMessages")
            }
        }

        // Clear conversations involving this user
        if let savedData = userDefaults.data(forKey: "conversations"),
           var conversations = try? decoder.decode([Conversation].self, from: savedData) {
            // Remove conversations involving the deleted user
            conversations.removeAll { conversation in
                conversation.participants.contains(userId)
            }

            // Save updated conversations
            let encoder = JSONEncoder()
            encoder.dateEncodingStrategy = .iso8601
            if let encoded = try? encoder.encode(conversations) {
                userDefaults.set(encoded, forKey: "conversations")
            }
        }

        print("🗑️ [UserAuth] Cleared chat data for user: \(userId)")
    }

    /// Clear friends data for the deleted user
    private func clearUserFriendsData(userId: String) {
        // Remove the deleted user from friends list
        FriendsManager.shared.removeFriend(userId: userId)

        // Clear friend activity data for this user
        FriendActivityManager.shared.clearActivity(for: userId)

        print("🗑️ [UserAuth] Cleared friends data for user: \(userId)")
    }

    // MARK: - User Data Management

    /// Update user data in persistent storage
    func updateUserData(_ user: User) {
        guard let data = userDefaults.data(forKey: currentUserKey),
              var storedData = try? JSONDecoder().decode(StoredUserData.self, from: data),
              storedData.isActive else {
            return
        }

        // Update the user data while preserving login type and state
        storedData = StoredUserData(
            loginType: storedData.loginType,
            user: user,
            isActive: storedData.isActive
        )

        if let encoded = try? JSONEncoder().encode(storedData) {
            userDefaults.set(encoded, forKey: currentUserKey)
        }

        // If this is a guest user, also update the device guest user
        if storedData.loginType == .guest {
            updateDeviceGuestUser(user)
        }
    }

    /// Get or create device guest user (persistent for this device)
    func getOrCreateDeviceGuestUser() -> User {
        // Try to load existing device guest user
        if let existingGuestUser = loadDeviceGuestUser() {
            print("🔄 [UserAuth] Using existing device guest user: \(existingGuestUser.id)")
            return existingGuestUser
        }

        // Create new device guest user
        let userId = "guest_\(UUID().uuidString)"
        let randomAvatar = AvatarManager.shared.getRandomAvatarName()

        let guestUser = User(
            id: userId,
            nickname: "Guest User",
            avatar: randomAvatar,
            coins: CoinSystemConfig.newUserStartingCoins,
            isLoggedIn: true
        )

        // Save as device guest user
        saveDeviceGuestUser(guestUser)
        print("✨ [UserAuth] Created new device guest user: \(guestUser.id)")

        return guestUser
    }

    /// Generate new guest user ID (for legacy compatibility)
    func generateGuestUser() -> User {
        return getOrCreateDeviceGuestUser()
    }

    /// Create or retrieve existing Apple user from credentials
    func createAppleUser(from credential: ASAuthorizationAppleIDCredential) -> User {
        let userID = credential.user

        // 首先检查是否已存在该Apple User ID的用户
        if let existingUser = getExistingAppleUser(appleUserID: userID) {
            print("🔄 [UserAuth] Found existing Apple user: \(existingUser.id) (\(existingUser.nickname))")

            // 更新现有用户的登录时间和token（如果有新的）
            var updatedUser = existingUser
            updatedUser.updateLastLogin()

            // 如果有新的identity token，更新它
            if let identityToken = credential.identityToken,
               let tokenString = String(data: identityToken, encoding: .utf8) {
                updatedUser.appleIdentityToken = tokenString
            }

            // 保存更新后的用户数据
            saveAppleUser(updatedUser)

            return updatedUser
        }

        // 如果不存在，创建新用户
        let email = credential.email
        let fullName = credential.fullName
        let identityToken = credential.identityToken != nil ? String(data: credential.identityToken!, encoding: .utf8) : nil

        let displayName = [fullName?.givenName, fullName?.familyName]
            .compactMap { $0 }
            .joined(separator: " ")

        let finalName = displayName.isEmpty ? "Apple User" : displayName
        let fullNameString = displayName.isEmpty ? nil : displayName
        let randomAvatar = AvatarManager.shared.getRandomAvatarName()

        print("🔐 Creating new Apple user with:")
        print("   - User ID: \(userID)")
        print("   - Email: \(email ?? "nil")")
        print("   - Full Name: \(fullNameString ?? "nil")")
        print("   - Identity Token: \(identityToken != nil ? "Available" : "nil")")

        let newUser = User(
            appleUserID: userID,
            nickname: finalName,
            email: email,
            fullName: fullNameString,
            avatar: randomAvatar,
            coins: CoinSystemConfig.newUserStartingCoins,
            identityToken: identityToken
        )

        // 保存新的Apple用户
        saveAppleUser(newUser)

        return newUser
    }

    /// Get user login history for debugging/analytics
    func getUserLoginHistory() -> [StoredUserData] {
        // This could be expanded to store login history
        // For now, just return current user if available
        guard let data = userDefaults.data(forKey: currentUserKey),
              let storedData = try? JSONDecoder().decode(StoredUserData.self, from: data) else {
            return []
        }
        return [storedData]
    }

    /// Clear all authentication data (for debugging or complete reset)
    func clearAllAuthenticationData() {
        userDefaults.removeObject(forKey: currentUserKey)
        userDefaults.removeObject(forKey: loginStateKey)
        userDefaults.removeObject(forKey: guestUserHistoryKey)
        userDefaults.removeObject(forKey: deviceGuestUserKey)
        userDefaults.removeObject(forKey: appleUsersKey)
        print("🗑️ [UserAuth] Cleared all authentication data including Apple users")
    }

    // MARK: - Device Guest User Management

    /// Save device guest user (persistent for this device)
    private func saveDeviceGuestUser(_ user: User) {
        if let encoded = try? JSONEncoder().encode(user) {
            userDefaults.set(encoded, forKey: deviceGuestUserKey)
            print("💾 [UserAuth] Saved device guest user: \(user.id)")
        }
    }

    /// Load device guest user
    private func loadDeviceGuestUser() -> User? {
        guard let data = userDefaults.data(forKey: deviceGuestUserKey),
              let user = try? JSONDecoder().decode(User.self, from: data) else {
            return nil
        }
        return user
    }

    /// Update device guest user data
    func updateDeviceGuestUser(_ user: User) {
        saveDeviceGuestUser(user)
    }

    /// Check if device has a guest user
    func hasDeviceGuestUser() -> Bool {
        return loadDeviceGuestUser() != nil
    }

    // MARK: - Apple User Management

    /// Save Apple user data for future logins
    private func saveAppleUser(_ user: User) {
        var appleUsers = loadAppleUsers()

        // 移除已存在的相同用户ID的记录（如果有）
        appleUsers.removeAll { $0.appleUserID == user.appleUserID }

        // 添加新的或更新的用户数据
        appleUsers.append(user)

        // 保存到UserDefaults
        if let encoded = try? JSONEncoder().encode(appleUsers) {
            userDefaults.set(encoded, forKey: appleUsersKey)
            print("💾 [UserAuth] Saved Apple user: \(user.id) (\(user.nickname))")
        }
    }

    /// Load all saved Apple users
    private func loadAppleUsers() -> [User] {
        guard let data = userDefaults.data(forKey: appleUsersKey),
              let users = try? JSONDecoder().decode([User].self, from: data) else {
            return []
        }
        return users
    }

    /// Get existing Apple user by Apple User ID
    private func getExistingAppleUser(appleUserID: String) -> User? {
        let appleUsers = loadAppleUsers()
        return appleUsers.first { $0.appleUserID == appleUserID }
    }

    /// Get all Apple users (for debugging or user management)
    func getAllAppleUsers() -> [User] {
        return loadAppleUsers()
    }

    /// Delete specific Apple user data
    private func deleteSpecificAppleUser(appleUserID: String) {
        var appleUsers = loadAppleUsers()
        appleUsers.removeAll { $0.appleUserID == appleUserID }

        if let encoded = try? JSONEncoder().encode(appleUsers) {
            userDefaults.set(encoded, forKey: appleUsersKey)
            print("🗑️ [UserAuth] Deleted Apple user data for ID: \(appleUserID)")
        }
    }

    /// Clear all Apple user data
    func clearAllAppleUsers() {
        userDefaults.removeObject(forKey: appleUsersKey)
        print("🗑️ [UserAuth] Cleared all Apple user data")
    }
}
