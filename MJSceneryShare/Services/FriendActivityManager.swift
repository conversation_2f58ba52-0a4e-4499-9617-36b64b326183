import Foundation

// MARK: - Friend Activity Manager
class FriendActivityManager {
    static let shared = FriendActivityManager()
    
    private let userDefaults = UserDefaults.standard
    private let activityKey = "friend_activity_data"
    private let recentActivityThreshold: TimeInterval = 24 * 60 * 60 // 24 hours
    
    private init() {}
    
    // MARK: - Activity Tracking
    
    /// Record message activity with a friend
    func recordMessageActivity(with friendUserId: String) {
        var activities = loadActivities()
        
        // Update or add activity record
        let activityRecord = FriendActivity(
            friendUserId: friendUserId,
            lastMessageDate: Date(),
            messageCount: (activities[friendUserId]?.messageCount ?? 0) + 1
        )
        
        activities[friendUserId] = activityRecord
        saveActivities(activities)
        
        // Post notification for UI updates
        NotificationCenter.default.post(
            name: .friendActivityDidUpdate,
            object: nil,
            userInfo: ["friendUserId": friendUserId]
        )
    }
    
    /// Check if a friend has recent message activity
    func hasRecentActivity(with friendUserId: String) -> Bool {
        let activities = loadActivities()
        
        guard let activity = activities[friendUserId] else {
            return false
        }
        
        let timeSinceLastMessage = Date().timeIntervalSince(activity.lastMessageDate)
        return timeSinceLastMessage <= recentActivityThreshold
    }
    
    /// Get the last message date with a friend
    func getLastMessageDate(with friendUserId: String) -> Date? {
        let activities = loadActivities()
        return activities[friendUserId]?.lastMessageDate
    }
    
    /// Get message count with a friend
    func getMessageCount(with friendUserId: String) -> Int {
        let activities = loadActivities()
        return activities[friendUserId]?.messageCount ?? 0
    }
    
    /// Get all friends with recent activity, sorted by last message date
    func getRecentlyActiveFriends() -> [String] {
        let activities = loadActivities()
        let currentTime = Date()
        
        return activities
            .filter { _, activity in
                currentTime.timeIntervalSince(activity.lastMessageDate) <= recentActivityThreshold
            }
            .sorted { $0.value.lastMessageDate > $1.value.lastMessageDate }
            .map { $0.key }
    }
    
    /// Clear activity data for a specific friend
    func clearActivity(for friendUserId: String) {
        var activities = loadActivities()
        activities.removeValue(forKey: friendUserId)
        saveActivities(activities)
    }
    
    /// Clear all activity data
    func clearAllActivities() {
        userDefaults.removeObject(forKey: activityKey)
    }
    
    // MARK: - Data Persistence
    
    private func loadActivities() -> [String: FriendActivity] {
        guard let data = userDefaults.data(forKey: activityKey),
              let activities = try? JSONDecoder().decode([String: FriendActivity].self, from: data) else {
            return [:]
        }
        return activities
    }
    
    private func saveActivities(_ activities: [String: FriendActivity]) {
        if let data = try? JSONEncoder().encode(activities) {
            userDefaults.set(data, forKey: activityKey)
        }
    }
    
    // MARK: - Statistics
    
    /// Get activity statistics for debugging/analytics
    func getActivityStatistics() -> FriendActivityStatistics {
        let activities = loadActivities()
        let currentTime = Date()
        
        let recentlyActiveCount = activities.values.filter { activity in
            currentTime.timeIntervalSince(activity.lastMessageDate) <= recentActivityThreshold
        }.count
        
        let totalMessageCount = activities.values.reduce(0) { $0 + $1.messageCount }
        
        let mostActiveFriend = activities.max { a, b in
            a.value.messageCount < b.value.messageCount
        }
        
        return FriendActivityStatistics(
            totalFriendsWithActivity: activities.count,
            recentlyActiveFriendsCount: recentlyActiveCount,
            totalMessageCount: totalMessageCount,
            mostActiveFriendId: mostActiveFriend?.key,
            mostActiveFriendMessageCount: mostActiveFriend?.value.messageCount ?? 0
        )
    }
}

// MARK: - Friend Activity Model
struct FriendActivity: Codable {
    let friendUserId: String
    let lastMessageDate: Date
    let messageCount: Int
}

// MARK: - Friend Activity Statistics
struct FriendActivityStatistics {
    let totalFriendsWithActivity: Int
    let recentlyActiveFriendsCount: Int
    let totalMessageCount: Int
    let mostActiveFriendId: String?
    let mostActiveFriendMessageCount: Int
}

// MARK: - Notification Names
extension Notification.Name {
    static let friendActivityDidUpdate = Notification.Name("friendActivityDidUpdate")
}
