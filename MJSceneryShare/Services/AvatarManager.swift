import Foundation
import UIKit

// MARK: - Avatar Manager
class AvatarManager {
    static let shared = AvatarManager()
    
    // Current available avatar range (1-25, will expand to 100)
    private let currentAvatarRange = 1...25
    private let futureAvatarRange = 1...100
    
    private init() {}
    
    // MARK: - Public Methods
    
    /// Get a random avatar name from available avatars
    func getRandomAvatarName() -> String {
        let randomNumber = Int.random(in: currentAvatarRange)
        return "avatar_\(randomNumber)"
    }
    
    /// Get avatar image by name
    func getAvatarImage(named avatarName: String) -> UIImage? {
        return UIImage(named: avatarName)
    }
    
    /// Get avatar image by name with fallback to default
    func getAvatarImageWithFallback(named avatarName: String) -> UIImage {
        if let image = UIImage(named: avatarName) {
            return image
        }
        
        // Fallback to a default avatar if the specified one doesn't exist
        if let defaultImage = UIImage(named: "avatar_1") {
            return defaultImage
        }
        
        // Ultimate fallback to system image
        return UIImage(systemName: "person.circle.fill") ?? UIImage()
    }
    
    /// Check if an avatar name is valid
    func isValidAvatarName(_ avatarName: String) -> Bool {
        // Check if it follows the avatar_xx pattern
        let pattern = "^avatar_([1-9]|[1-9][0-9]|100)$"
        let regex = try? NSRegularExpression(pattern: pattern)
        let range = NSRange(location: 0, length: avatarName.utf16.count)
        return regex?.firstMatch(in: avatarName, options: [], range: range) != nil
    }
    
    /// Get all available avatar names
    func getAllAvailableAvatarNames() -> [String] {
        return currentAvatarRange.map { "avatar_\($0)" }
    }
    
    /// Get avatar number from avatar name
    func getAvatarNumber(from avatarName: String) -> Int? {
        if isValidAvatarName(avatarName) {
            let numberString = avatarName.replacingOccurrences(of: "avatar_", with: "")
            return Int(numberString)
        }
        return nil
    }
    
    /// Migrate old system avatar to new local avatar
    func migrateSystemAvatarToLocal(_ systemAvatar: String) -> String {
        // If it's already a local avatar, return as is
        if isValidAvatarName(systemAvatar) {
            return systemAvatar
        }
        
        // If it's a system avatar (like "person.circle.fill"), assign a random local avatar
        return getRandomAvatarName()
    }
    
    /// Get multiple random avatars (useful for generating real data)
    func getRandomAvatarNames(count: Int) -> [String] {
        var avatars: [String] = []
        for _ in 0..<count {
            avatars.append(getRandomAvatarName())
        }
        return avatars
    }

    /// Get a consistent avatar for a specific user ID (to avoid same user having different avatars)
    func getConsistentAvatarName(for userId: String) -> String {
        // Use hash of userId to get consistent avatar
        let hash = abs(userId.hashValue)
        let avatarNumber = (hash % currentAvatarRange.count) + currentAvatarRange.lowerBound
        return "avatar_\(avatarNumber)"
    }

    /// Get multiple unique avatars for different users (no duplicates)
    func getUniqueAvatarNames(count: Int) -> [String] {
        let availableAvatars = getAllAvailableAvatarNames()
        let shuffled = availableAvatars.shuffled()
        return Array(shuffled.prefix(min(count, availableAvatars.count)))
    }
    
    /// Get avatar image with specific size configuration
    func getAvatarImage(named avatarName: String, size: CGSize) -> UIImage? {
        guard let originalImage = getAvatarImage(named: avatarName) else {
            return nil
        }
        
        // Resize image to specified size
        let renderer = UIGraphicsImageRenderer(size: size)
        return renderer.image { _ in
            originalImage.draw(in: CGRect(origin: .zero, size: size))
        }
    }
    
    // MARK: - Avatar Size Configurations
    
    /// Standard avatar sizes used throughout the app
    enum AvatarSize {
        case small      // 32x32 - for small lists
        case medium     // 50x50 - for normal lists
        case large      // 80x80 - for profile headers
        case extraLarge // 120x120 - for detailed profiles
        
        var size: CGSize {
            switch self {
            case .small:
                return CGSize(width: 32, height: 32)
            case .medium:
                return CGSize(width: 50, height: 50)
            case .large:
                return CGSize(width: 80, height: 80)
            case .extraLarge:
                return CGSize(width: 120, height: 120)
            }
        }
        
        var cornerRadius: CGFloat {
            return size.width / 2
        }
    }
    
    /// Get avatar image with predefined size
    func getAvatarImage(named avatarName: String, avatarSize: AvatarSize) -> UIImage? {
        return getAvatarImage(named: avatarName, size: avatarSize.size)
    }
    
    // MARK: - Batch Operations
    
    /// Update user avatar to random local avatar
    func assignRandomAvatarToUser(_ user: inout User) {
        user.avatar = getRandomAvatarName()
    }
    
    /// Update friend avatar to random local avatar
    func assignRandomAvatarToFriend(_ friend: inout Friend) -> Friend {
        return Friend(
            id: friend.id,
            userId: friend.userId,
            username: friend.username,
            displayName: friend.displayName,
            avatar: getRandomAvatarName(),
            bio: friend.bio,
            location: friend.location,
            status: friend.status,
            lastSeen: friend.lastSeen,
            mutualFriends: friend.mutualFriends,
            friendshipDate: friend.friendshipDate,
            isBlocked: friend.isBlocked
        )
    }
}

// MARK: - UIImageView Extension for Avatar Loading
extension UIImageView {
    /// Load avatar with fallback and proper styling
    func loadAvatar(named avatarName: String, size: AvatarManager.AvatarSize = .medium) {
        let avatarManager = AvatarManager.shared
        
        // Load the avatar image
        self.image = avatarManager.getAvatarImageWithFallback(named: avatarName)
        
        // Apply styling
        self.contentMode = .scaleAspectFill
        self.clipsToBounds = true
        self.layer.cornerRadius = size.cornerRadius
        
        // Set size constraints if needed
        self.snp.remakeConstraints { make in
            make.width.height.equalTo(size.size.width)
        }
    }
    
    /// Load avatar with custom size
    func loadAvatar(named avatarName: String, customSize: CGSize) {
        let avatarManager = AvatarManager.shared
        
        // Load the avatar image
        self.image = avatarManager.getAvatarImageWithFallback(named: avatarName)
        
        // Apply styling
        self.contentMode = .scaleAspectFill
        self.clipsToBounds = true
        self.layer.cornerRadius = customSize.width / 2
        
        // Set size constraints
        self.snp.remakeConstraints { make in
            make.width.height.equalTo(customSize.width)
        }
    }
}
