import Foundation

class ChatManager {
    static let shared = ChatManager()
    
    private init() {}
    
    // MARK: - Message Sending
    
    /// Send a message to a friend
    func sendMessage(
        text: String,
        to friend: Friend,
        from currentUser: User,
        type: MessageType = .text,
        metadata: [String: String]? = nil
    ) {
        let conversationId = "conv_\(friend.userId)"
        
        let message = ChatMessage(
            id: "msg_\(UUID().uuidString)",
            conversationId: conversationId,
            senderId: currentUser.id,
            receiverId: friend.userId,
            content: text,
            type: type,
            timestamp: Date(),
            isRead: false,
            isDelivered: true,
            reactions: [],
            replyToMessageId: nil
        )
        
        // Save message to storage
        ChatStorageManager.shared.saveMessage(message)
        
        // Add friend to friends list if not already added
        FriendsManager.shared.addFriend(friend)
        
        print("💬 [ChatManager] Sent \(type.displayName) message to \(friend.displayName): \(text)")
    }
    
    /// Send a shared content message to a friend
    func sendSharedContent(
        share: Share,
        to friend: Friend,
        from currentUser: User
    ) {
        let messageText = "📸 Shared: \(share.title)\n\n\(share.description)"
        
        sendMessage(
            text: messageText,
            to: friend,
            from: currentUser,
            type: .sharedContent,
            metadata: [
                "shared_content_id": share.id,
                "shared_content_title": share.title,
                "shared_content_image": share.imageURL
            ]
        )
    }
    
    /// Send a photo message to a friend
    func sendPhoto(
        imageName: String,
        caption: String?,
        to friend: Friend,
        from currentUser: User
    ) {
        let messageText = caption ?? "📷 Photo"
        
        sendMessage(
            text: messageText,
            to: friend,
            from: currentUser,
            type: .photo,
            metadata: [
                "image_name": imageName,
                "caption": caption ?? ""
            ]
        )
    }
    
    // MARK: - Conversation Management
    
    /// Get or create conversation between current user and friend
    func getOrCreateConversation(with friend: Friend) -> String {
        let conversationId = "conv_\(friend.userId)"
        
        // Check if conversation already exists
        if let _ = ChatStorageManager.shared.getConversation(by: conversationId) {
            return conversationId
        }
        
        // Create new conversation
        let conversation = Conversation(
            id: conversationId,
            participants: [AppViewModel.shared.currentUser.id, friend.userId],
            lastMessage: nil,
            lastActivity: Date(),
            unreadCount: 0,
            createdDate: Date(),
            isArchived: false,
            isMuted: false
        )
        
        ChatStorageManager.shared.saveConversation(conversation)
        
        print("💬 [ChatManager] Created new conversation: \(conversationId)")
        return conversationId
    }
    
    /// Get messages for a conversation
    func getMessages(for conversationId: String) -> [ChatMessage] {
        return ChatStorageManager.shared.loadMessages(for: conversationId)
    }
    
    /// Get all conversations for current user
    func getAllConversations() -> [Conversation] {
        let allConversations = ChatStorageManager.shared.loadAllConversations()
        let currentUserId = AppViewModel.shared.currentUser.id
        
        // Filter conversations that include current user
        return allConversations.filter { conversation in
            conversation.participants.contains(currentUserId)
        }.sorted { $0.lastActivity > $1.lastActivity }
    }
    
    // MARK: - Utility Methods
    
    /// Mark messages as read in a conversation
    func markMessagesAsRead(in conversationId: String) {
        let messages = getMessages(for: conversationId)
        let currentUserId = AppViewModel.shared.currentUser.id
        
        for var message in messages {
            if message.receiverId == currentUserId && !message.isRead {
                message.isRead = true
                ChatStorageManager.shared.saveMessage(message)
            }
        }
        
        print("💬 [ChatManager] Marked messages as read in conversation: \(conversationId)")
    }
    
    /// Delete conversation and all its messages
    func deleteConversation(_ conversationId: String) {
        ChatStorageManager.shared.deleteMessages(for: conversationId)
        ChatStorageManager.shared.deleteConversationInfo(for: conversationId)
        
        print("💬 [ChatManager] Deleted conversation: \(conversationId)")
    }
    
    /// Clear all chat data (for logout)
    func clearAllChatData() {
        ChatStorageManager.shared.clearAllChatData()
        
        print("💬 [ChatManager] Cleared all chat data")
    }
}
