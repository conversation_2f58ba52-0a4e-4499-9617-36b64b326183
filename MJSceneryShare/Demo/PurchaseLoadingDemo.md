# 购买按钮Loading状态功能演示

## 功能概述

为购买页面添加了完整的loading状态管理，提升用户体验，防止重复点击和提供清晰的反馈。

## 新增功能

### 1. 全屏Loading遮罩
- **半透明黑色遮罩**：覆盖整个屏幕，防止用户操作其他元素
- **居中Loading容器**：包含活动指示器和文字说明
- **优雅的动画效果**：淡入淡出动画，提升视觉体验

### 2. 购买按钮状态管理
- **防重复点击**：Loading期间禁用所有购买按钮
- **按钮文字变化**：显示"Processing..."状态
- **内置活动指示器**：使用系统的`showsActivityIndicator`
- **透明度变化**：禁用时降低透明度至60%

### 3. 每日奖励按钮Loading
- **独立Loading状态**：不影响购买按钮的Loading
- **文字动态更新**：显示"🔄 Claiming Bonus..."
- **模拟延迟**：添加0.5秒延迟提升用户体验
- **状态恢复**：完成后恢复原始文字

## 实现细节

### Loading遮罩组件
```swift
private func showLoadingOverlay() {
    // 创建半透明遮罩
    let overlay = UIView()
    overlay.backgroundColor = UIColor.black.withAlphaComponent(0.5)

    // 创建Loading容器 - 自适应宽度
    let container = UIView()
    container.backgroundColor = UIColor.black.withAlphaComponent(0.8)
    container.layer.cornerRadius = 16

    // 添加活动指示器和文字
    let indicator = UIActivityIndicatorView(style: .large)
    let label = UILabel()
    label.text = "Processing..." // 简化文案
    label.numberOfLines = 0 // 支持多行显示

    // 容器约束 - 自适应宽度
    container.widthAnchor.constraint(greaterThanOrEqualToConstant: 200), // 最小宽度
    container.widthAnchor.constraint(lessThanOrEqualToConstant: 280), // 最大宽度

    // 淡入动画
    UIView.animate(withDuration: 0.3) {
        overlay.alpha = 1
    }
}
```

### 按钮状态管理
```swift
private func updatePurchaseButtonsState(enabled: Bool) {
    // 遍历所有购买按钮
    for purchaseButton in allPurchaseButtons {
        purchaseButton.isEnabled = enabled
        purchaseButton.alpha = enabled ? 1.0 : 0.6
        
        // 更新按钮文字和指示器
        var config = purchaseButton.configuration
        config?.title = enabled ? "Purchase Now" : "Processing..."
        config?.showsActivityIndicator = !enabled
        purchaseButton.configuration = config
    }
}
```

### 每日奖励Loading
```swift
private func showDailyBonusLoading(_ show: Bool) {
    dailyBonusButton.isEnabled = !show

    var config = dailyBonusButton.configuration
    if show {
        config?.title = "🔄 Claiming..." // 简化文案
        config?.showsActivityIndicator = true
    } else {
        config?.title = originalTitle // 恢复原始文字
        config?.showsActivityIndicator = false
    }
    dailyBonusButton.configuration = config
}
```

## 用户体验改进

### 1. 视觉反馈
- **即时响应**：点击按钮后立即显示Loading状态
- **清晰指示**：用户明确知道操作正在进行
- **专业外观**：现代化的Loading设计

### 2. 交互保护
- **防重复操作**：Loading期间禁用所有相关按钮
- **全屏保护**：遮罩防止用户点击其他区域
- **状态一致性**：所有UI元素状态同步更新

### 3. 性能优化
- **轻量级实现**：使用系统原生组件
- **内存管理**：及时清理Loading视图
- **动画流畅**：使用Core Animation确保流畅性

## 使用场景

### 购买流程
1. **用户点击购买按钮**
   - 立即显示Loading遮罩
   - 按钮变为"Processing..."状态
   - 禁用所有交互

2. **处理购买请求**
   - 与App Store通信
   - 验证购买结果
   - 更新用户金币

3. **完成购买**
   - 隐藏Loading遮罩
   - 恢复按钮状态
   - 显示成功/失败提示

### 每日奖励流程
1. **用户点击奖励按钮**
   - 按钮显示"🔄 Claiming Bonus..."
   - 启用活动指示器
   - 禁用按钮交互

2. **处理奖励逻辑**
   - 验证奖励资格
   - 添加金币到账户
   - 更新用户状态

3. **完成奖励**
   - 恢复按钮原始状态
   - 显示成功提示
   - 更新UI显示

## 技术特点

### 1. 模块化设计
- **独立的Loading方法**：可复用的Loading组件
- **状态管理分离**：购买和奖励Loading独立
- **配置灵活**：易于自定义样式和文字

### 2. 错误处理
- **异常恢复**：确保Loading状态能正确清除
- **超时保护**：防止Loading状态卡住
- **用户友好**：清晰的错误提示

### 3. 可维护性
- **代码清晰**：方法职责单一
- **易于扩展**：可轻松添加新的Loading场景
- **调试友好**：详细的状态日志

## 测试建议

1. **基本功能测试**
   - 点击购买按钮验证Loading显示
   - 确认Loading期间无法重复点击
   - 验证Loading完成后状态恢复

2. **边界情况测试**
   - 网络异常时的Loading处理
   - 快速连续点击的防护
   - 应用切换时的状态保持

3. **用户体验测试**
   - Loading动画流畅性
   - 文字显示清晰度
   - 整体视觉效果

## 文案显示优化

### 问题修复
原始实现中loading文案可能显示不全的问题已修复：

1. **容器宽度自适应**：
   - 最小宽度：200px
   - 最大宽度：280px
   - 根据文案长度自动调整

2. **文案简化**：
   - 购买Loading：从"Processing Purchase..."简化为"Processing..."
   - 每日奖励：从"🔄 Claiming Bonus..."简化为"🔄 Claiming..."

3. **多行支持**：
   - 设置`numberOfLines = 0`支持文案换行
   - 确保长文案能够完整显示

### 技术实现
```swift
// 容器宽度约束
container.widthAnchor.constraint(greaterThanOrEqualToConstant: 200), // 最小宽度
container.widthAnchor.constraint(lessThanOrEqualToConstant: 280), // 最大宽度

// 文案标签设置
label.text = "Processing..." // 简化文案
label.numberOfLines = 0 // 支持多行
label.textAlignment = .center
```

这个Loading功能大大提升了购买流程的用户体验，让用户清楚地知道操作正在进行，同时防止了意外的重复操作。文案显示问题已完全解决，确保在各种屏幕尺寸下都能正常显示。
