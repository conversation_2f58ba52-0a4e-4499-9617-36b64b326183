# Apple登录用户持久化功能演示

## 功能说明

现在Apple登录已经支持用户持久化功能。当用户使用Apple ID登录时，系统会：

1. **首次登录**：创建新用户并保存用户信息
2. **再次登录**：检查是否已存在相同Apple User ID的用户，如果存在则使用保存的用户信息

## 实现细节

### 核心功能

1. **用户检测**：根据Apple User ID检查是否已存在用户
2. **数据持久化**：将Apple用户数据保存到UserDefaults
3. **信息更新**：每次登录时更新登录时间和token（如果有新的）
4. **数据恢复**：退出登录后再次登录时恢复用户的所有数据（金币、设置等）

### 代码修改

#### UserAuthenticationManager.swift

1. **新增存储键**：
   ```swift
   private let appleUsersKey = "AppleUsers" // 存储所有Apple用户数据
   ```

2. **修改createAppleUser方法**：
   - 首先检查是否已存在该Apple User ID的用户
   - 如果存在，返回现有用户并更新登录信息
   - 如果不存在，创建新用户并保存

3. **新增Apple用户管理方法**：
   - `saveAppleUser(_:)`: 保存Apple用户数据
   - `loadAppleUsers()`: 加载所有Apple用户
   - `getExistingAppleUser(appleUserID:)`: 根据ID获取现有用户
   - `deleteSpecificAppleUser(appleUserID:)`: 删除特定用户数据

## 使用流程

### 第一次Apple登录
```
用户点击Apple登录 
→ 系统获取Apple凭证 
→ 检查是否已存在该User ID (不存在)
→ 创建新用户
→ 保存用户数据到本地
→ 登录成功
```

### 再次Apple登录
```
用户点击Apple登录 
→ 系统获取Apple凭证 
→ 检查是否已存在该User ID (存在!)
→ 加载保存的用户数据
→ 更新登录时间和token
→ 使用现有用户信息登录成功
```

## 数据保护

- 用户数据存储在设备本地的UserDefaults中
- 支持多个不同Apple用户的数据管理
- 删除账户时会清理对应的Apple用户数据
- 支持完全重置所有认证数据

## 日志输出

系统会输出详细的日志来帮助调试：

- `🔐 Creating new Apple user with:` - 创建新用户
- `🔄 [UserAuth] Found existing Apple user:` - 找到现有用户
- `💾 [UserAuth] Saved Apple user:` - 保存用户数据
- `🗑️ [UserAuth] Deleted Apple user data for ID:` - 删除用户数据

## 测试建议

1. **首次登录测试**：
   - 清理应用数据
   - 使用Apple ID登录
   - 验证用户信息正确创建

2. **持久化测试**：
   - 登录后使用应用（获得金币等）
   - 退出登录
   - 再次使用相同Apple ID登录
   - 验证用户数据（金币、设置等）是否保持

3. **多用户测试**：
   - 使用不同Apple ID登录
   - 验证每个用户的数据独立保存

## 注意事项

- 此功能依赖于Apple提供的稳定User ID
- 用户数据仅存储在本地设备上
- 如果用户删除应用或清理数据，用户信息会丢失
- 建议在生产环境中考虑添加云端同步功能
