import Foundation

struct Share: Identifiable, Codable {
    let id: String
    let imageURL: String
    let title: String
    let description: String
    var likes: Int
    var comments: Int
    var shares: Int
    var favorites: Int
    let author: Author
    let publishTime: Date
    
    // 用户交互状态（不需要编码到 JSON）
    var isLiked: Bool = false
    var isFavorited: Bool = false
    var isReported: Bool = false
    var isBlocked: Bool = false
    
    struct Author: Codable {
        let id: String
        let avatar: String
        let nickname: String
        let region: String
    }
    
    enum CodingKeys: String, CodingKey {
        case id, imageURL, title, description, likes, comments, shares, favorites, author, publishTime
    }
} 