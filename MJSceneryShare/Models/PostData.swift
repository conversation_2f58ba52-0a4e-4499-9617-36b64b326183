//
//  PostData.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-04.
//

import Foundation
import UIKit

// MARK: - Post Data Model
struct PostData: Identifiable, Codable {
    let id: String
    let imageFileName: String
    let caption: String
    let tags: [String] // Store tag names as strings for simplicity
    let contentType: String
    let analysisDescription: String
    let confidence: Double
    let mood: String?
    let colors: [String]
    let timestamp: Date
    let userId: String
    
    // Computed properties
    var formattedTimestamp: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "en_US")
        return formatter.localizedString(for: timestamp, relativeTo: Date())
    }
    
    var displayContentType: String {
        return contentType.capitalized
    }
    
    var confidencePercentage: String {
        return String(format: "%.0f%%", confidence * 100)
    }
    
    // Initialize from UI components
    init(image: UIImage, caption: String, tags: [ImageTag], analysisResult: ImageRecognitionResult, timestamp: Date) {
        self.id = UUID().uuidString
        self.imageFileName = "\(UUID().uuidString).jpg"
        self.caption = caption
        self.tags = tags.map { $0.name }
        self.contentType = analysisResult.contentType.rawValue
        self.analysisDescription = analysisResult.description
        self.confidence = analysisResult.confidence
        self.mood = analysisResult.mood
        self.colors = analysisResult.colors
        self.timestamp = timestamp
        self.userId = AppViewModel.shared.currentUser.id
        
        // Save image to documents directory
        PostStorageManager.shared.saveImage(image, fileName: self.imageFileName)
    }
    
    // Get the actual image from storage
    func getImage() -> UIImage? {
        return PostStorageManager.shared.loadImage(fileName: imageFileName)
    }
}

// MARK: - Post Statistics
struct PostStatistics: Codable {
    var totalPosts: Int
    var postsByContentType: [String: Int]
    var averageConfidence: Double
    var mostUsedTags: [String: Int]
    var postsThisWeek: Int
    var postsThisMonth: Int
    
    init() {
        self.totalPosts = 0
        self.postsByContentType = [:]
        self.averageConfidence = 0.0
        self.mostUsedTags = [:]
        self.postsThisWeek = 0
        self.postsThisMonth = 0
    }
}

// MARK: - Post Filter Options
enum PostFilterOption: String, CaseIterable {
    case all = "all"
    case landscape = "landscape"
    case portrait = "portrait"
    case food = "food"
    case architecture = "architecture"
    case nature = "nature"
    case animal = "animal"
    case object = "object"
    case travel = "travel"
    case lifestyle = "lifestyle"
    
    var displayName: String {
        switch self {
        case .all: return "All Posts"
        case .landscape: return "🏞️ Landscape"
        case .portrait: return "👤 Portrait"
        case .food: return "🍽️ Food"
        case .architecture: return "🏛️ Architecture"
        case .nature: return "🌿 Nature"
        case .animal: return "🐾 Animal"
        case .object: return "📦 Object"
        case .travel: return "✈️ Travel"
        case .lifestyle: return "✨ Lifestyle"
        }
    }
}

// MARK: - Post Sort Options
enum PostSortOption: String, CaseIterable {
    case newest = "newest"
    case oldest = "oldest"
    case highestConfidence = "highest_confidence"
    case lowestConfidence = "lowest_confidence"
    case mostTags = "most_tags"
    
    var displayName: String {
        switch self {
        case .newest: return "Newest First"
        case .oldest: return "Oldest First"
        case .highestConfidence: return "Highest Confidence"
        case .lowestConfidence: return "Lowest Confidence"
        case .mostTags: return "Most Tags"
        }
    }
}

// MARK: - Post Export Data
struct PostExportData: Codable {
    let posts: [PostData]
    let statistics: PostStatistics
    let exportDate: Date
    let appVersion: String
    
    init(posts: [PostData], statistics: PostStatistics) {
        self.posts = posts
        self.statistics = statistics
        self.exportDate = Date()
        self.appVersion = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
    }
}

// MARK: - Post Search Result
struct PostSearchResult {
    let post: PostData
    let matchType: SearchMatchType
    let relevanceScore: Double
}

enum SearchMatchType {
    case caption
    case tag
    case contentType
    case description
}

// MARK: - Extensions
extension PostData {
    // Convert PostData to Share for Discover feed
    func toShare() -> Share {
        let currentUser = AppViewModel.shared.currentUser

        // Use filename directly for local image loading
        let imageURL = "local://\(imageFileName)"

        let author = Share.Author(
            id: userId,
            avatar: currentUser.avatar,
            nickname: currentUser.nickname,
            region: "Local User" // Default region for local posts
        )

        return Share(
            id: id,
            imageURL: imageURL,
            title: caption.isEmpty ? "Beautiful Moment ✨" : caption,
            description: analysisDescription,
            likes: 0,
            comments: 0,
            shares: 0,
            favorites: 0,
            author: author,
            publishTime: timestamp
        )
    }
    // Search functionality
    func matches(searchText: String) -> PostSearchResult? {
        let lowercaseSearch = searchText.lowercased()
        var relevanceScore: Double = 0
        var matchType: SearchMatchType = .caption
        
        // Check caption match
        if caption.lowercased().contains(lowercaseSearch) {
            relevanceScore = 1.0
            matchType = .caption
        }
        // Check tag match
        else if tags.contains(where: { $0.lowercased().contains(lowercaseSearch) }) {
            relevanceScore = 0.8
            matchType = .tag
        }
        // Check content type match
        else if contentType.lowercased().contains(lowercaseSearch) {
            relevanceScore = 0.6
            matchType = .contentType
        }
        // Check description match
        else if analysisDescription.lowercased().contains(lowercaseSearch) {
            relevanceScore = 0.4
            matchType = .description
        }
        
        if relevanceScore > 0 {
            return PostSearchResult(post: self, matchType: matchType, relevanceScore: relevanceScore)
        }
        
        return nil
    }
    
    // Filter by content type
    func matches(filter: PostFilterOption) -> Bool {
        if filter == .all {
            return true
        }
        return contentType == filter.rawValue
    }
    
    // Get hashtags from caption
    var hashtags: [String] {
        let words = caption.components(separatedBy: .whitespacesAndNewlines)
        return words.filter { $0.hasPrefix("#") }.map { String($0.dropFirst()) }
    }
    
    // Get clean caption without hashtags
    var cleanCaption: String {
        let lines = caption.components(separatedBy: .newlines)
        let nonTagLines = lines.filter { !$0.hasPrefix("#") && !$0.trimmingCharacters(in: .whitespaces).isEmpty }
        return nonTagLines.joined(separator: "\n")
    }
}

extension Array where Element == PostData {
    // Sort posts
    func sorted(by option: PostSortOption) -> [PostData] {
        switch option {
        case .newest:
            return self.sorted { $0.timestamp > $1.timestamp }
        case .oldest:
            return self.sorted { $0.timestamp < $1.timestamp }
        case .highestConfidence:
            return self.sorted { $0.confidence > $1.confidence }
        case .lowestConfidence:
            return self.sorted { $0.confidence < $1.confidence }
        case .mostTags:
            return self.sorted { $0.tags.count > $1.tags.count }
        }
    }
    
    // Filter posts
    func filtered(by option: PostFilterOption) -> [PostData] {
        if option == .all {
            return self
        }
        return self.filter { $0.matches(filter: option) }
    }
    
    // Search posts
    func search(text: String) -> [PostSearchResult] {
        return self.compactMap { $0.matches(searchText: text) }
            .sorted { $0.relevanceScore > $1.relevanceScore }
    }
    
    // Generate statistics
    func generateStatistics() -> PostStatistics {
        var stats = PostStatistics()
        stats.totalPosts = self.count
        
        if !self.isEmpty {
            // Calculate average confidence
            stats.averageConfidence = self.reduce(0) { $0 + $1.confidence } / Double(self.count)
            
            // Count posts by content type
            for post in self {
                stats.postsByContentType[post.contentType, default: 0] += 1
            }
            
            // Count most used tags
            for post in self {
                for tag in post.tags {
                    stats.mostUsedTags[tag, default: 0] += 1
                }
            }
            
            // Count posts this week and month
            let calendar = Calendar.current
            let now = Date()
            let weekAgo = calendar.date(byAdding: .weekOfYear, value: -1, to: now) ?? now
            let monthAgo = calendar.date(byAdding: .month, value: -1, to: now) ?? now
            
            stats.postsThisWeek = self.filter { $0.timestamp >= weekAgo }.count
            stats.postsThisMonth = self.filter { $0.timestamp >= monthAgo }.count
        }
        
        return stats
    }
}
