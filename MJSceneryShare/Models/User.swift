import Foundation

struct User: Codable {
    let id: String
    var nickname: String
    var avatar: String
    var coins: Int
    var isLoggedIn: Bool
    var freeProcessingCount: Int
    var purchasedProcessingCount: Int  // 购买获得的AI分析次数
    var totalProcessingCount: Int
    var lastLoginDate: Date?
    var registrationDate: Date
    var hasClaimedDailyBonus: Bool
    var lastDailyBonusDate: Date?

    // MARK: - Apple Login Specific Fields
    var email: String?
    var appleUserID: String?
    var appleIdentityToken: String?
    var fullName: String?
    var loginType: String? // "guest" or "apple"

    init(id: String, nickname: String, avatar: String, coins: Int, isLoggedIn: Bool, loginType: String = "guest") {
        self.id = id
        self.nickname = nickname
        self.avatar = avatar
        self.coins = coins
        self.isLoggedIn = isLoggedIn
        self.freeProcessingCount = CoinSystemConfig.enableFreeProcessingForNewUsers ? AIProcessingPricing.freeProcessingLimit : 0
        self.purchasedProcessingCount = 0
        self.totalProcessingCount = 0
        self.lastLoginDate = isLoggedIn ? Date() : nil
        self.registrationDate = Date()
        self.hasClaimedDailyBonus = false
        self.lastDailyBonusDate = nil

        // Initialize Apple-specific fields
        self.email = nil
        self.appleUserID = nil
        self.appleIdentityToken = nil
        self.fullName = nil
        self.loginType = loginType
    }

    // MARK: - Apple Login Initializer
    init(appleUserID: String, nickname: String, email: String?, fullName: String?, avatar: String, coins: Int, identityToken: String?) {
        self.id = appleUserID
        self.nickname = nickname
        self.avatar = avatar
        self.coins = coins
        self.isLoggedIn = true
        self.freeProcessingCount = CoinSystemConfig.enableFreeProcessingForNewUsers ? AIProcessingPricing.freeProcessingLimit : 0
        self.purchasedProcessingCount = 0
        self.totalProcessingCount = 0
        self.lastLoginDate = Date()
        self.registrationDate = Date()
        self.hasClaimedDailyBonus = false
        self.lastDailyBonusDate = nil

        // Apple-specific fields
        self.email = email
        self.appleUserID = appleUserID
        self.appleIdentityToken = identityToken
        self.fullName = fullName
        self.loginType = "apple"
    }

    static var guest: User {
        User(id: "guest",
             nickname: "Guest",
             avatar: AvatarManager.shared.getRandomAvatarName(),
             coins: 0,
             isLoggedIn: false,
             loginType: "guest")
    }

    // MARK: - Computed Properties
    var canProcessForFree: Bool {
        return freeProcessingCount > 0
    }

    var canAffordProcessing: Bool {
        return canProcessForFree || (!isGuestUser && purchasedProcessingCount > 0)
    }

    var processingCost: Int {
        return 0  // No coin cost - using direct AI count deduction
    }

    /// Deduct one AI processing count (free first, then purchased)
    mutating func deductAIProcessing() -> Bool {
        if freeProcessingCount > 0 {
            freeProcessingCount -= 1
            return true
        } else if purchasedProcessingCount > 0 {
            purchasedProcessingCount -= 1
            return true
        }
        return false
    }

    var isLowOnCoins: Bool {
        return totalAvailableProcessing <= 3  // 当总可用AI分析次数少于等于3次时提醒
    }

    /// Total number of AI processing attempts available (free + purchased)
    var totalAvailableProcessing: Int {
        return freeProcessingCount + purchasedProcessingCount
    }

    /// Number of purchased AI processing attempts available (renamed for clarity)
    var paidProcessingCount: Int {
        return purchasedProcessingCount
    }

    var canClaimDailyBonus: Bool {
        guard CoinSystemConfig.enableDailyBonus else { return false }

        // Check if user has already claimed today
        if let lastBonusDate = lastDailyBonusDate {
            let calendar = Calendar.current
            // If the last bonus was claimed today, can't claim again
            if calendar.isDate(lastBonusDate, inSameDayAs: Date()) {
                return false
            }
        }

        // Can claim if it's a new day or first time claiming
        return true
    }

    var daysSinceRegistration: Int {
        let calendar = Calendar.current
        return calendar.dateComponents([.day], from: registrationDate, to: Date()).day ?? 0
    }

    // MARK: - Apple Login Computed Properties
    var displayEmail: String {
        return email ?? "-"
    }

    var isAppleUser: Bool {
        return loginType == "apple" && appleUserID != nil
    }

    var isGuestUser: Bool {
        return loginType == "guest" || id == "guest"
    }

    var loginTypeDisplayName: String {
        switch loginType {
        case "apple":
            return "Apple ID"
        case "guest":
            return "Guest"
        default:
            return "Unknown"
        }
    }

    // MARK: - Mutating Methods
    mutating func processImage() -> Bool {
        guard canAffordProcessing else { return false }

        if canProcessForFree {
            freeProcessingCount -= 1
        } else if !isGuestUser && purchasedProcessingCount > 0 {
            purchasedProcessingCount -= 1
        } else {
            return false  // 不应该到达这里
        }

        totalProcessingCount += 1
        return true
    }

    mutating func addCoins(_ amount: Int) {
        coins += amount
    }

    mutating func addPurchasedProcessing(_ count: Int) {
        purchasedProcessingCount += count
    }

    mutating func claimDailyBonus() -> Bool {
        guard canClaimDailyBonus else { return false }

        coins += CoinSystemConfig.dailyBonusAmount
        hasClaimedDailyBonus = true
        lastDailyBonusDate = Date()
        return true
    }

    mutating func updateLastLogin() {
        lastLoginDate = Date()

        // Reset daily bonus flag if it's a new day or if user has never claimed before
        let calendar = Calendar.current
        if let lastBonusDate = lastDailyBonusDate {
            // User has claimed before - check if it's a new day
            if !calendar.isDate(lastBonusDate, inSameDayAs: Date()) {
                hasClaimedDailyBonus = false
            }
        } else {
            // User has never claimed daily bonus - allow claiming
            hasClaimedDailyBonus = false
        }
    }
}