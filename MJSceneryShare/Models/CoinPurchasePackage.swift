//
//  CoinPurchasePackage.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-06.
//

import Foundation

// MARK: - Coin Purchase Package
struct CoinPurchasePackage: Identifiable, CaseIterable {
    let id: String
    let coins: Int
    let price: String
    let productId: String
    let bonusCoins: Int
    let aiAnalysisCount: Int  // 直接定义AI分析次数
    let isPopular: Bool
    let isBestValue: Bool
    let discountPercentage: Int?
    let emoji: String
    let title: String
    let description: String
    let features: [String]

    var totalCoins: Int {
        return coins + bonusCoins
    }

    var displayTitle: String {
        if bonusCoins > 0 {
            return "\(coins) + \(bonusCoins) Bonus"
        } else {
            return "\(coins) Coins"
        }
    }
    
    // MARK: - Static Package Definitions (similar to enum cases)
    
    static let starter = CoinPurchasePackage(
        id: "starter",
        coins: 100,
        price: "$0.99",
//        productId: "com.beautyspots.coins100",
        productId: "com.askina.coins.100",
        bonusCoins: 0,
        aiAnalysisCount: 5,
        isPopular: false,
        isBestValue: false,
        discountPercentage: nil,
        emoji: "🌱",
        title: "Starter Pack",
        description: "Perfect for trying out AI features",
        features: ["5 AI image analyses", "Basic photo enhancement", "Standard support"]
    )
    
    static let popular = CoinPurchasePackage(
        id: "popular",
        coins: 250,
        price: "$1.99",
//        productId: "com.beautyspots.coins250",
        productId: "com.askina.coins.500",
        bonusCoins: 50,
        aiAnalysisCount: 15,
        isPopular: true,
        isBestValue: false,
        discountPercentage: 20,
        emoji: "🔥",
        title: "Popular Choice",
        description: "Most loved by our community",
        features: ["15 AI image analyses", "Priority processing", "Advanced filters", "Email support"]
    )
    
    static let value = CoinPurchasePackage(
        id: "value",
        coins: 500,
        price: "$3.99",
        productId: "com.beautyspots.coins500",
        bonusCoins: 150,
        aiAnalysisCount: 32,
        isPopular: false,
        isBestValue: true,
        discountPercentage: 35,
        emoji: "💎",
        title: "Best Value",
        description: "Maximum savings for regular users",
        features: ["32+ AI image analyses", "Unlimited filters", "Premium templates", "Priority support"]
    )
    
    static let premium = CoinPurchasePackage(
        id: "premium",
        coins: 1000,
        price: "$6.99",
        productId: "com.beautyspots.coins1000",
        bonusCoins: 400,
        aiAnalysisCount: 70,
        isPopular: false,
        isBestValue: false,
        discountPercentage: 40,
        emoji: "👑",
        title: "Premium Pack",
        description: "For power users and creators",
        features: ["70+ AI image analyses", "Exclusive features", "Custom templates", "VIP support"]
    )
    
    static let ultimate = CoinPurchasePackage(
        id: "ultimate",
        coins: 2000,
        price: "$11.99",
        productId: "com.beautyspots.coins2000",
        bonusCoins: 1000,
        aiAnalysisCount: 150,
        isPopular: false,
        isBestValue: false,
        discountPercentage: 50,
        emoji: "🚀",
        title: "Ultimate Pack",
        description: "Maximum power for professionals",
        features: ["150+ AI image analyses", "All premium features", "Early access to new tools", "Dedicated support"]
    )
    
    static let mega = CoinPurchasePackage(
        id: "mega",
        coins: 5000,
        price: "$24.99",
        productId: "com.beautyspots.coins5000",
        bonusCoins: 3000,
        aiAnalysisCount: 400,
        isPopular: false,
        isBestValue: false,
        discountPercentage: 60,
        emoji: "⭐",
        title: "Mega Pack",
        description: "Ultimate value for heavy users",
        features: ["400+ AI image analyses", "Lifetime premium features", "Beta access", "Personal account manager"]
    )
    
    // MARK: - CaseIterable Implementation
    static var allCases: [CoinPurchasePackage] {
        return [starter, popular, value, premium, ultimate, mega]
    }
    
    // MARK: - Convenience Methods (similar to enum functionality)
    
    /// Get package by product ID (similar to enum rawValue init)
    static func package(for productId: String) -> CoinPurchasePackage? {
        return allCases.first { $0.productId == productId }
    }
    
    /// Get package by ID
    static func package(withId id: String) -> CoinPurchasePackage? {
        return allCases.first { $0.id == id }
    }
    
    /// Get all product IDs (for StoreKit)
    static var allProductIds: [String] {
        return allCases.map { $0.productId }
    }
    
    /// Get all product IDs as Set (for StoreKit)
    static var productIdSet: Set<String> {
        return Set(allProductIds)
    }
    
    /// Local fallback price (shown until a real SKProduct arrives)
    var fallbackPrice: String {
        return price
    }
    
    /// Display name for debug logs
    var displayName: String {
        return title
    }
    
    /// Number of coins the user receives (including bonus)
    var coinAmount: Int {
        return totalCoins
    }

    /// Subtitle for UI display (combines price with special offers)
    var subtitle: String {
        var components: [String] = []

        if let discount = discountPercentage {
            components.append("\(discount)% OFF")
        }

        if isPopular {
            components.append("Most Popular")
        }

        if isBestValue {
            components.append("Best Value")
        }

        return components.isEmpty ? price : "\(price) • \(components.joined(separator: " • "))"
    }
}

// MARK: - Equatable
extension CoinPurchasePackage: Equatable {
    static func == (lhs: CoinPurchasePackage, rhs: CoinPurchasePackage) -> Bool {
        return lhs.productId == rhs.productId
    }
}

// MARK: - Hashable
extension CoinPurchasePackage: Hashable {
    func hash(into hasher: inout Hasher) {
        hasher.combine(productId)
    }
}
