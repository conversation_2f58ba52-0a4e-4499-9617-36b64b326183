//
//  CoinSystem.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-04.
//

import Foundation

// MARK: - Coin Transaction Types
enum CoinTransactionType: String, Codable, CaseIterable {
    case purchase = "purchase"
    case aiProcessing = "ai_processing"
    case dailyBonus = "daily_bonus"
    case referralBonus = "referral_bonus"
    case refund = "refund"
    case contentPublishing = "content_publishing"

    var displayName: String {
        switch self {
        case .purchase: return "Coin Purchase"
        case .aiProcessing: return "AI Image Processing"
        case .dailyBonus: return "Daily Login Bonus"
        case .referralBonus: return "Referral Bonus"
        case .refund: return "Refund"
        case .contentPublishing: return "Content Publishing"
        }
    }

    var isDebit: Bool {
        switch self {
        case .aiProcessing, .contentPublishing: return true
        case .purchase, .dailyBonus, .referralBonus, .refund: return false
        }
    }
}

// MARK: - Coin Transaction Model
struct CoinTransaction: Identifiable, Codable {
    let id: String
    let userId: String
    let type: CoinTransactionType
    let amount: Int
    let balanceAfter: Int
    let timestamp: Date
    let description: String
    let metadata: [String: String]?
    
    init(userId: String, type: CoinTransactionType, amount: Int, balanceAfter: Int, description: String, metadata: [String: String]? = nil) {
        self.id = UUID().uuidString
        self.userId = userId
        self.type = type
        self.amount = amount
        self.balanceAfter = balanceAfter
        self.timestamp = Date()
        self.description = description
        self.metadata = metadata
    }
    
    var formattedAmount: String {
        let prefix = type.isDebit ? "-" : "+"
        return "\(prefix)\(amount)"
    }
    
    var formattedTimestamp: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "en_US")
        return formatter.localizedString(for: timestamp, relativeTo: Date())
    }
}

// MARK: - AI Processing Pricing
struct AIProcessingPricing {
    static let imageAnalysisCost = 20
    static let captionGenerationCost = 10
    static let tagSuggestionCost = 5
    static let fullProcessingCost = imageAnalysisCost // Full processing includes all features
    
    static let freeProcessingLimit = 3 // Free AI processing attempts for new users
    
    static func getCostForProcessing(includeCaption: Bool = true, includeTags: Bool = true) -> Int {
        return fullProcessingCost // Simplified pricing - one cost for full processing
    }
}

// MARK: - Coin Balance Model
struct CoinBalance: Codable {
    let userId: String
    var totalCoins: Int
    var freeProcessingCount: Int
    let lastUpdated: Date
    let createdDate: Date
    
    init(userId: String, initialCoins: Int = 100) {
        self.userId = userId
        self.totalCoins = initialCoins
        self.freeProcessingCount = AIProcessingPricing.freeProcessingLimit
        self.lastUpdated = Date()
        self.createdDate = Date()
    }
    
    var hasFreeProcesing: Bool {
        return freeProcessingCount > 0
    }

    // Note: This method is deprecated in favor of User.canAffordProcessing
    // which uses the new direct AI count system
    func canAffordProcessing() -> Bool {
        return hasFreeProcesing || totalCoins >= AIProcessingPricing.fullProcessingCost
    }

    // Note: This method is deprecated in favor of direct AI count deduction
    func getProcessingCost() -> Int {
        return hasFreeProcesing ? 0 : AIProcessingPricing.fullProcessingCost
    }
}

// MARK: - Coin Usage Statistics
struct CoinUsageStatistics: Codable {
    let userId: String
    var totalSpent: Int
    var totalEarned: Int
    var aiProcessingCount: Int
    var totalTransactions: Int
    var averageSpendingPerDay: Double
    var lastProcessingDate: Date?
    let trackingStartDate: Date
    
    init(userId: String) {
        self.userId = userId
        self.totalSpent = 0
        self.totalEarned = 0
        self.aiProcessingCount = 0
        self.totalTransactions = 0
        self.averageSpendingPerDay = 0.0
        self.lastProcessingDate = nil
        self.trackingStartDate = Date()
    }
    
    var netBalance: Int {
        return totalEarned - totalSpent
    }
    
    var processingFrequency: String {
        guard aiProcessingCount > 0 else { return "No processing yet" }
        
        let daysSinceStart = Calendar.current.dateComponents([.day], from: trackingStartDate, to: Date()).day ?? 1
        let frequency = Double(aiProcessingCount) / Double(max(daysSinceStart, 1))
        
        if frequency >= 1.0 {
            return String(format: "%.1f per day", frequency)
        } else {
            let weeklyFrequency = frequency * 7
            return String(format: "%.1f per week", weeklyFrequency)
        }
    }
}

// MARK: - Purchase Package
// CoinPurchasePackage has been moved to its own file: CoinPurchasePackage.swift

// MARK: - Coin System Configuration
struct CoinSystemConfig {
    static let newUserStartingCoins = 100  // New users start with 100 coins + 3 free AI analyses
    static let dailyBonusAmount = 10
    static let referralBonusAmount = 50
    static let maxDailyProcessing = 20
    static let lowBalanceWarningThreshold = 50
    static let contentPublishingCost = 30

    // Processing costs
    static let costs = AIProcessingPricing.self

    // Feature flags
    static let enableDailyBonus = true
    static let enableReferralSystem = true
    static let enableFreeProcessingForNewUsers = true
}

// MARK: - Coin System Errors
enum CoinSystemError: LocalizedError {
    case insufficientFunds(required: Int, available: Int)
    case invalidTransaction
    case userNotFound
    case processingLimitReached
    case networkError
    
    var errorDescription: String? {
        switch self {
        case .insufficientFunds(let required, let available):
            return "Insufficient coins. Need \(required) coins, but only have \(available) coins."
        case .invalidTransaction:
            return "Invalid transaction. Please try again."
        case .userNotFound:
            return "User not found."
        case .processingLimitReached:
            return "Daily processing limit reached. Please try again tomorrow."
        case .networkError:
            return "Network error. Please check your connection."
        }
    }
    
    var recoverySuggestion: String? {
        switch self {
        case .insufficientFunds:
            return "Purchase more coins to continue using AI features."
        case .processingLimitReached:
            return "Upgrade to premium for unlimited processing."
        default:
            return "Please try again later."
        }
    }
}
