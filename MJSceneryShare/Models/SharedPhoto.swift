import Foundation

// MARK: - Shared Photo Model
struct SharedPhoto: Identifiable, Codable, Hashable {
    let id: String
    let senderId: String
    let receiverId: String
    let imageURL: String
    let thumbnailURL: String?
    let caption: String?
    let timestamp: Date
    let expiresAt: Date?
    var isViewed: Bool
    var reactions: [PhotoReaction]
    let originalPostId: String? // If shared from Discover feed
    
    // Computed properties
    var isExpired: Bool {
        guard let expiresAt = expiresAt else { return false }
        return Date() > expiresAt
    }
    
    var isSentByCurrentUser: Bool {
        return senderId == AppViewModel.shared.currentUser.id
    }
    
    var timeRemaining: String? {
        guard let expiresAt = expiresAt, !isExpired else { return nil }
        
        let timeInterval = expiresAt.timeIntervalSince(Date())
        let hours = Int(timeInterval) / 3600
        let minutes = Int(Int(timeInterval) % 3600) / 60
        
        if hours > 0 {
            return "\(hours)h \(minutes)m"
        } else {
            return "\(minutes)m"
        }
    }
    
    var formattedTimestamp: String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "en_US")
        return formatter.localizedString(for: timestamp, relativeTo: Date())
    }
}

// MARK: - Photo Reaction Model
struct PhotoReaction: Identifiable, Codable, Hashable {
    let id: String
    let photoId: String
    let userId: String
    let type: PhotoReactionType
    let timestamp: Date
    
    var emoji: String {
        return type.emoji
    }
}

// MARK: - Photo Reaction Type Enum
enum PhotoReactionType: String, Codable, CaseIterable {
    case love = "love"
    case laugh = "laugh"
    case wow = "wow"
    case sad = "sad"
    case angry = "angry"
    case fire = "fire"
    case sparkles = "sparkles"
    case heart = "heart"
    
    var emoji: String {
        switch self {
        case .love:
            return "😍"
        case .laugh:
            return "😂"
        case .wow:
            return "😮"
        case .sad:
            return "😢"
        case .angry:
            return "😡"
        case .fire:
            return "🔥"
        case .sparkles:
            return "✨"
        case .heart:
            return "💕"
        }
    }
    
    var displayName: String {
        switch self {
        case .love:
            return "Love"
        case .laugh:
            return "Laugh"
        case .wow:
            return "Wow"
        case .sad:
            return "Sad"
        case .angry:
            return "Angry"
        case .fire:
            return "Fire"
        case .sparkles:
            return "Sparkles"
        case .heart:
            return "Heart"
        }
    }
}

// MARK: - Photo Expiration Options
enum PhotoExpirationOption: String, Codable, CaseIterable {
    case oneHour = "1h"
    case twentyFourHours = "24h"
    case oneWeek = "1w"
    case never = "never"
    
    var displayName: String {
        switch self {
        case .oneHour:
            return "1 Hour"
        case .twentyFourHours:
            return "24 Hours"
        case .oneWeek:
            return "1 Week"
        case .never:
            return "Never"
        }
    }
    
    var timeInterval: TimeInterval? {
        switch self {
        case .oneHour:
            return 3600
        case .twentyFourHours:
            return 86400
        case .oneWeek:
            return 604800
        case .never:
            return nil
        }
    }
    
    var icon: String {
        switch self {
        case .oneHour:
            return "clock"
        case .twentyFourHours:
            return "clock.badge"
        case .oneWeek:
            return "calendar"
        case .never:
            return "infinity"
        }
    }
}

// MARK: - Photo Share Request Model
struct PhotoShareRequest: Codable {
    let imageURL: String
    let caption: String?
    let recipientIds: [String]
    let expirationOption: PhotoExpirationOption
    let originalPostId: String?
}

// MARK: - Photo Share Response Model
struct PhotoShareResponse: Codable {
    let sharedPhotos: [SharedPhoto]
    let success: Bool
    let error: String?
}

// MARK: - Shared Photo Gallery Model
struct SharedPhotoGallery: Identifiable, Codable {
    let id: String
    let friendId: String
    let photos: [SharedPhoto]
    let totalCount: Int
    let lastUpdated: Date
    
    var recentPhotos: [SharedPhoto] {
        return Array(photos.prefix(10))
    }
    
    var hasNewPhotos: Bool {
        return photos.contains { !$0.isViewed && !$0.isSentByCurrentUser }
    }
}

// MARK: - Real Data Extensions
extension SharedPhoto {
    static func realSharedPhotos() -> [SharedPhoto] {
        return RealDataManager.generateRealSharedPhotos()
    }
}

extension SharedPhotoGallery {
    static func realGalleries() -> [SharedPhotoGallery] {
        let currentUserId = AppViewModel.shared.currentUser.id
        let sharedPhotos = SharedPhoto.realSharedPhotos()
        
        return [
            SharedPhotoGallery(
                id: "gallery_1",
                friendId: "user_emma",
                photos: sharedPhotos.filter { $0.senderId == "user_emma" || $0.receiverId == "user_emma" },
                totalCount: 15,
                lastUpdated: Date().addingTimeInterval(-3600)
            ),
            SharedPhotoGallery(
                id: "gallery_2",
                friendId: "user_sophia",
                photos: sharedPhotos.filter { $0.senderId == "user_sophia" || $0.receiverId == "user_sophia" },
                totalCount: 8,
                lastUpdated: Date().addingTimeInterval(-7200)
            )
        ]
    }
}
