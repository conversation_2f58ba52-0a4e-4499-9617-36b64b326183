import Foundation

// MARK: - Chat Message Model
struct ChatMessage: Identifiable, Codable, Hashable {
    let id: String
    let conversationId: String
    let senderId: String
    let receiverId: String
    let content: String
    let type: MessageType
    let timestamp: Date
    var isRead: Bool
    var isDelivered: Bool
    var reactions: [MessageReaction]
    let replyToMessageId: String?
    
    // Computed properties
    var isSentByCurrentUser: Bool {
        // This would be determined by comparing with current user ID
        return senderId == AppViewModel.shared.currentUser.id
    }
    
    var formattedTime: String {
        let formatter = DateFormatter()
        formatter.timeStyle = .short
        return formatter.string(from: timestamp)
    }
    
    var formattedDate: String {
        let formatter = DateFormatter()
        formatter.dateStyle = .medium
        return formatter.string(from: timestamp)
    }
    
    var isToday: Bool {
        return Calendar.current.isDateInToday(timestamp)
    }
    
    var isYesterday: Bool {
        return Calendar.current.isDateInYesterday(timestamp)
    }
}

// MARK: - Message Type Enum
enum MessageType: String, Codable, CaseIterable {
    case text = "text"
    case photo = "photo"
    case voice = "voice"
    case location = "location"
    case sticker = "sticker"
    case system = "system"
    case sharedContent = "shared_content"
    
    var displayName: String {
        switch self {
        case .text:
            return "Text"
        case .photo:
            return "Photo"
        case .voice:
            return "Voice"
        case .location:
            return "Location"
        case .sticker:
            return "Sticker"
        case .system:
            return "System"
        case .sharedContent:
            return "Shared Content"
        }
    }
    
    var icon: String {
        switch self {
        case .text:
            return "text.bubble"
        case .photo:
            return "photo"
        case .voice:
            return "mic"
        case .location:
            return "location"
        case .sticker:
            return "face.smiling"
        case .system:
            return "info.circle"
        case .sharedContent:
            return "square.and.arrow.up"
        }
    }
}

// MARK: - Message Reaction Model
struct MessageReaction: Identifiable, Codable, Hashable {
    let id: String
    let messageId: String
    let userId: String
    let emoji: String
    let timestamp: Date
    
    static let availableEmojis = ["❤️", "😂", "😮", "😢", "😡", "👍", "👎", "✨", "💕", "🔥"]
}

// MARK: - Conversation Model
struct Conversation: Identifiable, Codable {
    let id: String
    let participants: [String] // User IDs
    let lastMessage: ChatMessage?
    let lastActivity: Date
    var unreadCount: Int
    let createdDate: Date
    var isArchived: Bool
    var isMuted: Bool
    
    var otherParticipantId: String? {
        // For 1-on-1 conversations, return the other participant's ID
        let currentUserId = AppViewModel.shared.currentUser.id
        return participants.first { $0 != currentUserId }
    }
    
    var hasUnreadMessages: Bool {
        return unreadCount > 0
    }
}

// MARK: - Typing Indicator Model
struct TypingIndicator: Codable {
    let conversationId: String
    let userId: String
    let isTyping: Bool
    let timestamp: Date
}

// MARK: - Chat Response Models
struct ChatResponse: Codable {
    let conversations: [Conversation]
    let messages: [ChatMessage]
}

struct SendMessageRequest: Codable {
    let conversationId: String
    let content: String
    let type: MessageType
    let replyToMessageId: String?
}

struct SendMessageResponse: Codable {
    let message: ChatMessage
    let success: Bool
    let error: String?
}

// MARK: - Real Data Extensions
extension ChatMessage {
    static func realMessages(for conversationId: String) -> [ChatMessage] {
        // Extract friend ID from conversation ID
        let friendId = conversationId.replacingOccurrences(of: "conv_", with: "user_")
        return RealDataManager.generateRealChatMessages(for: conversationId, with: friendId)
    }
}

extension Conversation {
    static func realConversations() -> [Conversation] {
        let currentUserId = AppViewModel.shared.currentUser.id
        
        return [
            Conversation(
                id: "conv_1",
                participants: [currentUserId, "user_emma"],
                lastMessage: ChatMessage(
                    id: "msg_last_1",
                    conversationId: "conv_1",
                    senderId: "user_emma",
                    receiverId: currentUserId,
                    content: "Absolutely stunning! Where was this taken? 🏔️",
                    type: .text,
                    timestamp: Date().addingTimeInterval(-300),
                    isRead: false,
                    isDelivered: true,
                    reactions: [],
                    replyToMessageId: nil
                ),
                lastActivity: Date().addingTimeInterval(-300),
                unreadCount: 2,
                createdDate: Date().addingTimeInterval(-86400 * 7),
                isArchived: false,
                isMuted: false
            ),
            Conversation(
                id: "conv_2",
                participants: [currentUserId, "user_sophia"],
                lastMessage: ChatMessage(
                    id: "msg_last_2",
                    conversationId: "conv_2",
                    senderId: currentUserId,
                    receiverId: "user_sophia",
                    content: "Thanks for the yoga tips! 🧘‍♀️",
                    type: .text,
                    timestamp: Date().addingTimeInterval(-1800),
                    isRead: true,
                    isDelivered: true,
                    reactions: [],
                    replyToMessageId: nil
                ),
                lastActivity: Date().addingTimeInterval(-1800),
                unreadCount: 0,
                createdDate: Date().addingTimeInterval(-86400 * 14),
                isArchived: false,
                isMuted: false
            )
        ]
    }
}
