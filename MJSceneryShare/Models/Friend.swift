import Foundation

// MARK: - Friend Model
struct Friend: Identifiable, Codable, Hashable {
    let id: String
    let userId: String
    let username: String
    let displayName: String
    let avatar: String
    let bio: String?
    let location: String?
    var status: FriendStatus
    let lastSeen: Date
    let mutualFriends: Int
    let friendshipDate: Date
    var isBlocked: Bool
    
    // Computed properties
    var isOnline: Bool {
        return status == .online
    }
    
    var isRecentlyActive: Bool {
        let oneHourAgo = Date().addingTimeInterval(-3600)
        return status == .recentlyActive || lastSeen > oneHourAgo
    }
    
    var statusDisplayText: String {
        switch status {
        case .online:
            return "Online"
        case .recentlyActive:
            return "Active recently"
        case .offline:
            let formatter = RelativeDateTimeFormatter()
            formatter.locale = Locale(identifier: "en_US")
            return "Last seen \(formatter.localizedString(for: lastSeen, relativeTo: Date()))"
        case .doNotDisturb:
            return "Do not disturb"
        }
    }
}

// MARK: - Friend Status Enum
enum FriendStatus: String, Codable, CaseIterable {
    case online = "online"
    case recentlyActive = "recently_active"
    case offline = "offline"
    case doNotDisturb = "do_not_disturb"
    
    var icon: String {
        switch self {
        case .online:
            return "circle.fill"
        case .recentlyActive:
            return "circle.fill"
        case .offline:
            return ""
        case .doNotDisturb:
            return "moon.fill"
        }
    }
    
    var color: String {
        switch self {
        case .online:
            return "systemGreen"
        case .recentlyActive:
            return "systemOrange"
        case .offline:
            return "clear"
        case .doNotDisturb:
            return "systemPurple"
        }
    }
}

// MARK: - Friendship Model
struct Friendship: Identifiable, Codable {
    let id: String
    let userId: String
    let friendId: String
    let status: FriendshipStatus
    let createdDate: Date
    let acceptedDate: Date?
    let requestMessage: String?
    var unreadMessagesCount: Int
    var lastMessageDate: Date?
    var sharedPhotosCount: Int
    
    var isAccepted: Bool {
        return status == .accepted
    }
    
    var isPending: Bool {
        return status == .pending
    }
}

// MARK: - Friendship Status Enum
enum FriendshipStatus: String, Codable, CaseIterable {
    case pending = "pending"
    case accepted = "accepted"
    case blocked = "blocked"
    case declined = "declined"
}

// MARK: - Friend Request Model
struct FriendRequest: Identifiable, Codable {
    let id: String
    let senderId: String
    let receiverId: String
    let senderInfo: Friend
    let message: String?
    let createdDate: Date
    var status: FriendRequestStatus
    
    var isReceived: Bool {
        // This would be determined by comparing with current user ID
        return true // Placeholder
    }
    
    var isSent: Bool {
        return !isReceived
    }
}

// MARK: - Friend Request Status Enum
enum FriendRequestStatus: String, Codable, CaseIterable {
    case pending = "pending"
    case accepted = "accepted"
    case declined = "declined"
    case cancelled = "cancelled"
}

// MARK: - Friends Response Model
struct FriendsResponse: Codable {
    let friends: [Friend]
    let friendships: [Friendship]
    let friendRequests: [FriendRequest]
}

// MARK: - Friend Extensions
extension Friend {
    static var realFriends: [Friend] {
        return RealDataManager.generateRealFriends()
    }

    // Create a Friend from Share.Author for chat purposes
    static func fromShareAuthor(_ author: Share.Author) -> Friend {
        return Friend(
            id: UUID().uuidString,
            userId: author.id,
            username: author.nickname.lowercased().replacingOccurrences(of: " ", with: "_"),
            displayName: author.nickname,
            avatar: author.avatar,
            bio: "User from \(author.region)",
            location: author.region,
            status: .offline,
            lastSeen: Date(),
            mutualFriends: 0,
            friendshipDate: Date(),
            isBlocked: false
        )
    }
}
