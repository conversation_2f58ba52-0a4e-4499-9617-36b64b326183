# BeautySpots Friends - UI Design Specifications

## 🎨 Visual Design Language

### **Design Principles**
- **Feminine Elegance**: Soft curves, gentle shadows, pink accents
- **Social Warmth**: Inviting colors, friendly interactions
- **Modern Simplicity**: Clean layouts, intuitive navigation
- **Emotional Connection**: Delightful animations, personal touches

## 📱 Screen Designs

### 1. **Friends List Screen**
```
┌─────────────────────────────────────┐
│ ◀ Friends                    🔍 ➕  │
├─────────────────────────────────────┤
│ 📱 Friend Requests (2)        >     │
├─────────────────────────────────────┤
│ 🔍 Search friends...               │
├─────────────────────────────────────┤
│ Online (3)                          │
│ ● Emma_Wanderlust        💬 📷      │
│ ● SophiaBeachVibes       💬 📷      │
│ ● ChloeCityLife          💬 📷      │
├─────────────────────────────────────┤
│ Recently Active (2)                 │
│ ◐ LilyNatureLover        💬 📷      │
│ ◐ GraceCozyMoments       💬 📷      │
├─────────────────────────────────────┤
│ All Friends (12)                    │
│   RoseGardenDreams       💬 📷      │
│   MiaFashionista         💬 📷      │
│   ...                               │
└─────────────────────────────────────┘
```

### 2. **Chat Interface Screen**
```
┌─────────────────────────────────────┐
│ ◀ Emma_Wanderlust           📷 ℹ️   │
├─────────────────────────────────────┤
│                                     │
│     ┌─────────────────────┐         │
│     │ Hey! Check out this │         │
│     │ amazing sunset! 🌅  │         │
│     └─────────────────────┘         │
│                        Emma • 2m    │
│                                     │
│ ┌─────────────────────┐             │
│ │ Absolutely gorgeous!│             │
│ │ Where was this? ✨  │             │
│ └─────────────────────┘             │
│                   You • 1m          │
│                                     │
│     ┌─────────────────────┐         │
│     │ [📷 Shared Photo]   │         │
│     │ Malibu Beach! 🏖️    │         │
│     └─────────────────────┘         │
│                        Emma • now   │
├─────────────────────────────────────┤
│ 💬 Type a message...        📷 😊  │
└─────────────────────────────────────┘
```

### 3. **Photo Sharing Screen**
```
┌─────────────────────────────────────┐
│ ◀ Share Photo                    ✓  │
├─────────────────────────────────────┤
│ ┌─────────────────────────────────┐ │
│ │                                 │ │
│ │        [Selected Photo]         │ │
│ │                                 │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ 💬 Add a caption...                │
├─────────────────────────────────────┤
│ Share with:                         │
│ ☑️ Emma_Wanderlust                  │
│ ☑️ SophiaBeachVibes                 │
│ ☐ ChloeCityLife                     │
│ ☐ LilyNatureLover                   │
├─────────────────────────────────────┤
│ ⏰ Expires: Never ▼                │
├─────────────────────────────────────┤
│           [Send Photo] 💕           │
└─────────────────────────────────────┘
```

### 4. **Friend Requests Screen**
```
┌─────────────────────────────────────┐
│ ◀ Friend Requests                   │
├─────────────────────────────────────┤
│ Received (2)                        │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 👤 MiaFashionista               │ │
│ │ Fashion lover from NYC 🗽       │ │
│ │ 5 mutual friends                │ │
│ │                                 │ │
│ │    [Accept] 💕    [Decline]     │ │
│ └─────────────────────────────────┘ │
│                                     │
│ ┌─────────────────────────────────┐ │
│ │ 👤 ZoeArtistic                  │ │
│ │ Artist & dreamer ✨             │ │
│ │ 2 mutual friends                │ │
│ │                                 │ │
│ │    [Accept] 💕    [Decline]     │ │
│ └─────────────────────────────────┘ │
├─────────────────────────────────────┤
│ Sent (1)                            │
│ 👤 IsabellaTravels - Pending...     │
└─────────────────────────────────────┘
```

## 🎯 Interactive Elements

### **Button Styles**
- **Primary Action**: Pink gradient background, white text, rounded corners
- **Secondary Action**: Pink border, pink text, transparent background
- **Destructive Action**: Red text, transparent background
- **Icon Buttons**: Circular, subtle shadow, pink tint when active

### **Status Indicators**
- **Online**: Solid green circle (8pt diameter)
- **Recently Active**: Orange circle with fade animation
- **Typing**: Three animated dots in pink
- **Message Status**: Checkmarks (sent ✓, delivered ✓✓, read 💕)

### **Animations**
```swift
// Message send animation
UIView.animate(withDuration: 0.3, delay: 0, 
               usingSpringWithDamping: 0.7, 
               initialSpringVelocity: 0.5) {
    messageView.transform = CGAffineTransform(scaleX: 1.1, y: 1.1)
} completion: { _ in
    UIView.animate(withDuration: 0.2) {
        messageView.transform = .identity
    }
}

// Friend request acceptance
UIView.animate(withDuration: 0.5, delay: 0,
               usingSpringWithDamping: 0.6,
               initialSpringVelocity: 0.8) {
    requestCard.transform = CGAffineTransform(translationX: view.bounds.width, y: 0)
    requestCard.alpha = 0
}
```

## 🌈 Color Specifications

### **Primary Colors**
- **Pink Primary**: `#FF69B4` (systemPink)
- **Pink Light**: `#FFB6C1` (systemPink.withAlphaComponent(0.3))
- **Pink Dark**: `#C71585` (darker pink for pressed states)

### **Status Colors**
- **Online Green**: `#32CD32` (systemGreen)
- **Active Orange**: `#FF8C00` (systemOrange)
- **Offline Gray**: `#A9A9A9` (systemGray)

### **Message Bubbles**
- **Sent Messages**: Pink gradient (`#FF69B4` to `#FFB6C1`)
- **Received Messages**: Light gray (`#F0F0F0`)
- **System Messages**: Ultra light gray (`#F8F8F8`)

## 📐 Layout Specifications

### **Spacing System**
- **Micro**: 4pt (tight spacing)
- **Small**: 8pt (component spacing)
- **Medium**: 16pt (section spacing)
- **Large**: 24pt (screen margins)
- **XLarge**: 32pt (major sections)

### **Typography Scale**
- **Title**: 28pt, Bold (screen titles)
- **Headline**: 22pt, Semibold (section headers)
- **Body**: 16pt, Regular (main content)
- **Caption**: 14pt, Medium (metadata)
- **Small**: 12pt, Regular (timestamps)

### **Component Sizes**
- **Avatar**: 40pt (friends list), 32pt (chat), 60pt (profile)
- **Button Height**: 44pt (minimum touch target)
- **Cell Height**: 64pt (friends list), variable (chat)
- **Tab Bar**: 83pt (including safe area)

## 🎭 Accessibility

### **VoiceOver Support**
- Descriptive labels for all interactive elements
- Status announcements for friend activity
- Message content reading with sender identification
- Photo descriptions for shared images

### **Dynamic Type**
- All text scales with user preferences
- Minimum touch targets maintained at all sizes
- Layout adapts to larger text sizes
- Icons scale appropriately

### **Color Accessibility**
- High contrast mode support
- Alternative indicators for color-blind users
- Status symbols in addition to colors
- Clear visual hierarchy

---

**BeautySpots Friends UI** - *Designed for connection* ✨

*This design specification ensures a cohesive, elegant, and accessible user experience that encourages meaningful social interactions within the BeautySpots community.*
