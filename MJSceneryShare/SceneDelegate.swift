//
//  SceneDelegate.swift
//  BeautySpots
//
//  Created by lian<PERSON><PERSON><PERSON> on 2025/5/19.
//

import UIKit

// BeautySpots - AI-powered lifestyle sharing app for women
class SceneDelegate: UIResponder, UIWindowSceneDelegate {

    var window: UIWindow?


    func scene(_ scene: UIScene, willConnectTo session: UISceneSession, options connectionOptions: UIScene.ConnectionOptions) {
        // Use this method to optionally configure and attach the UIWindow `window` to the provided UIWindowScene `scene`.
        // If using a storyboard, the `window` property will automatically be initialized and attached to the scene.
        // This delegate does not imply the connecting scene or session are new (see `application:configurationForConnectingSceneSession` instead).
        guard let windowScene = (scene as? UIWindowScene) else { return }
        
        let window = UIWindow(windowScene: windowScene)

        // Always start with main app - no forced login required
        // Users can browse content without logging in
        window.rootViewController = MainTabBarController()
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5, execute: {
            // 2. 主动弹出网络请求权限弹框
            self.checkNetWorkPermission()
        })

        self.window = window
        window.makeKeyAndVisible()
    }
    
    func checkNetWorkPermission() {
        // 创建一个URL对象，这里我们使用Apple的官网作为示例
        guard let url = URL(string: "https://www.apple.com") else { return }
        
        // 创建一个网络请求任务
        let task = URLSession.shared.dataTask(with: url) { _, _, _ in
            // 这个闭包会在网络请求完成后执行。
            // 我们只是为了触发权限弹窗，所以这里不需要写任何代码。
        }
        
        // 启动任务
        task.resume()
    }

    func sceneDidDisconnect(_ scene: UIScene) {
        // Called as the scene is being released by the system.
        // This occurs shortly after the scene enters the background, or when its session is discarded.
        // Release any resources associated with this scene that can be re-created the next time the scene connects.
        // The scene may re-connect later, as its session was not necessarily discarded (see `application:didDiscardSceneSessions` instead).
    }

    func sceneDidBecomeActive(_ scene: UIScene) {
        // Called when the scene has moved from an inactive state to an active state.
        // Use this method to restart any tasks that were paused (or not yet started) when the scene was inactive.
    }

    func sceneWillResignActive(_ scene: UIScene) {
        // Called when the scene will move from an active state to an inactive state.
        // This may occur due to temporary interruptions (ex. an incoming phone call).
    }

    func sceneWillEnterForeground(_ scene: UIScene) {
        // Called as the scene transitions from the background to the foreground.
        // Use this method to undo the changes made on entering the background.
    }

    func sceneDidEnterBackground(_ scene: UIScene) {
        // Called as the scene transitions from the foreground to the background.
        // Use this method to save data, release shared resources, and store enough scene-specific state information
        // to restore the scene back to its current state.
    }


}

