/* 
  Localizable.strings
  BeautySpots

  English localization strings for BeautySpots app
*/

// MARK: - Tab Bar
"tab.discover" = "Discover";
"tab.post" = "Post";
"tab.friends" = "Friends";
"tab.profile" = "Profile";

// MARK: - Navigation Titles
"nav.discover" = "Discover";
"nav.profile" = "Profile";
"nav.about" = "About";
"nav.getCoins" = "Get Coins";

// MARK: - Authentication
"auth.appName" = "BeautySpots";
"auth.subtitle" = "Share Your Beautiful Moments ✨";
"auth.guestLogin" = "Continue as Guest";

// MARK: - Profile Section
"profile.account" = "Account";
"profile.app" = "App";
"profile.personalInfo" = "Personal Info";
"profile.settings" = "Settings";
"profile.getCoins" = "Get Coins";
"profile.about" = "About";
"profile.feedback" = "Feedback";
"profile.privacy" = "Privacy Policy";
"profile.terms" = "Terms of Service";
"profile.signOut" = "Sign Out";

// MARK: - Alerts
"alert.signOut.title" = "Sign Out";
"alert.signOut.message" = "Are you sure you want to sign out?";
"alert.cancel" = "Cancel";
"alert.ok" = "OK";
"alert.awesome" = "Awesome!";

"alert.comments.title" = "Comments";
"alert.comments.message" = "Comments feature coming soon! 💬✨";

"alert.purchase.title" = "Purchase Confirmation";
"alert.purchase.message" = "Would you like to purchase %@?";
"alert.purchase.button" = "Purchase";
"alert.purchase.success.title" = "Purchase Successful! ✨";
"alert.purchase.success.message" = "You've successfully purchased %@";

"alert.save.failed" = "Save Failed";
"alert.save.success.title" = "Saved Successfully! ✨";
"alert.save.success.message" = "Photo has been saved to your camera roll";
"alert.save.permission.title" = "Cannot Save Photo";
"alert.save.permission.message" = "Please allow photo library access in Settings";

// MARK: - About Screen
"about.appName" = "BeautySpots";
"about.version" = "Version %@";
"about.description" = "Discover and share beautiful lifestyle moments with a community of inspiring women ✨";
"about.contact" = "Contact: <EMAIL>";

// MARK: - Purchase Screen
"purchase.currentBalance" = "Current Balance: %d coins ✨";
"purchase.header" = "Choose Your Coin Package";
"purchase.footer" = "Coins can be used for premium features and exclusive content ✨";
"purchase.coins" = "%d Coins ✨";

// MARK: - User
"user.guest" = "Guest User";

// MARK: - Permissions
"permission.photos.add" = "BeautySpots would like to save beautiful photos to your camera roll so you can keep your favorite moments forever ✨";
"permission.photos.access" = "BeautySpots needs access to your photo library to save and share beautiful lifestyle moments";
