//
//  APIConfiguration.swift
//  BeautySpots
//
//  Created by AI Assistant on 2025-07-04.
//

import Foundation

struct APIConfiguration {
    
    // MARK: - Gemini AI Configuration
    static var geminiAPIKey: String {
        // Primary API key for Google Generative AI
        let primaryAPIKey = "AIzaSyCIwAx1SLS6GFfi-_k_R1EBKK_2eddDFuc"

        print("🔑 APIConfiguration: Using primary Gemini API key")
        print("🔑 API Key length: \(primaryAPIKey.count) characters")
        print("🔑 API Key prefix: \(String(primaryAPIKey.prefix(10)))...")

        // Try to get the API key from the environment first (for production)
        if let apiKey = Bundle.main.infoDictionary?["GEMINI_API_KEY"] as? String, !apiKey.isEmpty {
            print("🔑 APIConfiguration: Using API key from Bundle Info.plist")
            return apiKey
        }

        // Try to get the API key from UserDefaults (for development/testing)
        if let apiKey = UserDefaults.standard.string(forKey: "GEMINI_API_KEY"), !apiKey.isEmpty {
            print("🔑 APIConfiguration: Using API key from UserDefaults")
            return apiKey
        }

        // Return the primary API key
        print("🔑 APIConfiguration: Using hardcoded primary API key")
        return primaryAPIKey
    }
    
    // MARK: - API Endpoints
    struct Endpoints {
        static let baseURL = "https://api.example.com"
        static let imageUpload = "/api/v1/images/upload"
        static let imageAnalysis = "/api/v1/images/analyze"
    }
    
    // MARK: - Configuration Methods
    static func isGeminiAPIAvailable() -> Bool {
        let isAvailable = !geminiAPIKey.isEmpty
        print("🔍 APIConfiguration: Gemini API available: \(isAvailable)")
        return isAvailable
    }
    
    static func setGeminiAPIKey(_ key: String) {
        UserDefaults.standard.set(key, forKey: "GEMINI_API_KEY")
    }
    
    static func clearGeminiAPIKey() {
        UserDefaults.standard.removeObject(forKey: "GEMINI_API_KEY")
    }
}

// MARK: - Development Helper
#if DEBUG
extension APIConfiguration {
    static func setupDevelopmentAPIKey() {
        print("🔧 Setting up development API key...")
        // This is for development only - in production, use proper secure storage
        let devAPIKey = "AIzaSyCIwAx1SLS6GFfi-_k_R1EBKK_2eddDFuc"
        setGeminiAPIKey(devAPIKey)
        print("✅ Development API key set in UserDefaults")
        print("🔑 Key length: \(devAPIKey.count) characters")
    }

    static func testAPIKeyAccess() {
        print("🧪 Testing API key access...")
        let key = geminiAPIKey
        print("🔑 Retrieved API key length: \(key.count)")
        print("🔑 API available: \(isGeminiAPIAvailable())")
    }
}
#endif
