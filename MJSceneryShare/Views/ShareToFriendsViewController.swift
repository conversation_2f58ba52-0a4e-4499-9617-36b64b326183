import UIKit
import SnapKit

protocol ShareToFriendsViewControllerDelegate: AnyObject {
    func shareToFriendsViewController(_ controller: ShareToFriendsViewController, didShareTo friends: [Friend])
    func shareToFriendsViewController(_ controller: ShareToFriendsViewController, shouldNavigateToChatWith friend: Friend)
}

class ShareToFriendsViewController: UIViewController {
    
    // MARK: - Properties
    private let shareContent: Share
    private var friends: [Friend] = []
    private var selectedFriends: Set<String> = []
    weak var delegate: ShareToFriendsViewControllerDelegate?
    
    // MARK: - UI Components
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = .systemGroupedBackground
        tableView.register(ShareToFriendCell.self, forCellReuseIdentifier: ShareToFriendCell.identifier)
        tableView.allowsMultipleSelection = true
        return tableView
    }()
    
    private lazy var shareButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Share to Selected Friends", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemPink
        button.layer.cornerRadius = 25
        button.titleLabel?.font = .systemFont(ofSize: 18, weight: .semibold)
        button.addTarget(self, action: #selector(shareButtonTapped), for: .touchUpInside)
        button.isEnabled = false
        button.alpha = 0.6
        return button
    }()
    
    private lazy var emptyStateView: UIView = {
        let view = UIView()
        view.isHidden = true
        
        let imageView = UIImageView(image: UIImage(systemName: "person.2.slash"))
        imageView.tintColor = .systemGray3
        imageView.contentMode = .scaleAspectFit
        
        let titleLabel = UILabel()
        titleLabel.text = "No Friends Yet"
        titleLabel.font = .systemFont(ofSize: 20, weight: .semibold)
        titleLabel.textColor = .systemGray2
        titleLabel.textAlignment = .center
        
        let subtitleLabel = UILabel()
        subtitleLabel.text = "Add friends to share content with them"
        subtitleLabel.font = .systemFont(ofSize: 16)
        subtitleLabel.textColor = .systemGray3
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        
        view.addSubview(imageView)
        view.addSubview(titleLabel)
        view.addSubview(subtitleLabel)
        
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview()
            make.width.height.equalTo(80)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(20)
            make.bottom.equalToSuperview()
        }
        
        return view
    }()
    
    // MARK: - Initialization
    init(shareContent: Share) {
        self.shareContent = shareContent
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        loadFriends()
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        
        view.addSubview(tableView)
        view.addSubview(shareButton)
        view.addSubview(emptyStateView)
    }
    
    private func setupConstraints() {
        tableView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(shareButton.snp.top).offset(-16)
        }
        
        shareButton.snp.makeConstraints { make in
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-16)
            make.height.equalTo(50)
        }
        
        emptyStateView.snp.makeConstraints { make in
            make.center.equalTo(tableView)
            make.left.right.equalToSuperview().inset(40)
        }
    }
    
    private func setupNavigationBar() {
        title = "Share to Friends"
        navigationController?.navigationBar.prefersLargeTitles = true
        
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelTapped)
        )
    }
    
    // MARK: - Data Methods
    private func loadFriends() {
        friends = FriendsManager.shared.getAllFriends()
        updateUI()
    }
    
    private func updateUI() {
        let isEmpty = friends.isEmpty
        emptyStateView.isHidden = !isEmpty
        tableView.isHidden = isEmpty
        shareButton.isHidden = isEmpty
        
        if !isEmpty {
            tableView.reloadData()
        }
    }
    
    private func updateShareButton() {
        let hasSelection = !selectedFriends.isEmpty
        shareButton.isEnabled = hasSelection
        shareButton.alpha = hasSelection ? 1.0 : 0.6
        
        let count = selectedFriends.count
        if count == 0 {
            shareButton.setTitle("Share to Selected Friends", for: .normal)
        } else if count == 1 {
            shareButton.setTitle("Share to 1 Friend", for: .normal)
        } else {
            shareButton.setTitle("Share to \(count) Friends", for: .normal)
        }
    }
    
    // MARK: - Actions
    @objc private func cancelTapped() {
        dismiss(animated: true)
    }
    
    @objc private func shareButtonTapped() {
        let selectedFriendObjects = friends.filter { selectedFriends.contains($0.userId) }

        // Share content to selected friends
        shareContentToFriends(selectedFriendObjects)

        // Save reference to presenting view controller before dismissing
        let presentingVC = self.presentingViewController

        // Dismiss the share interface and navigate to chat
        dismiss(animated: true) {
            // Notify delegate about successful sharing
            self.delegate?.shareToFriendsViewController(self, didShareTo: selectedFriendObjects)

            // Navigate to chat with the first friend through delegate
            if let firstFriend = selectedFriendObjects.first {
                self.delegate?.shareToFriendsViewController(self, shouldNavigateToChatWith: firstFriend)
            }
        }
    }
    
    private func shareContentToFriends(_ friends: [Friend]) {
        let currentUser = AppViewModel.shared.currentUser

        for friend in friends {
            // Send shared content message to friend's chat
            ChatManager.shared.sendSharedContent(
                share: shareContent,
                to: friend,
                from: currentUser
            )

            // Record friend activity
            FriendActivityManager.shared.recordMessageActivity(with: friend.userId)
        }

        print("📤 [ShareToFriends] Shared content '\(shareContent.title)' to \(friends.count) friends")
    }


}

// MARK: - UITableViewDataSource
extension ShareToFriendsViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return friends.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: ShareToFriendCell.identifier, for: indexPath) as! ShareToFriendCell
        let friend = friends[indexPath.row]
        let isSelected = selectedFriends.contains(friend.userId)
        cell.configure(with: friend, isSelected: isSelected)
        return cell
    }
}

// MARK: - UITableViewDelegate
extension ShareToFriendsViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let friend = friends[indexPath.row]
        
        if selectedFriends.contains(friend.userId) {
            selectedFriends.remove(friend.userId)
        } else {
            selectedFriends.insert(friend.userId)
        }
        
        tableView.reloadRows(at: [indexPath], with: .none)
        updateShareButton()
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 70
    }
}
