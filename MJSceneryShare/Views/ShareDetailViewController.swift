import UIKit
import SnapKit
import Photos

class ShareDetailViewController: UIViewController {
    
    private let share: Share
    
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.alwaysBounceVertical = true
        return scrollView
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var imageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.backgroundColor = .systemGray6
        imageView.layer.cornerRadius = 12
        return imageView
    }()

    private lazy var placeholderIconView: UIImageView = {
        let imageView = UIImageView()
        let config = UIImage.SymbolConfiguration(pointSize: 60, weight: .light)
        imageView.image = UIImage(systemName: "mountain.2", withConfiguration: config)
        imageView.tintColor = .systemGray4
        imageView.contentMode = .scaleAspectFit
        imageView.isHidden = true
        return imageView
    }()

    private lazy var saveButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "square.and.arrow.down"), for: .normal)
        button.tintColor = .white
        button.backgroundColor = UIColor.black.withAlphaComponent(0.6)
        button.layer.cornerRadius = 20
        button.addTarget(self, action: #selector(saveButtonTapped), for: .touchUpInside)
        return button
    }()

    // MARK: - New UI Components
    private lazy var authorInfoView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 12
        view.layer.shadowColor = UIColor.systemGray.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 4
        return view
    }()

    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.backgroundColor = .systemGray5
        imageView.layer.cornerRadius = 20
        imageView.layer.borderWidth = 2
        imageView.layer.borderColor = UIColor.systemPink.withAlphaComponent(0.3).cgColor
        imageView.tintColor = .systemPink
        return imageView
    }()

    private lazy var nicknameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .label
        return label
    }()

    private lazy var regionLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .secondaryLabel
        return label
    }()

    private lazy var timeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12)
        label.textColor = .tertiaryLabel
        return label
    }()

    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 20, weight: .bold)
        label.textColor = .label
        label.numberOfLines = 0
        return label
    }()

    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16)
        label.textColor = .label
        label.numberOfLines = 0
        label.lineBreakMode = .byWordWrapping
        return label
    }()



    private lazy var socialInteractionView: SocialInteractionView = {
        let view = SocialInteractionView()
        view.delegate = self
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 12
        view.layer.shadowColor = UIColor.systemGray.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 4
        return view
    }()

    private lazy var shareToFriendsButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Share to Friends", for: .normal)
        button.setImage(UIImage(systemName: "person.2.fill"), for: .normal)
        button.backgroundColor = .systemPink
        button.tintColor = .white
        button.layer.cornerRadius = 25
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .semibold)
        button.addTarget(self, action: #selector(shareToFriendsButtonTapped), for: .touchUpInside)
        return button
    }()
    
    init(share: Share) {
        self.share = share
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()

        setupUI()
        setupConstraints()
        setupNavigationBar()
        loadImage()
        configureContent()
    }
    
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        title = share.title

        view.addSubview(scrollView)
        scrollView.addSubview(contentView)

        // Add all subviews
        contentView.addSubview(imageView)
        imageView.addSubview(placeholderIconView)
        contentView.addSubview(saveButton)
        contentView.addSubview(authorInfoView)
        contentView.addSubview(titleLabel)
        contentView.addSubview(descriptionLabel)
        contentView.addSubview(socialInteractionView)
        contentView.addSubview(shareToFriendsButton)

        // Author info subviews
        authorInfoView.addSubview(avatarImageView)
        authorInfoView.addSubview(nicknameLabel)
        authorInfoView.addSubview(regionLabel)
        authorInfoView.addSubview(timeLabel)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        imageView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview().inset(16)
            make.height.equalTo(300)
        }

        placeholderIconView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(100)
        }

        saveButton.snp.makeConstraints { make in
            make.top.equalTo(imageView).offset(12)
            make.right.equalTo(imageView).offset(-12)
            make.width.height.equalTo(40)
        }

        authorInfoView.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(60)
        }

        avatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(40)
        }

        nicknameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(12)
            make.top.equalTo(avatarImageView).offset(2)
        }

        regionLabel.snp.makeConstraints { make in
            make.left.equalTo(nicknameLabel)
            make.bottom.equalTo(avatarImageView).offset(-2)
        }

        timeLabel.snp.makeConstraints { make in
            make.right.centerY.equalToSuperview()
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(authorInfoView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
        }

        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
        }

        socialInteractionView.snp.makeConstraints { make in
            make.top.equalTo(descriptionLabel.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(60)
        }

        shareToFriendsButton.snp.makeConstraints { make in
            make.top.equalTo(socialInteractionView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(50)
            make.bottom.equalToSuperview().offset(-24)
        }
    }

    private func setupNavigationBar() {
        let moreButton = UIBarButtonItem(
            image: UIImage(systemName: "ellipsis.circle"),
            style: .plain,
            target: self,
            action: #selector(moreButtonTapped)
        )
        navigationItem.rightBarButtonItem = moreButton
    }
    
    private func configureContent() {
        // Set avatar using AvatarManager
        avatarImageView.image = AvatarManager.shared.getAvatarImageWithFallback(named: share.author.avatar)

        // Set author info
        nicknameLabel.text = share.author.nickname
        regionLabel.text = share.author.region
        timeLabel.text = formatDate(share.publishTime)

        // Set content
        titleLabel.text = share.title
        descriptionLabel.text = share.description

        // Configure social interaction view
        socialInteractionView.configure(with: share)
    }

    private func loadImage() {
        // Check if it's a local image
        if share.imageURL.hasPrefix("local://") {
            // Local image file - extract filename
            let fileName = String(share.imageURL.dropFirst(8)) // Remove "local://" prefix
            if let image = PostStorageManager.shared.loadImage(fileName: fileName) {
                imageView.image = image
                placeholderIconView.isHidden = true
            } else {
                imageView.image = nil
                placeholderIconView.isHidden = false
            }
        } else if let url = URL(string: share.imageURL) {
            // Use image caching to ensure consistency
            ImageCacheManager.shared.loadImage(from: url) { [weak self] image in
                DispatchQueue.main.async {
                    if let image = image {
                        self?.imageView.image = image
                        self?.placeholderIconView.isHidden = true
                    } else {
                        self?.imageView.image = nil
                        self?.placeholderIconView.isHidden = false
                    }
                }
            }
        }
    }

    private func formatDate(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "en_US")
        return formatter.localizedString(for: date, relativeTo: Date())
    }
    
    @objc private func saveButtonTapped() {
        guard let image = imageView.image else { return }
        
        PHPhotoLibrary.requestAuthorization { status in
            switch status {
            case .authorized, .limited:
                UIImageWriteToSavedPhotosAlbum(image, self, #selector(self.image(_:didFinishSavingWithError:contextInfo:)), nil)
            case .denied, .restricted:
                DispatchQueue.main.async {
                    self.showAlert(title: "Cannot Save Photo", message: "Please allow photo library access in Settings")
                }
            case .notDetermined:
                break
            @unknown default:
                break
            }
        }
    }
    
    @objc private func moreButtonTapped() {
        let alertController = UIAlertController(title: nil, message: nil, preferredStyle: .actionSheet)

        // Report action
        let reportAction = UIAlertAction(title: "Report Content", style: .destructive) { [weak self] _ in
            self?.showReportConfirmation()
        }

        // Block action
        let blockAction = UIAlertAction(title: "Block User", style: .destructive) { [weak self] _ in
            self?.showBlockConfirmation()
        }

        let cancelAction = UIAlertAction(title: "Cancel", style: .cancel)

        alertController.addAction(reportAction)
        alertController.addAction(blockAction)
        alertController.addAction(cancelAction)

        // For iPad support
        if let popover = alertController.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItem
        }

        present(alertController, animated: true)
    }

    @objc private func shareToFriendsButtonTapped() {
        // Open friends selection interface
        let shareToFriendsVC = ShareToFriendsViewController(shareContent: share)
        shareToFriendsVC.delegate = self

        let navController = UINavigationController(rootViewController: shareToFriendsVC)
        navController.modalPresentationStyle = .pageSheet

        // Configure the sheet presentation
        if let sheet = navController.sheetPresentationController {
            sheet.detents = [.medium(), .large()]
            sheet.prefersGrabberVisible = true
            sheet.preferredCornerRadius = 20
        }

        present(navController, animated: true)
    }

    @objc private func image(_ image: UIImage, didFinishSavingWithError error: Error?, contextInfo: UnsafeRawPointer) {
        if let error = error {
            showAlert(title: "Save Failed", message: error.localizedDescription)
        } else {
            showAlert(title: "Saved Successfully! ✨", message: "Photo has been saved to your camera roll")
        }
    }

    private func showReportConfirmation() {
        let alert = UIAlertController(
            title: "Report Content",
            message: "Are you sure you want to report this content? It will be hidden from your feed.",
            preferredStyle: .alert
        )

        let reportAction = UIAlertAction(title: "Report", style: .destructive) { [weak self] _ in
            self?.reportContent()
        }

        let cancelAction = UIAlertAction(title: "Cancel", style: .cancel)

        alert.addAction(reportAction)
        alert.addAction(cancelAction)

        present(alert, animated: true)
    }

    private func showBlockConfirmation() {
        let alert = UIAlertController(
            title: "Block User",
            message: "Are you sure you want to block @\(share.author.nickname)? You won't see their content anymore.",
            preferredStyle: .alert
        )

        let blockAction = UIAlertAction(title: "Block", style: .destructive) { [weak self] _ in
            self?.blockUser()
        }

        let cancelAction = UIAlertAction(title: "Cancel", style: .cancel)

        alert.addAction(blockAction)
        alert.addAction(cancelAction)

        present(alert, animated: true)
    }

    private func reportContent() {
        AppViewModel.shared.reportShare(share.id)

        let successAlert = UIAlertController(
            title: "Content Reported",
            message: "Thank you for your report. This content has been hidden.",
            preferredStyle: .alert
        )

        let okAction = UIAlertAction(title: "OK", style: .default) { [weak self] _ in
            self?.navigationController?.popViewController(animated: true)
        }

        successAlert.addAction(okAction)
        present(successAlert, animated: true)
    }

    private func blockUser() {
        AppViewModel.shared.blockUser(share.author.id)

        let successAlert = UIAlertController(
            title: "User Blocked",
            message: "You have blocked @\(share.author.nickname). Their content will no longer appear.",
            preferredStyle: .alert
        )

        let okAction = UIAlertAction(title: "OK", style: .default) { [weak self] _ in
            self?.navigationController?.popViewController(animated: true)
        }

        successAlert.addAction(okAction)
        present(successAlert, animated: true)
    }

    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - SocialInteractionViewDelegate
extension ShareDetailViewController: SocialInteractionViewDelegate {
    func socialInteractionView(_ view: SocialInteractionView, didTapLike share: Share) {
        AppViewModel.shared.toggleLike(for: share.id)

        // Update the social interaction view
        if let updatedShare = AppViewModel.shared.visibleShares.first(where: { $0.id == share.id }) {
            socialInteractionView.configure(with: updatedShare)
        }
    }

    func socialInteractionView(_ view: SocialInteractionView, didTapComment share: Share) {
        // Check if this is the current user's own post
        let currentUserId = AppViewModel.shared.currentUser.id
        if share.author.id == currentUserId {
            // Show alert that user cannot chat with themselves
            let alert = UIAlertController(
                title: "Cannot Chat",
                message: "You cannot start a chat with yourself. This is your own post!",
                preferredStyle: .alert
            )
            alert.addAction(UIAlertAction(title: "OK", style: .default))
            present(alert, animated: true)
            return
        }

        // Increment chat count when user enters chat
        AppViewModel.shared.incrementChatCount(for: share.id)

        // Create a Friend from the share author and navigate to chat
        let friend = Friend.fromShareAuthor(share.author)

        // Add friend to friends list if not already added
        FriendsManager.shared.addFriendFromShareAuthor(share.author)

        let chatVC = ChatViewController(friend: friend)
        navigationController?.pushViewController(chatVC, animated: true)

        print("💬 [ShareDetailViewController] Opening chat with \(friend.displayName) (ID: \(share.author.id))")
    }

    func socialInteractionView(_ view: SocialInteractionView, didTapShare share: Share) {
        let activityVC = UIActivityViewController(activityItems: [share.title, share.description], applicationActivities: nil)
        present(activityVC, animated: true)
    }

    func socialInteractionView(_ view: SocialInteractionView, didTapFavorite share: Share) {
        AppViewModel.shared.toggleFavorite(for: share.id)

        // Update the social interaction view
        if let updatedShare = AppViewModel.shared.visibleShares.first(where: { $0.id == share.id }) {
            socialInteractionView.configure(with: updatedShare)
        }
    }
}

// MARK: - ShareToFriendsViewControllerDelegate
extension ShareDetailViewController: ShareToFriendsViewControllerDelegate {
    func shareToFriendsViewController(_ controller: ShareToFriendsViewController, didShareTo friends: [Friend]) {
        print("📤 [ShareDetailViewController] Successfully shared content to \(friends.count) friends")

        // Show a brief success toast
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
            let message = friends.count == 1 ?
                "Shared to \(friends.first?.displayName ?? "friend")! 💬" :
                "Shared to \(friends.count) friends! 💬"

            self.showSuccessToast(message: message)
        }
    }

    func shareToFriendsViewController(_ controller: ShareToFriendsViewController, shouldNavigateToChatWith friend: Friend) {
        print("💬 [ShareDetailViewController] Navigating to chat with \(friend.displayName)")

        // Navigate to chat with the selected friend
        let chatVC = ChatViewController(friend: friend)
        navigationController?.pushViewController(chatVC, animated: true)
    }

    private func showSuccessToast(message: String) {
        let toastLabel = UILabel()
        toastLabel.backgroundColor = UIColor.systemGreen.withAlphaComponent(0.9)
        toastLabel.textColor = .white
        toastLabel.font = .systemFont(ofSize: 16, weight: .medium)
        toastLabel.textAlignment = .center
        toastLabel.text = message
        toastLabel.alpha = 0
        toastLabel.layer.cornerRadius = 20
        toastLabel.clipsToBounds = true

        view.addSubview(toastLabel)
        toastLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.bottom.equalTo(view.safeAreaLayoutGuide).offset(-100)
            make.left.right.equalToSuperview().inset(40)
            make.height.equalTo(40)
        }

        UIView.animate(withDuration: 0.3, animations: {
            toastLabel.alpha = 1
        }) { _ in
            UIView.animate(withDuration: 0.3, delay: 1.5, animations: {
                toastLabel.alpha = 0
            }) { _ in
                toastLabel.removeFromSuperview()
            }
        }
    }
}
