import UIKit
import SnapKit
import AuthenticationServices

class LoginViewController: UIViewController {

    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.alwaysBounceVertical = true
        scrollView.showsVerticalScrollIndicator = false
        return scrollView
    }()

    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()

    private lazy var headerView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 20
        view.clipsToBounds = true
        return view
    }()

    private var gradientLayer: CAGradientLayer?

    private lazy var loginTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "Welcome Back"
        label.font = .systemFont(ofSize: 28, weight: .bold)
        label.textAlignment = .center
        label.textColor = .white
        return label
    }()

    private lazy var logoImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "BeautySpots_Logo")
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()

    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "BeautySpots"
        label.font = .systemFont(ofSize: 24, weight: .bold)
        label.textAlignment = .center
        label.textColor = .white
        return label
    }()

    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = "Share Your Beautiful Moments ✨"
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .white.withAlphaComponent(0.9)
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    private lazy var featuresContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        return view
    }()

    private lazy var featuresStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.alignment = .fill
        return stackView
    }()

    private lazy var loginContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 20
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 4)
        view.layer.shadowRadius = 12
        return view
    }()

    private lazy var appleSignInButton: ASAuthorizationAppleIDButton = {
        let button = ASAuthorizationAppleIDButton(type: .signIn, style: .black)
        button.addTarget(self, action: #selector(appleSignInTapped), for: .touchUpInside)
        button.layer.cornerRadius = 12
        return button
    }()

    private lazy var guestLoginButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Continue as Guest", for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 18, weight: .semibold)
        button.backgroundColor = .systemGray6
        button.setTitleColor(.label, for: .normal)
        button.layer.cornerRadius = 12
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.systemGray4.cgColor
        button.addTarget(self, action: #selector(guestLoginTapped), for: .touchUpInside)
        return button
    }()

    private lazy var skipLoginButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Skip for Now", for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        button.setTitleColor(.systemGray, for: .normal)
        button.backgroundColor = .clear
        // 无边框，浅色按钮
        button.addTarget(self, action: #selector(skipLoginTapped), for: .touchUpInside)
        return button
    }()

    private lazy var privacyLabel: UILabel = {
        let label = UILabel()
        label.text = "By continuing, you agree to our Terms of Service and Privacy Policy"
        label.font = .systemFont(ofSize: 12)
        label.textColor = .secondaryLabel
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupFeatures()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        // Update gradient layer frame
        DispatchQueue.main.async {
            self.gradientLayer?.frame = self.headerView.bounds
        }
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        // Ensure gradient is properly sized
        gradientLayer?.frame = headerView.bounds
    }

    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground

        view.addSubview(scrollView)
        scrollView.addSubview(contentView)

        contentView.addSubview(headerView)

        // Create and add gradient layer to header view
        setupGradientBackground()

        headerView.addSubview(loginTitleLabel)
        headerView.addSubview(logoImageView)
        headerView.addSubview(titleLabel)
        headerView.addSubview(subtitleLabel)

        contentView.addSubview(featuresContainerView)
        featuresContainerView.addSubview(featuresStackView)

        contentView.addSubview(loginContainerView)
        loginContainerView.addSubview(appleSignInButton)
        loginContainerView.addSubview(guestLoginButton)
        loginContainerView.addSubview(skipLoginButton)
        loginContainerView.addSubview(privacyLabel)
    }

    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }

        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }

        headerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(200)
        }

        loginTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.right.equalToSuperview().inset(20)
        }

        logoImageView.snp.makeConstraints { make in
            make.top.equalTo(loginTitleLabel.snp.bottom).offset(16)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(80)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(logoImageView.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(20)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(6)
            make.left.right.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }

        featuresContainerView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(20)
        }

        featuresStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }

        loginContainerView.snp.makeConstraints { make in
            make.top.equalTo(featuresContainerView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }

        appleSignInButton.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(24)
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(50)
        }

        guestLoginButton.snp.makeConstraints { make in
            make.top.equalTo(appleSignInButton.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(50)
        }

        skipLoginButton.snp.makeConstraints { make in
            make.top.equalTo(guestLoginButton.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(20)
            make.height.equalTo(40)
        }

        privacyLabel.snp.makeConstraints { make in
            make.top.equalTo(skipLoginButton.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-24)
        }
    }

    private func setupGradientBackground() {
        let gradient = CAGradientLayer()
        gradient.colors = [
            UIColor.systemPink.cgColor,
            UIColor.systemPurple.cgColor
        ]
        gradient.startPoint = CGPoint(x: 0, y: 0)
        gradient.endPoint = CGPoint(x: 1, y: 1)
        gradient.frame = headerView.bounds
        gradient.cornerRadius = 20

        headerView.layer.insertSublayer(gradient, at: 0)
        gradientLayer = gradient
    }

    private func setupFeatures() {
        let features = [
            FeatureItem(
                icon: "camera.viewfinder",
                title: "AI-Powered Photo Analysis",
                description: "Smart image recognition with detailed descriptions and automatic tagging",
                color: .systemBlue
            ),
            FeatureItem(
                icon: "sparkles",
                title: "Smart Caption Generation",
                description: "AI generates perfect captions for your photos automatically",
                color: .systemPurple
            ),
            FeatureItem(
                icon: "person.2.fill",
                title: "Social Sharing",
                description: "Share beautiful moments with friends and discover amazing content",
                color: .systemPink
            )
        ]

        for feature in features {
            let featureView = createFeatureView(feature: feature)
            featuresStackView.addArrangedSubview(featureView)
        }
    }

    private func createFeatureView(feature: FeatureItem) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .systemBackground
        containerView.layer.cornerRadius = 12
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOpacity = 0.05
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowRadius = 4

        let iconBackgroundView = UIView()
        iconBackgroundView.backgroundColor = feature.color.withAlphaComponent(0.1)
        iconBackgroundView.layer.cornerRadius = 20

        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: feature.icon)
        iconImageView.tintColor = feature.color
        iconImageView.contentMode = .scaleAspectFit

        let titleLabel = UILabel()
        titleLabel.text = feature.title
        titleLabel.font = .systemFont(ofSize: 16, weight: .semibold)
        titleLabel.textColor = .label
        titleLabel.numberOfLines = 0

        let descriptionLabel = UILabel()
        descriptionLabel.text = feature.description
        descriptionLabel.font = .systemFont(ofSize: 13)
        descriptionLabel.textColor = .secondaryLabel
        descriptionLabel.numberOfLines = 0

        containerView.addSubview(iconBackgroundView)
        iconBackgroundView.addSubview(iconImageView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(descriptionLabel)

        iconBackgroundView.snp.makeConstraints { make in
            make.left.top.equalToSuperview().offset(12)
            make.width.height.equalTo(40)
        }

        iconImageView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(20)
        }

        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconBackgroundView.snp.right).offset(12)
            make.right.equalToSuperview().offset(-12)
            make.top.equalToSuperview().offset(12)
        }

        descriptionLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.right.equalToSuperview().offset(-12)
            make.top.equalTo(titleLabel.snp.bottom).offset(3)
            make.bottom.equalToSuperview().offset(-12)
        }

        return containerView
    }

    // MARK: - Actions
    @objc private func appleSignInTapped() {
        let request = ASAuthorizationAppleIDProvider().createRequest()
        request.requestedScopes = [.fullName, .email]

        let authorizationController = ASAuthorizationController(authorizationRequests: [request])
        authorizationController.delegate = self
        authorizationController.presentationContextProvider = self
        authorizationController.performRequests()
    }

    @objc private func guestLoginTapped() {
        AppViewModel.shared.login()
        // 登录成功后直接关闭登录页面
        dismiss(animated: true)
    }

    @objc private func skipLoginTapped() {
        // 跳过登录，直接关闭登录页面
        dismiss(animated: true)
    }

    private func navigateToMainApp() {
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            let mainTabBar = MainTabBarController()
            window.rootViewController = mainTabBar

            UIView.transition(with: window, duration: 0.3, options: .transitionCrossDissolve, animations: nil)
            window.makeKeyAndVisible()
        }
    }
}

// MARK: - Feature Item Model
private struct FeatureItem {
    let icon: String
    let title: String
    let description: String
    let color: UIColor
}

// MARK: - Apple Sign In Delegate
extension LoginViewController: ASAuthorizationControllerDelegate {
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            print("🔐 Apple Sign In successful")
            print("   - User ID: \(appleIDCredential.user)")
            print("   - Email: \(appleIDCredential.email ?? "nil")")
            print("   - Full Name: \(appleIDCredential.fullName?.givenName ?? "nil") \(appleIDCredential.fullName?.familyName ?? "nil")")

            // Use AppViewModel's new Apple login method
            AppViewModel.shared.loginWithApple(credential: appleIDCredential)

            // Show success feedback briefly before dismissing
            DispatchQueue.main.asyncAfter(deadline: .now() + 0.3) {
                self.dismiss(animated: true)
            }
        }
    }

    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        print("🔐 Apple Sign In failed: \(error.localizedDescription)")
        print("   - Error domain: \(error._domain)")
        print("   - Error code: \(error._code)")

        // Handle different types of Apple Sign In errors
        var title = "Sign In Failed"
        var message = "Unable to sign in with Apple. Please try again or continue as guest."

        // Handle ASAuthorizationError cases
        if let authError = error as? ASAuthorizationError {
            switch authError.code {
            case .canceled:
                // User canceled the sign in, don't show error
                print("🔐 Apple Sign In was canceled by user")
                return
            case .failed:
                title = "Sign In Failed"
                message = "Apple Sign In failed. Please check your Apple ID settings and try again."
            case .invalidResponse:
                title = "Invalid Response"
                message = "Received invalid response from Apple. Please try again."
            case .notHandled:
                title = "Not Handled"
                message = "Sign in request was not handled. Please try again."
            case .unknown:
                title = "Unknown Error"
                message = "An unknown error occurred. Please check your Apple ID settings and try again."
            default:
                title = "Sign In Error"
                message = "An unexpected error occurred during sign in."
            }
        }

        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)

        alert.addAction(UIAlertAction(title: "Try Again", style: .default) { _ in
            self.appleSignInTapped()
        })

        alert.addAction(UIAlertAction(title: "Continue as Guest", style: .cancel) { _ in
            self.guestLoginTapped()
        })

        present(alert, animated: true)
    }
}

// MARK: - Apple Sign In Presentation Context
extension LoginViewController: ASAuthorizationControllerPresentationContextProviding {
    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        return view.window!
    }
}
