import UIKit
import SnapKit
import Photos

class PhotoSharingViewController: UIViewController {
    
    // MARK: - Properties
    private var selectedImage: UIImage?
    private var selectedFriends: [Friend] = []
    private var allFriends: [Friend] = []
    private let targetFriend: Friend?
    private var imageAnalysisResult: ImageRecognitionResult?
    private var suggestedTags: [ImageTag] = []
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.alwaysBounceVertical = true
        return scrollView
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var imageContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemGray6
        view.layer.cornerRadius = 16
        view.clipsToBounds = true
        return view
    }()
    
    private lazy var selectedImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.backgroundColor = .systemGray5
        return imageView
    }()
    
    private lazy var selectPhotoButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Select Photo", for: .normal)
        button.setTitleColor(.systemPink, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .semibold)
        button.backgroundColor = .systemBackground
        button.layer.cornerRadius = 12
        button.layer.borderWidth = 2
        button.layer.borderColor = UIColor.systemPink.cgColor
        button.addTarget(self, action: #selector(selectPhotoTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var captionTextView: UITextView = {
        let textView = UITextView()
        textView.font = .systemFont(ofSize: 16)
        textView.backgroundColor = .systemBackground
        textView.layer.cornerRadius = 12
        textView.layer.borderWidth = 1
        textView.layer.borderColor = UIColor.systemGray4.cgColor
        textView.textContainerInset = UIEdgeInsets(top: 12, left: 12, bottom: 12, right: 12)
        textView.delegate = self
        return textView
    }()
    
    private lazy var captionPlaceholderLabel: UILabel = {
        let label = UILabel()
        label.text = "Add a caption... ✨"
        label.font = .systemFont(ofSize: 16)
        label.textColor = .placeholderText
        return label
    }()
    


    // MARK: - Image Analysis UI Components
    private lazy var analysisContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 12
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.systemGray4.cgColor
        view.isHidden = true
        return view
    }()

    private lazy var analysisLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .systemGray
        label.text = "Analyzing image..."
        return label
    }()

    private lazy var contentTypeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .label
        label.numberOfLines = 0
        return label
    }()

    private lazy var suggestedCaptionLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .secondaryLabel
        label.numberOfLines = 0
        return label
    }()

    private lazy var tagsCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .horizontal
        layout.minimumInteritemSpacing = 8
        layout.minimumLineSpacing = 8
        layout.estimatedItemSize = UICollectionViewFlowLayout.automaticSize

        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.showsHorizontalScrollIndicator = false
        collectionView.register(TagCell.self, forCellWithReuseIdentifier: "TagCell")
        collectionView.dataSource = self
        collectionView.delegate = self
        return collectionView
    }()

    private lazy var useSuggestedCaptionButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Use Suggested Caption", for: .normal)
        button.setTitleColor(.systemPink, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.addTarget(self, action: #selector(useSuggestedCaptionTapped), for: .touchUpInside)
        button.isHidden = true
        return button
    }()
    
    private lazy var shareButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Share Photo 💕", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemPink
        button.layer.cornerRadius = 25
        button.titleLabel?.font = .systemFont(ofSize: 18, weight: .semibold)
        button.addTarget(self, action: #selector(sharePhotoTapped), for: .touchUpInside)
        button.isEnabled = false
        button.alpha = 0.6
        return button
    }()
    
    // MARK: - Initialization
    init(friend: Friend? = nil) {
        self.targetFriend = friend
        super.init(nibName: nil, bundle: nil)
        
        if let friend = friend {
            selectedFriends = [friend]
        }
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        loadFriends()
        updateShareButtonState()
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        contentView.addSubview(imageContainerView)
        imageContainerView.addSubview(selectedImageView)
        imageContainerView.addSubview(selectPhotoButton)
        
        contentView.addSubview(captionTextView)
        captionTextView.addSubview(captionPlaceholderLabel)

        contentView.addSubview(analysisContainerView)
        analysisContainerView.addSubview(analysisLabel)
        analysisContainerView.addSubview(contentTypeLabel)
        analysisContainerView.addSubview(suggestedCaptionLabel)
        analysisContainerView.addSubview(tagsCollectionView)
        analysisContainerView.addSubview(useSuggestedCaptionButton)


        
        contentView.addSubview(shareButton)
        
        // Add keyboard observers
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillShow), name: UIResponder.keyboardWillShowNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillHide), name: UIResponder.keyboardWillHideNotification, object: nil)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        imageContainerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(250)
        }
        
        selectedImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        selectPhotoButton.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(140)
            make.height.equalTo(44)
        }
        
        captionTextView.snp.makeConstraints { make in
            make.top.equalTo(imageContainerView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(80)
        }
        
        captionPlaceholderLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(12)
            make.left.equalToSuperview().offset(16)
        }

        analysisContainerView.snp.makeConstraints { make in
            make.top.equalTo(captionTextView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.greaterThanOrEqualTo(120)
        }

        analysisLabel.snp.makeConstraints { make in
            make.top.left.equalToSuperview().offset(12)
            make.right.equalToSuperview().offset(-12)
        }

        contentTypeLabel.snp.makeConstraints { make in
            make.top.equalTo(analysisLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(12)
        }

        suggestedCaptionLabel.snp.makeConstraints { make in
            make.top.equalTo(contentTypeLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(12)
        }

        tagsCollectionView.snp.makeConstraints { make in
            make.top.equalTo(suggestedCaptionLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(12)
            make.height.equalTo(30)
        }

        useSuggestedCaptionButton.snp.makeConstraints { make in
            make.top.equalTo(tagsCollectionView.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(12)
            make.bottom.equalToSuperview().offset(-12)
        }

        shareButton.snp.makeConstraints { make in
            make.top.equalTo(analysisContainerView.snp.bottom).offset(30)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(50)
            make.bottom.equalToSuperview().offset(-30)
        }
    }
    
    private func setupNavigationBar() {
        title = targetFriend != nil ? "Share to \(targetFriend!.displayName)" : "Share Photo"
        navigationController?.navigationBar.tintColor = .systemPink
        
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelTapped)
        )
    }
    
    private func loadFriends() {
        allFriends = Friend.realFriends
    }
    
    private func updateShareButtonState() {
        let canShare = selectedImage != nil && !selectedFriends.isEmpty
        shareButton.isEnabled = canShare
        shareButton.alpha = canShare ? 1.0 : 0.6
    }
    
    // MARK: - Actions
    @objc private func selectPhotoTapped() {
        let alert = UIAlertController(title: "Select Photo", message: nil, preferredStyle: .actionSheet)
        
        alert.addAction(UIAlertAction(title: "Camera", style: .default) { _ in
            self.presentImagePicker(sourceType: .camera)
        })
        
        alert.addAction(UIAlertAction(title: "Photo Library", style: .default) { _ in
            self.presentImagePicker(sourceType: .photoLibrary)
        })
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        if let popover = alert.popoverPresentationController {
            popover.sourceView = selectPhotoButton
            popover.sourceRect = selectPhotoButton.bounds
        }
        
        present(alert, animated: true)
    }
    
    @objc private func sharePhotoTapped() {
        guard let image = selectedImage else { return }

        // Create shared photo objects
        let caption = captionTextView.text.isEmpty ? nil : captionTextView.text

        // Simulate sharing process
        shareButton.isEnabled = false
        shareButton.setTitle("Sharing... ✨", for: .normal)

        // Add loading animation
        let activityIndicator = UIActivityIndicatorView(style: .medium)
        activityIndicator.color = .white
        shareButton.addSubview(activityIndicator)
        activityIndicator.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
        }
        activityIndicator.startAnimating()

        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            // Record activity with all selected friends
            for friend in self.selectedFriends {
                FriendActivityManager.shared.recordMessageActivity(with: friend.userId)
            }

            activityIndicator.removeFromSuperview()
            self.showSuccessAndDismiss()
        }
    }
    
    @objc private func cancelTapped() {
        dismiss(animated: true)
    }
    
    // MARK: - Helper Methods
    private func presentImagePicker(sourceType: UIImagePickerController.SourceType) {
        guard UIImagePickerController.isSourceTypeAvailable(sourceType) else { return }
        
        let picker = UIImagePickerController()
        picker.sourceType = sourceType
        picker.delegate = self
        picker.allowsEditing = true
        
        present(picker, animated: true)
    }
    
    private func showSuccessAndDismiss() {
        let alert = UIAlertController(
            title: "Photo Shared! ✨",
            message: "Your photo has been shared with \(selectedFriends.count) friend\(selectedFriends.count > 1 ? "s" : "")",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Awesome!", style: .default) { _ in
            self.dismiss(animated: true)
        })
        
        present(alert, animated: true)
    }
    
    // MARK: - Keyboard Handling
    @objc private func keyboardWillShow(notification: NSNotification) {
        guard let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect else { return }
        
        let keyboardHeight = keyboardFrame.height
        scrollView.contentInset.bottom = keyboardHeight
        scrollView.scrollIndicatorInsets.bottom = keyboardHeight
    }
    
    @objc private func keyboardWillHide(notification: NSNotification) {
        scrollView.contentInset.bottom = 0
        scrollView.scrollIndicatorInsets.bottom = 0
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    // MARK: - Image Analysis Methods
    private func analyzeSelectedImage(_ image: UIImage) {
        analysisContainerView.isHidden = false
        analysisLabel.text = "Analyzing image..."
        contentTypeLabel.text = ""
        suggestedCaptionLabel.text = ""
        useSuggestedCaptionButton.isHidden = true

        Task {
            // Analyze image content
            let result = await ImageContentRecognitionService.shared.analyzeImageContent(image)
            self.imageAnalysisResult = result

            // Get suggested tags
            let tags = ImageTaggingService.shared.getSuggestedTags(for: result)
            self.suggestedTags = tags

            // Generate suggested caption
            let suggestedCaption = await ImageContentRecognitionService.shared.generateCaption(for: image)

            DispatchQueue.main.async {
                self.updateAnalysisUI(with: result, suggestedCaption: suggestedCaption)
            }
        }
    }

    private func updateAnalysisUI(with result: ImageRecognitionResult, suggestedCaption: String) {
        analysisLabel.text = "Analysis complete"
        contentTypeLabel.text = result.displayText
        suggestedCaptionLabel.text = suggestedCaption
        useSuggestedCaptionButton.isHidden = false

        // Reload tags collection view
        tagsCollectionView.reloadData()

        // Add subtle animation
        UIView.animate(withDuration: 0.3) {
            self.analysisContainerView.alpha = 1.0
        }
    }

    @objc private func useSuggestedCaptionTapped() {
        if let suggestedCaption = suggestedCaptionLabel.text, !suggestedCaption.isEmpty {
            captionTextView.text = suggestedCaption
            captionTextView.textColor = .label
        }
    }
}

// MARK: - UIImagePickerControllerDelegate
extension PhotoSharingViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true)

        if let editedImage = info[.editedImage] as? UIImage {
            selectedImage = editedImage
        } else if let originalImage = info[.originalImage] as? UIImage {
            selectedImage = originalImage
        }

        if let image = selectedImage {
            selectedImageView.image = image
            selectPhotoButton.isHidden = true

            // Add tap gesture to change photo
            let tapGesture = UITapGestureRecognizer(target: self, action: #selector(selectPhotoTapped))
            selectedImageView.addGestureRecognizer(tapGesture)
            selectedImageView.isUserInteractionEnabled = true

            // Analyze image content
            analyzeSelectedImage(image)

            updateShareButtonState()
        }
    }

    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true)
    }
}

// MARK: - UITextViewDelegate
extension PhotoSharingViewController: UITextViewDelegate {
    func textViewDidChange(_ textView: UITextView) {
        captionPlaceholderLabel.isHidden = !textView.text.isEmpty
    }

    func textViewDidBeginEditing(_ textView: UITextView) {
        captionPlaceholderLabel.isHidden = true
    }

    func textViewDidEndEditing(_ textView: UITextView) {
        captionPlaceholderLabel.isHidden = !textView.text.isEmpty
    }
}

// MARK: - UICollectionViewDataSource
extension PhotoSharingViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        if collectionView == tagsCollectionView {
            return suggestedTags.count
        }
        return 0
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        if collectionView == tagsCollectionView {
            let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "TagCell", for: indexPath) as! TagCell
            let tag = suggestedTags[indexPath.item]
            cell.configure(with: tag)
            return cell
        }

        return UICollectionViewCell()
    }
}

// MARK: - UICollectionViewDelegate
extension PhotoSharingViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        if collectionView == tagsCollectionView {
            // Handle tag selection
            let tag = suggestedTags[indexPath.item]
            if let cell = collectionView.cellForItem(at: indexPath) as? TagCell {
                cell.setSelected(true)
            }

            // Add tag to caption if not already present
            let currentCaption = captionTextView.text ?? ""
            if !currentCaption.contains(tag.name) {
                let newCaption = currentCaption.isEmpty ? "#\(tag.name)" : "\(currentCaption) #\(tag.name)"
                captionTextView.text = newCaption
                captionTextView.textColor = .label
            }
        }
    }

    func collectionView(_ collectionView: UICollectionView, didDeselectItemAt indexPath: IndexPath) {
        if collectionView == tagsCollectionView {
            // Handle tag deselection
            if let cell = collectionView.cellForItem(at: indexPath) as? TagCell {
                cell.setSelected(false)
            }
        }
    }
}


