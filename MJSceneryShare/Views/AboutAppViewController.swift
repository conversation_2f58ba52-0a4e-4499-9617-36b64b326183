//
//  AboutAppViewController.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-04.
//

import UIKit
import SnapKit

class AboutAppViewController: UIViewController {
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.alwaysBounceVertical = true
        scrollView.backgroundColor = .systemGroupedBackground
        return scrollView
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var headerView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 20
        view.clipsToBounds = true
        return view
    }()

    private var gradientLayer: CAGradientLayer?
    
    private lazy var appIconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(named: "BeautySpots_Logo")
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = .clear
        imageView.layer.cornerRadius = 30
        imageView.clipsToBounds = true
        return imageView
    }()
    
    private lazy var appNameLabel: UILabel = {
        let label = UILabel()
        label.text = "BeautySpots"
        label.font = .systemFont(ofSize: 28, weight: .bold)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    private lazy var versionLabel: UILabel = {
        let label = UILabel()
        let version = Bundle.main.infoDictionary?["CFBundleShortVersionString"] as? String ?? "1.0"
        let build = Bundle.main.infoDictionary?["CFBundleVersion"] as? String ?? "1"
        label.text = "Version \(version) (\(build))"
        label.font = .systemFont(ofSize: 16)
        label.textColor = .white.withAlphaComponent(0.9)
        label.textAlignment = .center
        return label
    }()
    
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.text = "Where Beautiful Moments Are Shared ✨"
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textColor = .white.withAlphaComponent(0.9)
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var featuresContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 8
        return view
    }()
    
    private lazy var featuresStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 16
        return stackView
    }()
    
    private lazy var linksContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 8
        return view
    }()
    
    private lazy var linksStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 0
        return stackView
    }()
    
    private lazy var copyrightLabel: UILabel = {
        let label = UILabel()
        let year = Calendar.current.component(.year, from: Date())
        label.text = "© \(year) BeautySpots. All rights reserved."
        label.font = .systemFont(ofSize: 12)
        label.textColor = .secondaryLabel
        label.textAlignment = .center
        return label
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        setupContent()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        // Update gradient layer frame
        DispatchQueue.main.async {
            self.gradientLayer?.frame = self.headerView.bounds
        }
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        // Ensure gradient is properly sized
        gradientLayer?.frame = headerView.bounds
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        contentView.addSubview(headerView)

        // Create and add gradient layer to header view
        setupGradientBackground()

        headerView.addSubview(appIconImageView)
        headerView.addSubview(appNameLabel)
        headerView.addSubview(versionLabel)
        headerView.addSubview(descriptionLabel)
        
        contentView.addSubview(featuresContainerView)
        featuresContainerView.addSubview(featuresStackView)
        
        contentView.addSubview(linksContainerView)
        linksContainerView.addSubview(linksStackView)
        
        contentView.addSubview(copyrightLabel)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        headerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.right.equalToSuperview().inset(16)
        }
        
        appIconImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(24)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(60)
        }
        
        appNameLabel.snp.makeConstraints { make in
            make.top.equalTo(appIconImageView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(20)
        }
        
        versionLabel.snp.makeConstraints { make in
            make.top.equalTo(appNameLabel.snp.bottom).offset(4)
            make.left.right.equalToSuperview().inset(20)
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(versionLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-24)
        }
        
        featuresContainerView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(16)
        }
        
        featuresStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(20)
        }
        
        linksContainerView.snp.makeConstraints { make in
            make.top.equalTo(featuresContainerView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(16)
        }
        
        linksStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        copyrightLabel.snp.makeConstraints { make in
            make.top.equalTo(linksContainerView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    private func setupGradientBackground() {
        let gradient = CAGradientLayer()
        gradient.colors = [
            UIColor.systemPink.cgColor,
            UIColor.systemPurple.cgColor
        ]
        gradient.startPoint = CGPoint(x: 0, y: 0)
        gradient.endPoint = CGPoint(x: 1, y: 1)
        gradient.frame = headerView.bounds
        gradient.cornerRadius = 20

        headerView.layer.insertSublayer(gradient, at: 0)
        gradientLayer = gradient
    }

    private func setupNavigationBar() {
        title = "About"
        navigationController?.navigationBar.tintColor = .systemPink
    }
    
    private func setupContent() {
        setupFeatures()
        setupLinks()
    }
    
    private func setupFeatures() {
        let titleLabel = UILabel()
        titleLabel.text = "Key Features"
        titleLabel.font = .systemFont(ofSize: 20, weight: .bold)
        titleLabel.textColor = .label
        featuresStackView.addArrangedSubview(titleLabel)
        
        let features = [
            "🤖 AI-powered image analysis and recognition",
            "✨ Smart caption generation for your photos",
            "🏷️ Automatic tagging and categorization",
            "👥 Social sharing with friends and community",
            "💰 Coin system for premium features",
            "📊 Detailed usage analytics and insights",
            "🔒 Privacy-first approach with local storage",
            "🎨 Beautiful and intuitive user interface"
        ]
        
        for feature in features {
            let featureLabel = UILabel()
            featureLabel.text = feature
            featureLabel.font = .systemFont(ofSize: 14)
            featureLabel.textColor = .secondaryLabel
            featureLabel.numberOfLines = 0
            featuresStackView.addArrangedSubview(featureLabel)
        }
    }
    
    private func setupLinks() {
        let links = [
            ("Contact Support", "envelope", { self.contactSupport() }),
            ("Share App", "square.and.arrow.up", { self.shareApp() })
        ]
        
        for (index, (title, icon, action)) in links.enumerated() {
            let linkView = createLinkView(title: title, icon: icon, action: action)
            linksStackView.addArrangedSubview(linkView)
            
            if index < links.count - 1 {
                let separator = UIView()
                separator.backgroundColor = .separator
                separator.snp.makeConstraints { make in
                    make.height.equalTo(0.5)
                }
                linksStackView.addArrangedSubview(separator)
            }
        }
    }
    
    private func createLinkView(title: String, icon: String, action: @escaping () -> Void) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .clear
        
        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: icon)
        iconImageView.tintColor = .systemPink
        iconImageView.contentMode = .scaleAspectFit
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 16)
        titleLabel.textColor = .label
        
        let chevronImageView = UIImageView()
        chevronImageView.image = UIImage(systemName: "chevron.right")
        chevronImageView.tintColor = .systemGray3
        chevronImageView.contentMode = .scaleAspectFit
        
        containerView.addSubview(iconImageView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(chevronImageView)
        
        iconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(20)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconImageView.snp.right).offset(12)
            make.centerY.equalToSuperview()
            make.right.lessThanOrEqualTo(chevronImageView.snp.left).offset(-8)
        }
        
        chevronImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(12)
        }
        
        containerView.snp.makeConstraints { make in
            make.height.equalTo(50)
        }
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(linkTapped(_:)))
        containerView.addGestureRecognizer(tapGesture)
        containerView.isUserInteractionEnabled = true
        containerView.tag = linksStackView.arrangedSubviews.count
        
        // Store action in associated object
        objc_setAssociatedObject(containerView, &AssociatedKeys.action, action, .OBJC_ASSOCIATION_RETAIN)
        
        return containerView
    }
    
    @objc private func linkTapped(_ gesture: UITapGestureRecognizer) {
        guard let containerView = gesture.view,
              let action = objc_getAssociatedObject(containerView, &AssociatedKeys.action) as? () -> Void else {
            return
        }
        action()
    }
    
    // MARK: - Actions

    private func contactSupport() {
        let alert = UIAlertController(
            title: "Contact Support",
            message: "Need help? Contact <NAME_EMAIL>",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "Copy Email", style: .default) { _ in
            UIPasteboard.general.string = "<EMAIL>"
        })
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        present(alert, animated: true)
    }
    
    private func rateApp() {
        let alert = UIAlertController(
            title: "Rate App",
            message: "Enjoying BeautySpots? Please rate us on the App Store!",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "Rate Now", style: .default) { _ in
            // In a real app, this would open the App Store
            print("Opening App Store for rating")
        })
        alert.addAction(UIAlertAction(title: "Later", style: .cancel))
        present(alert, animated: true)
    }
    
    private func shareApp() {
        let shareText = "Check out BeautySpots - Where beautiful moments are shared! ✨"
        let activityVC = UIActivityViewController(activityItems: [shareText], applicationActivities: nil)

        if let popover = activityVC.popoverPresentationController {
            popover.sourceView = view
            popover.sourceRect = view.bounds
        }

        present(activityVC, animated: true)
    }
}

// MARK: - Associated Keys
private struct AssociatedKeys {
    static var action = UnsafeRawPointer(bitPattern: "action".hashValue)!
}
