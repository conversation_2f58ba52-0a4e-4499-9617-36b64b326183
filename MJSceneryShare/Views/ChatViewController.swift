import UIKit
import SnapKit

class ChatViewController: UIViewController {
    
    // MARK: - Properties
    private let friend: Friend
    private var messages: [ChatMessage] = []
    private var isTyping = false
    private var typingTimer: Timer?
    
    // MARK: - UI Components
    private lazy var tableView: UITableView = {
        let tableView = UITableView()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = .systemGroupedBackground
        tableView.separatorStyle = .none
        tableView.register(ChatMessageCell.self, forCellReuseIdentifier: ChatMessageCell.identifier)
        tableView.register(ChatPhotoCell.self, forCellReuseIdentifier: ChatPhotoCell.identifier)
        tableView.register(TypingIndicatorCell.self, forCellReuseIdentifier: TypingIndicatorCell.identifier)
        tableView.keyboardDismissMode = .interactive
        return tableView
    }()
    
    private lazy var inputContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: -2)
        view.layer.shadowRadius = 4
        return view
    }()
    
    private lazy var messageInputView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemGray6
        view.layer.cornerRadius = 20
        return view
    }()
    
    private lazy var messageTextView: UITextView = {
        let textView = UITextView()
        textView.font = .systemFont(ofSize: 16)
        textView.backgroundColor = .clear
        textView.textContainerInset = UIEdgeInsets(top: 8, left: 12, bottom: 8, right: 12)
        textView.delegate = self
        textView.isScrollEnabled = false
        textView.textContainer.lineBreakMode = .byWordWrapping
        return textView
    }()
    
    private lazy var placeholderLabel: UILabel = {
        let label = UILabel()
        label.text = "Type a message..."
        label.font = .systemFont(ofSize: 16)
        label.textColor = .placeholderText
        return label
    }()
    
    private lazy var sendButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "paperplane.fill"), for: .normal)
        button.tintColor = .systemPink
        button.backgroundColor = .systemPink.withAlphaComponent(0.1)
        button.layer.cornerRadius = 18
        button.addTarget(self, action: #selector(sendButtonTapped), for: .touchUpInside)
        button.isEnabled = false
        button.alpha = 0.5
        return button
    }()
    
    private lazy var photoButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "photo"), for: .normal)
        button.tintColor = .systemPink
        button.addTarget(self, action: #selector(photoButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var emojiButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "face.smiling"), for: .normal)
        button.tintColor = .systemPink
        button.addTarget(self, action: #selector(emojiButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var emptyStateView: UIView = {
        let view = UIView()
        view.isHidden = true

        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.alignment = .center
        stackView.spacing = 16

        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: "message.circle")
        iconImageView.tintColor = .systemGray3
        iconImageView.contentMode = .scaleAspectFit

        let titleLabel = UILabel()
        titleLabel.text = "Start a conversation"
        titleLabel.font = .systemFont(ofSize: 18, weight: .medium)
        titleLabel.textColor = .secondaryLabel
        titleLabel.textAlignment = .center

        let subtitleLabel = UILabel()
        subtitleLabel.text = "Send a message to start chatting with \(friend.displayName)"
        subtitleLabel.font = .systemFont(ofSize: 14)
        subtitleLabel.textColor = .tertiaryLabel
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0

        stackView.addArrangedSubview(iconImageView)
        stackView.addArrangedSubview(titleLabel)
        stackView.addArrangedSubview(subtitleLabel)

        view.addSubview(stackView)

        iconImageView.snp.makeConstraints { make in
            make.width.height.equalTo(60)
        }

        stackView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.leading.trailing.equalToSuperview().inset(40)
        }

        return view
    }()
    
    // MARK: - Constraints
    private var inputContainerBottomConstraint: Constraint?
    private var messageInputHeightConstraint: Constraint?
    
    // MARK: - Initialization
    init(friend: Friend) {
        self.friend = friend
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        loadMessages()
        setupKeyboardObservers()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        scrollToBottom(animated: false)
    }
    
    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)
        markMessagesAsRead()
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground

        view.addSubview(tableView)
        view.addSubview(emptyStateView)
        view.addSubview(inputContainerView)

        inputContainerView.addSubview(photoButton)
        inputContainerView.addSubview(messageInputView)
        inputContainerView.addSubview(sendButton)
        inputContainerView.addSubview(emojiButton)

        messageInputView.addSubview(messageTextView)
        messageInputView.addSubview(placeholderLabel)

        // Add tap gesture to dismiss keyboard
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(dismissKeyboard))
        tableView.addGestureRecognizer(tapGesture)
    }
    
    private func setupConstraints() {
        tableView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(inputContainerView.snp.top)
        }

        emptyStateView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.bottom.equalTo(inputContainerView.snp.top)
        }

        inputContainerView.snp.makeConstraints { make in
            make.left.right.equalToSuperview()
            inputContainerBottomConstraint = make.bottom.equalTo(view.safeAreaLayoutGuide).constraint
        }
        
        photoButton.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.bottom.equalTo(messageInputView)
            make.width.height.equalTo(36)
        }
        
        messageInputView.snp.makeConstraints { make in
            make.left.equalTo(photoButton.snp.right).offset(8)
            make.right.equalTo(sendButton.snp.left).offset(-8)
            make.top.equalToSuperview().offset(8)
            make.bottom.equalToSuperview().offset(-34) // Safe area bottom
            messageInputHeightConstraint = make.height.greaterThanOrEqualTo(40).constraint
        }
        
        sendButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalTo(messageInputView)
            make.width.height.equalTo(36)
        }
        
        emojiButton.snp.makeConstraints { make in
            make.right.equalTo(messageInputView).offset(-8)
            make.centerY.equalTo(messageInputView)
            make.width.height.equalTo(24)
        }
        
        messageTextView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 0, left: 0, bottom: 0, right: 32))
        }
        
        placeholderLabel.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
        }
    }
    
    private func setupNavigationBar() {
        title = friend.displayName
        navigationController?.navigationBar.tintColor = .systemPink
        
        // Add friend info button
        let infoButton = UIBarButtonItem(
            image: UIImage(systemName: "info.circle"),
            style: .plain,
            target: self,
            action: #selector(infoButtonTapped)
        )
        
        // Add photo share button
        let sharePhotoButton = UIBarButtonItem(
            image: UIImage(systemName: "photo.badge.plus"),
            style: .plain,
            target: self,
            action: #selector(sharePhotoButtonTapped)
        )
        
        navigationItem.rightBarButtonItems = [infoButton, sharePhotoButton]
    }
    
    private func loadMessages() {
        // Load real messages for this conversation from local storage
        let conversationId = "conv_\(friend.userId)"

        // First check if we have any saved messages for this conversation
        if ChatStorageManager.shared.hasMessages(for: conversationId) {
            // Load saved messages from local storage
            messages = ChatStorageManager.shared.loadMessages(for: conversationId)
            print("💬 [ChatViewController] Loaded \(messages.count) saved messages for conversation: \(conversationId)")
        } else {
            // No saved messages, start with empty conversation
            messages = []
            print("💬 [ChatViewController] Starting new conversation: \(conversationId)")
        }

        DispatchQueue.main.async {
            self.updateEmptyState()
            self.tableView.reloadData()
            self.scrollToBottom(animated: false)
        }
    }

    private func updateEmptyState() {
        let isEmpty = messages.isEmpty
        emptyStateView.isHidden = !isEmpty
        tableView.isHidden = isEmpty
    }

    private func setupKeyboardObservers() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(keyboardWillShow),
            name: UIResponder.keyboardWillShowNotification,
            object: nil
        )
        
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(keyboardWillHide),
            name: UIResponder.keyboardWillHideNotification,
            object: nil
        )
    }
    
    // MARK: - Helper Methods
    private func scrollToBottom(animated: Bool) {
        guard !messages.isEmpty else { return }
        
        let lastIndexPath = IndexPath(row: messages.count - 1, section: 0)
        tableView.scrollToRow(at: lastIndexPath, at: .bottom, animated: animated)
    }
    
    private func updateSendButtonState() {
        let hasText = !messageTextView.text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        sendButton.isEnabled = hasText
        sendButton.alpha = hasText ? 1.0 : 0.5
    }
    
    private func markMessagesAsRead() {
        // Mark unread messages as read
        for i in 0..<messages.count {
            if !messages[i].isRead && !messages[i].isSentByCurrentUser {
                messages[i].isRead = true
            }
        }
    }
    
    private func simulateTypingResponse() {
        // Simulate friend typing
        isTyping = true
        tableView.reloadData()
        scrollToBottom(animated: true)
        
        // Simulate response after delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            self.isTyping = false
            self.addRealResponse()
        }
    }
    
    private func addRealResponse() {
        let responses = [
            "That's amazing! ✨",
            "Love it! 💕",
            "So beautiful! 😍",
            "Thanks for sharing! 🙏",
            "Absolutely gorgeous! 🌟",
            "You're so talented! 👏"
        ]

        let randomResponse = responses.randomElement() ?? "Nice! 😊"

        let responseMessage = ChatMessage(
            id: "msg_\(UUID().uuidString)",
            conversationId: "conv_\(friend.userId)",
            senderId: friend.userId,
            receiverId: AppViewModel.shared.currentUser.id,
            content: randomResponse,
            type: .text,
            timestamp: Date(),
            isRead: false,
            isDelivered: true,
            reactions: [],
            replyToMessageId: nil
        )

        // Add message to local array
        messages.append(responseMessage)

        // Save message to local storage
        ChatStorageManager.shared.saveMessage(responseMessage)

        // Record message activity for friend's response
        FriendActivityManager.shared.recordMessageActivity(with: friend.userId)

        print("💬 [ChatViewController] Friend \(friend.displayName) replied: \(randomResponse)")

        DispatchQueue.main.async {
            self.updateEmptyState()
            self.tableView.reloadData()
            self.scrollToBottom(animated: true)
        }
    }
    
    // MARK: - Actions
    @objc private func sendButtonTapped() {
        guard !messageTextView.text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else { return }

        let messageText = messageTextView.text.trimmingCharacters(in: .whitespacesAndNewlines)

        let newMessage = ChatMessage(
            id: "msg_\(UUID().uuidString)",
            conversationId: "conv_\(friend.userId)",
            senderId: AppViewModel.shared.currentUser.id,
            receiverId: friend.userId,
            content: messageText,
            type: .text,
            timestamp: Date(),
            isRead: false,
            isDelivered: true,
            reactions: [],
            replyToMessageId: nil
        )

        // Add message to local array
        messages.append(newMessage)

        // Save message to local storage
        ChatStorageManager.shared.saveMessage(newMessage)

        // Clear input
        messageTextView.text = ""
        placeholderLabel.isHidden = false
        updateSendButtonState()

        // Record message activity with this friend
        FriendActivityManager.shared.recordMessageActivity(with: friend.userId)

        // Add friend to friends list if not already added
        FriendsManager.shared.addFriend(friend)

        // Update UI
        updateEmptyState()
        tableView.reloadData()
        scrollToBottom(animated: true)

        print("💬 [ChatViewController] Sent message: \(newMessage.id) to \(friend.displayName)")

        // Simulate friend response
        simulateTypingResponse()
    }
    
    @objc private func photoButtonTapped() {
        let photoSharingVC = PhotoSharingViewController(friend: friend)
        let navController = UINavigationController(rootViewController: photoSharingVC)
        present(navController, animated: true)
    }
    
    @objc private func emojiButtonTapped() {
        // Toggle emoji keyboard
        if messageTextView.inputView == nil {
            // Show emoji picker (simplified)
            let alert = UIAlertController(title: "Quick Emojis", message: nil, preferredStyle: .actionSheet)
            
            let emojis = ["😊", "😍", "🥰", "😘", "🤗", "😂", "🤣", "😭", "🥺", "✨", "💕", "❤️", "🔥", "👏", "🙌"]
            
            for emoji in emojis {
                alert.addAction(UIAlertAction(title: emoji, style: .default) { _ in
                    self.messageTextView.text += emoji
                    self.placeholderLabel.isHidden = !self.messageTextView.text.isEmpty
                    self.updateSendButtonState()
                })
            }
            
            alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
            
            if let popover = alert.popoverPresentationController {
                popover.sourceView = emojiButton
                popover.sourceRect = emojiButton.bounds
            }
            
            present(alert, animated: true)
        }
    }
    
    @objc private func infoButtonTapped() {
        let profileVC = FriendProfileViewController(friend: friend)
        navigationController?.pushViewController(profileVC, animated: true)
    }
    
    @objc private func sharePhotoButtonTapped() {
        photoButtonTapped()
    }
    
    @objc private func dismissKeyboard() {
        view.endEditing(true)
    }
    
    // MARK: - Keyboard Handling
    @objc private func keyboardWillShow(notification: NSNotification) {
        guard let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect,
              let duration = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? Double else { return }
        
        let keyboardHeight = keyboardFrame.height
        
        UIView.animate(withDuration: duration) {
            self.inputContainerBottomConstraint?.update(offset: -keyboardHeight)
            self.view.layoutIfNeeded()
        }
        
        scrollToBottom(animated: true)
    }
    
    @objc private func keyboardWillHide(notification: NSNotification) {
        guard let duration = notification.userInfo?[UIResponder.keyboardAnimationDurationUserInfoKey] as? Double else { return }
        
        UIView.animate(withDuration: duration) {
            self.inputContainerBottomConstraint?.update(offset: 0)
            self.view.layoutIfNeeded()
        }
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
        typingTimer?.invalidate()
    }
}

// MARK: - UITableViewDataSource
extension ChatViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return messages.count + (isTyping ? 1 : 0)
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        // Show typing indicator as last row
        if isTyping && indexPath.row == messages.count {
            let cell = tableView.dequeueReusableCell(withIdentifier: TypingIndicatorCell.identifier, for: indexPath) as! TypingIndicatorCell
            cell.configure(with: friend)
            return cell
        }

        let message = messages[indexPath.row]

        switch message.type {
        case .photo:
            let cell = tableView.dequeueReusableCell(withIdentifier: ChatPhotoCell.identifier, for: indexPath) as! ChatPhotoCell
            cell.configure(with: message)
            cell.delegate = self
            return cell
        default:
            let cell = tableView.dequeueReusableCell(withIdentifier: ChatMessageCell.identifier, for: indexPath) as! ChatMessageCell
            cell.configure(with: message)
            cell.delegate = self
            return cell
        }
    }
}

// MARK: - UITableViewDelegate
extension ChatViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        dismissKeyboard()
    }
}

// MARK: - UITextViewDelegate
extension ChatViewController: UITextViewDelegate {
    func textViewDidChange(_ textView: UITextView) {
        placeholderLabel.isHidden = !textView.text.isEmpty
        updateSendButtonState()

        // Auto-resize text view
        let size = textView.sizeThatFits(CGSize(width: textView.frame.width, height: CGFloat.greatestFiniteMagnitude))
        let newHeight = min(max(size.height, 40), 120) // Min 40, max 120

        messageInputHeightConstraint?.update(offset: newHeight)

        UIView.animate(withDuration: 0.1) {
            self.view.layoutIfNeeded()
        }
    }

    func textViewDidBeginEditing(_ textView: UITextView) {
        placeholderLabel.isHidden = true
    }

    func textViewDidEndEditing(_ textView: UITextView) {
        placeholderLabel.isHidden = !textView.text.isEmpty
    }
}

// MARK: - ChatMessageCellDelegate
protocol ChatMessageCellDelegate: AnyObject {
    func chatMessageCell(_ cell: ChatMessageCell, didTapReaction message: ChatMessage)
    func chatMessageCell(_ cell: ChatMessageCell, didLongPress message: ChatMessage)
}

extension ChatViewController: ChatMessageCellDelegate {
    func chatMessageCell(_ cell: ChatMessageCell, didTapReaction message: ChatMessage) {
        // Show reaction picker
        showReactionPicker(for: message)
    }

    func chatMessageCell(_ cell: ChatMessageCell, didLongPress message: ChatMessage) {
        // Show message options
        showMessageOptions(for: message)
    }

    private func showReactionPicker(for message: ChatMessage) {
        let alert = UIAlertController(title: "React to message", message: nil, preferredStyle: .actionSheet)

        for emoji in MessageReaction.availableEmojis {
            alert.addAction(UIAlertAction(title: emoji, style: .default) { _ in
                // Add reaction logic here
                print("Added reaction: \(emoji)")
            })
        }

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        present(alert, animated: true)
    }

    private func showMessageOptions(for message: ChatMessage) {
        let alert = UIAlertController(title: nil, message: nil, preferredStyle: .actionSheet)

        alert.addAction(UIAlertAction(title: "Copy", style: .default) { _ in
            UIPasteboard.general.string = message.content
        })

        if message.isSentByCurrentUser {
            alert.addAction(UIAlertAction(title: "Delete", style: .destructive) { _ in
                // Delete message logic
                print("Delete message")
            })
        }

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        present(alert, animated: true)
    }
}

// MARK: - ChatPhotoCellDelegate
protocol ChatPhotoCellDelegate: AnyObject {
    func chatPhotoCell(_ cell: ChatPhotoCell, didTapPhoto message: ChatMessage)
}

extension ChatViewController: ChatPhotoCellDelegate {
    func chatPhotoCell(_ cell: ChatPhotoCell, didTapPhoto message: ChatMessage) {
        // Show full screen photo
        let photoDetailVC = PhotoDetailViewController(imageURL: message.content)
        let navController = UINavigationController(rootViewController: photoDetailVC)
        present(navController, animated: true)
    }
}
