import UIKit
import SnapKit

class MyFavoritesViewController: UIViewController {
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(ShareTableViewCell.self, forCellReuseIdentifier: ShareTableViewCell.identifier)
        tableView.separatorStyle = .none
        tableView.backgroundColor = .systemGroupedBackground
        tableView.showsVerticalScrollIndicator = false
        return tableView
    }()
    
    private lazy var emptyStateView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "star.slash")
        imageView.tintColor = .systemGray3
        imageView.contentMode = .scaleAspectFit
        
        let titleLabel = UILabel()
        titleLabel.text = "No Favorites Yet"
        titleLabel.font = .systemFont(ofSize: 20, weight: .semibold)
        titleLabel.textColor = .secondaryLabel
        titleLabel.textAlignment = .center
        
        let subtitleLabel = UILabel()
        subtitleLabel.text = "Posts you favorite will appear here"
        subtitleLabel.font = .systemFont(ofSize: 16)
        subtitleLabel.textColor = .tertiaryLabel
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0
        
        view.addSubview(imageView)
        view.addSubview(titleLabel)
        view.addSubview(subtitleLabel)
        
        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(20) // Start from top with reasonable margin
            make.width.height.equalTo(80)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(20)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(20)
            make.bottom.lessThanOrEqualToSuperview().offset(-20) // Ensure content doesn't go beyond view
        }
        
        return view
    }()
    
    private var favoritedShares: [Share] = []
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        setupConstraints()
        setupNavigationBar()
        setupNotifications()
        loadData()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadData()
    }
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        view.addSubview(tableView)
    }
    
    private func setupConstraints() {
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    private func setupNavigationBar() {
        title = "My Favorites"
        navigationController?.navigationBar.tintColor = .systemPink
    }
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(handleDataUpdate),
            name: .sharesDidUpdate,
            object: nil
        )
    }
    
    @objc private func handleDataUpdate() {
        loadData()
    }
    
    private func loadData() {
        favoritedShares = AppViewModel.shared.favoritedShares
        updateUI()
    }
    
    private func updateUI() {
        DispatchQueue.main.async { [weak self] in
            guard let self = self else { return }
            
            if self.favoritedShares.isEmpty {
                self.showEmptyState()
            } else {
                self.hideEmptyState()
            }
            
            self.tableView.reloadData()
        }
    }
    
    private func showEmptyState() {
        // Create a container view to properly center the empty state
        let containerView = UIView()
        containerView.addSubview(emptyStateView)

        emptyStateView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(40)
        }

        tableView.backgroundView = containerView
    }
    
    private func hideEmptyState() {
        tableView.backgroundView = nil
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - UITableViewDataSource
extension MyFavoritesViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return favoritedShares.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: ShareTableViewCell.identifier, for: indexPath) as! ShareTableViewCell
        let share = favoritedShares[indexPath.row]
        cell.configure(with: share)
        cell.delegate = self
        return cell
    }
}

// MARK: - UITableViewDelegate
extension MyFavoritesViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let share = favoritedShares[indexPath.row]
        let detailVC = ShareDetailViewController(share: share)
        navigationController?.pushViewController(detailVC, animated: true)
    }
}

// MARK: - ShareTableViewCellDelegate
extension MyFavoritesViewController: ShareTableViewCellDelegate {
    func shareCell(_ cell: ShareTableViewCell, didTapLike share: Share) {
        AppViewModel.shared.toggleLike(for: share.id)
    }
    
    func shareCell(_ cell: ShareTableViewCell, didTapComment share: Share) {
        let alert = UIAlertController(title: "Comments", message: "Comments feature coming soon! 💬✨", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
    
    func shareCell(_ cell: ShareTableViewCell, didTapShare share: Share) {
        let activityVC = UIActivityViewController(activityItems: [share.title, share.description], applicationActivities: nil)
        present(activityVC, animated: true)
    }
    
    func shareCell(_ cell: ShareTableViewCell, didTapFavorite share: Share) {
        AppViewModel.shared.toggleFavorite(for: share.id)
    }
    
    func shareCell(_ cell: ShareTableViewCell, didTapReport share: Share) {
        // Not implemented for favorited posts
    }
    
    func shareCell(_ cell: ShareTableViewCell, didTapBlock share: Share) {
        // Not implemented for favorited posts
    }
}
