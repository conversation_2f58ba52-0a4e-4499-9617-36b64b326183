//
//  PostDetailViewController.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-04.
//

import UIKit
import SnapKit

class PostDetailViewController: UIViewController {
    
    // MARK: - Properties
    private let post: PostData
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.alwaysBounceVertical = true
        scrollView.backgroundColor = .systemGroupedBackground
        return scrollView
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var imageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 16
        imageView.backgroundColor = .systemGray6
        return imageView
    }()
    
    private lazy var captionContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 8
        return view
    }()
    
    private lazy var captionLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16)
        label.textColor = .label
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var analysisContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 8
        return view
    }()
    
    private lazy var analysisHeaderLabel: UILabel = {
        let label = UILabel()
        label.text = "AI Analysis"
        label.font = .systemFont(ofSize: 18, weight: .bold)
        label.textColor = .label
        return label
    }()
    
    private lazy var contentTypeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .systemPink
        return label
    }()
    
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .secondaryLabel
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var confidenceLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .systemBlue
        return label
    }()
    
    private lazy var moodLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .systemPurple
        return label
    }()
    
    private lazy var colorsLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .systemOrange
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var tagsContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 8
        return view
    }()
    
    private lazy var tagsHeaderLabel: UILabel = {
        let label = UILabel()
        label.text = "Tags"
        label.font = .systemFont(ofSize: 18, weight: .bold)
        label.textColor = .label
        return label
    }()
    
    private lazy var tagsLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .systemBlue
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var metadataContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 8
        return view
    }()
    
    private lazy var metadataHeaderLabel: UILabel = {
        let label = UILabel()
        label.text = "Post Information"
        label.font = .systemFont(ofSize: 18, weight: .bold)
        label.textColor = .label
        return label
    }()
    
    private lazy var timestampLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private lazy var postIdLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .medium)
        label.textColor = .tertiaryLabel
        return label
    }()
    
    // MARK: - Initialization
    init(post: PostData) {
        self.post = post
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        configureWithPost()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        contentView.addSubview(imageView)
        contentView.addSubview(captionContainerView)
        contentView.addSubview(analysisContainerView)
        contentView.addSubview(tagsContainerView)
        contentView.addSubview(metadataContainerView)
        
        captionContainerView.addSubview(captionLabel)
        
        analysisContainerView.addSubview(analysisHeaderLabel)
        analysisContainerView.addSubview(contentTypeLabel)
        analysisContainerView.addSubview(descriptionLabel)
        analysisContainerView.addSubview(confidenceLabel)
        analysisContainerView.addSubview(moodLabel)
        analysisContainerView.addSubview(colorsLabel)
        
        tagsContainerView.addSubview(tagsHeaderLabel)
        tagsContainerView.addSubview(tagsLabel)
        
        metadataContainerView.addSubview(metadataHeaderLabel)
        metadataContainerView.addSubview(timestampLabel)
        metadataContainerView.addSubview(postIdLabel)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        imageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(300)
        }
        
        captionContainerView.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(16)
        }
        
        captionLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }
        
        analysisContainerView.snp.makeConstraints { make in
            make.top.equalTo(captionContainerView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(16)
        }
        
        analysisHeaderLabel.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview().inset(16)
        }
        
        contentTypeLabel.snp.makeConstraints { make in
            make.top.equalTo(analysisHeaderLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(contentTypeLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(16)
        }
        
        confidenceLabel.snp.makeConstraints { make in
            make.top.equalTo(descriptionLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(16)
        }
        
        moodLabel.snp.makeConstraints { make in
            make.top.equalTo(confidenceLabel.snp.bottom).offset(4)
            make.left.right.equalToSuperview().inset(16)
        }
        
        colorsLabel.snp.makeConstraints { make in
            make.top.equalTo(moodLabel.snp.bottom).offset(4)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        tagsContainerView.snp.makeConstraints { make in
            make.top.equalTo(analysisContainerView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(16)
        }
        
        tagsHeaderLabel.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview().inset(16)
        }
        
        tagsLabel.snp.makeConstraints { make in
            make.top.equalTo(tagsHeaderLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        metadataContainerView.snp.makeConstraints { make in
            make.top.equalTo(tagsContainerView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-24)
        }
        
        metadataHeaderLabel.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview().inset(16)
        }
        
        timestampLabel.snp.makeConstraints { make in
            make.top.equalTo(metadataHeaderLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
        }
        
        postIdLabel.snp.makeConstraints { make in
            make.top.equalTo(timestampLabel.snp.bottom).offset(4)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    private func setupNavigationBar() {
        title = "Post Details"
        navigationController?.navigationBar.tintColor = .systemPink
        
        let shareButton = UIBarButtonItem(
            barButtonSystemItem: .action,
            target: self,
            action: #selector(shareButtonTapped)
        )
        
        navigationItem.rightBarButtonItem = shareButton
    }
    
    private func configureWithPost() {
        // Load image
        if let image = post.getImage() {
            imageView.image = image
        } else {
            imageView.image = UIImage(systemName: "photo")
            imageView.tintColor = .systemGray3
        }
        
        // Set caption
        captionLabel.text = post.caption.isEmpty ? "No caption provided" : post.caption
        
        // Set analysis data
        let contentType = ImageContentType(rawValue: post.contentType) ?? .unknown
        contentTypeLabel.text = "\(contentType.emoji) \(post.displayContentType)"
        descriptionLabel.text = post.analysisDescription
        confidenceLabel.text = "Confidence: \(post.confidencePercentage)"
        
        if let mood = post.mood, !mood.isEmpty {
            moodLabel.text = "Mood: \(mood.capitalized)"
        } else {
            moodLabel.text = "Mood: Not detected"
        }
        
        if !post.colors.isEmpty {
            colorsLabel.text = "Colors: \(post.colors.joined(separator: ", "))"
        } else {
            colorsLabel.text = "Colors: Not analyzed"
        }
        
        // Set tags
        if post.tags.isEmpty {
            tagsLabel.text = "No tags added"
            tagsLabel.textColor = .systemGray
        } else {
            tagsLabel.text = post.tags.map { "#\($0)" }.joined(separator: " ")
            tagsLabel.textColor = .systemBlue
        }
        
        // Set metadata
        let formatter = DateFormatter()
        formatter.dateStyle = .full
        formatter.timeStyle = .short
        timestampLabel.text = "Created: \(formatter.string(from: post.timestamp))"
        postIdLabel.text = "ID: \(post.id)"
    }
    
    // MARK: - Actions
    @objc private func shareButtonTapped() {
        guard let image = post.getImage() else { return }
        
        let activityItems: [Any] = [image, post.caption]
        let activityVC = UIActivityViewController(activityItems: activityItems, applicationActivities: nil)
        
        if let popover = activityVC.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItem
        }
        
        present(activityVC, animated: true)
    }
}
