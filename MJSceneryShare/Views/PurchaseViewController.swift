import UIKit
import SnapKit
import StoreKit

class PurchaseViewController: UIViewController {

    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.showsVerticalScrollIndicator = false
        scrollView.contentInsetAdjustmentBehavior = .automatic
        return scrollView
    }()

    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()

    private lazy var headerView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        return view
    }()

    private lazy var gradientLayer: CAGradientLayer = {
        let gradient = CAGradientLayer()
        gradient.colors = [
            UIColor.systemPink.withAlphaComponent(0.1).cgColor,
            UIColor.systemPurple.withAlphaComponent(0.05).cgColor,
            UIColor.clear.cgColor
        ]
        gradient.locations = [0.0, 0.5, 1.0]
        return gradient
    }()

    private lazy var headerStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 12
        stackView.alignment = .center
        return stackView
    }()

    private lazy var currentCoinsLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 32, weight: .bold)
        label.textAlignment = .center
        label.textColor = .systemPink
        return label
    }()

    private lazy var freeProcessingLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textAlignment = .center
        label.textColor = .systemBlue
        label.numberOfLines = 0 // Allow multiple lines
        label.lineBreakMode = .byWordWrapping
        return label
    }()

    private lazy var processingCostLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textAlignment = .center
        label.textColor = .secondaryLabel
        label.text = "💡 AI Processing: \(AIProcessingPricing.fullProcessingCost) coins per image"
        return label
    }()

    private lazy var dailyBonusButton: UIButton = {
        var config = UIButton.Configuration.filled()
        config.title = "🎁 Claim Daily Bonus"
        config.baseBackgroundColor = .systemGreen
        config.baseForegroundColor = .white
        config.cornerStyle = .medium
        config.contentInsets = NSDirectionalEdgeInsets(top: 14, leading: 20, bottom: 14, trailing: 20)

        let button = UIButton(configuration: config)
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .semibold)
        button.layer.shadowColor = UIColor.systemGreen.cgColor
        button.layer.shadowOffset = CGSize(width: 0, height: 2)
        button.layer.shadowRadius = 4
        button.layer.shadowOpacity = 0.3
        button.addTarget(self, action: #selector(claimDailyBonusTapped), for: .touchUpInside)
        return button
    }()

    private lazy var packagesStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 16
        stackView.distribution = .fill
        return stackView
    }()

    private lazy var sectionTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "💰 Choose Your Coin Package"
        label.font = .systemFont(ofSize: 24, weight: .bold)
        label.textColor = .label
        label.textAlignment = .center
        return label
    }()

    private lazy var sectionSubtitleLabel: UILabel = {
        let label = UILabel()
        label.text = "Unlock premium AI features and enhance your photos ✨"
        label.font = .systemFont(ofSize: 16)
        label.textColor = .secondaryLabel
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()

    private let purchasePackages = CoinPurchasePackage.allCases
    private let iapManager = IAPManager.shared

    // Loading state
    private var isLoading = false
    private var loadingOverlay: UIView?
    private var loadingIndicator: UIActivityIndicatorView?
    private var loadingLabel: UILabel?

    // Pending purchase after login
    private var pendingPurchasePackage: CoinPurchasePackage?

    // Test mode for development
    #if DEBUG
    private var isTestMode = false
    #endif

    override func viewDidLoad() {
        super.viewDidLoad()

        setupUI()
        setupConstraints()
        setupNavigationBar()
        setupNotifications()
        setupPackageCards()
        updateCoinsDisplay()
        loadProductPrices()
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        updateCoinsDisplay()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()
        gradientLayer.frame = headerView.bounds
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }

    private func setupUI() {
        view.backgroundColor = .systemBackground

        view.addSubview(scrollView)
        scrollView.addSubview(contentView)

        contentView.addSubview(sectionTitleLabel)
        contentView.addSubview(sectionSubtitleLabel)
        contentView.addSubview(packagesStackView)

        // Setup header based on login status
        setupHeaderBasedOnLoginStatus()
    }

    private func setupHeaderBasedOnLoginStatus() {
        let isLoggedIn = UserAuthenticationManager.shared.isUserLoggedIn()

        if isLoggedIn {
            // Show header with coins and daily bonus for logged in users
            setupLoggedInHeader()
        } else {
            // Hide header for not logged in users
            headerView.isHidden = true
        }
    }

    private func setupLoggedInHeader() {
        // Add gradient to header
        headerView.layer.insertSublayer(gradientLayer, at: 0)

        contentView.addSubview(headerView)
        headerView.addSubview(headerStackView)
        headerStackView.addArrangedSubview(currentCoinsLabel)
        headerStackView.addArrangedSubview(freeProcessingLabel)
        headerStackView.addArrangedSubview(processingCostLabel)
        headerStackView.addArrangedSubview(dailyBonusButton)

        headerView.isHidden = false
    }

    private func setupConstraints() {
        // Only set scroll view and content view constraints once
        if scrollView.constraints.isEmpty {
            scrollView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
            }
        }

        if contentView.constraints.isEmpty {
            contentView.snp.makeConstraints { make in
                make.edges.equalToSuperview()
                make.width.equalToSuperview()
            }
        }

        let isLoggedIn = UserAuthenticationManager.shared.isUserLoggedIn()

        if isLoggedIn {
            // Setup constraints for logged in users (with header)
            setupLoggedInConstraints()
        } else {
            // Setup constraints for not logged in users (without header)
            setupNotLoggedInConstraints()
        }
    }

    private func setupLoggedInConstraints() {
        // Remove existing constraints first
        headerView.snp.removeConstraints()
        headerStackView.snp.removeConstraints()
        dailyBonusButton.snp.removeConstraints()
        sectionTitleLabel.snp.removeConstraints()
        sectionSubtitleLabel.snp.removeConstraints()
        packagesStackView.snp.removeConstraints()

        headerView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(220)
        }

        headerStackView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(20)
        }

        dailyBonusButton.snp.makeConstraints { make in
            make.height.equalTo(48)
        }

        sectionTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(20)
        }

        sectionSubtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(sectionTitleLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(20)
        }

        packagesStackView.snp.makeConstraints { make in
            make.top.equalTo(sectionSubtitleLabel.snp.bottom).offset(24)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().inset(20)
        }
    }

    private func setupNotLoggedInConstraints() {
        // Remove existing constraints first
        sectionTitleLabel.snp.removeConstraints()
        sectionSubtitleLabel.snp.removeConstraints()
        packagesStackView.snp.removeConstraints()

        sectionTitleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(40)
            make.left.right.equalToSuperview().inset(20)
        }

        sectionSubtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(sectionTitleLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(20)
        }

        packagesStackView.snp.makeConstraints { make in
            make.top.equalTo(sectionSubtitleLabel.snp.bottom).offset(24)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().inset(20)
        }
    }
    
    private func setupPackageCards() {
        for package in purchasePackages {
            let cardView = createPackageCard(for: package)
            packagesStackView.addArrangedSubview(cardView)
        }
    }

    private func createPackageCard(for package: CoinPurchasePackage) -> UIView {
        let cardView = UIView()
        cardView.backgroundColor = .secondarySystemGroupedBackground
        cardView.layer.cornerRadius = 16
        cardView.layer.shadowColor = UIColor.black.cgColor
        cardView.layer.shadowOffset = CGSize(width: 0, height: 2)
        cardView.layer.shadowRadius = 8
        cardView.layer.shadowOpacity = 0.1

        // Add special styling for popular/best value packages
        if package.isPopular || package.isBestValue {
            cardView.layer.borderWidth = 2
            cardView.layer.borderColor = package.isPopular ? UIColor.systemOrange.cgColor : UIColor.systemGreen.cgColor
            cardView.layer.shadowOpacity = 0.2
        }

        let contentStack = UIStackView()
        contentStack.axis = .vertical
        contentStack.spacing = 12
        contentStack.alignment = .fill

        // Header with emoji and title
        let headerStack = UIStackView()
        headerStack.axis = .horizontal
        headerStack.spacing = 12
        headerStack.alignment = .center

        let emojiLabel = UILabel()
        emojiLabel.text = package.emoji
        emojiLabel.font = .systemFont(ofSize: 32)

        let titleStack = UIStackView()
        titleStack.axis = .vertical
        titleStack.spacing = 4
        titleStack.alignment = .leading

        let titleLabel = UILabel()
        titleLabel.text = package.title
        titleLabel.font = .systemFont(ofSize: 20, weight: .bold)
        titleLabel.textColor = .label

        let descriptionLabel = UILabel()
        descriptionLabel.text = package.description
        descriptionLabel.font = .systemFont(ofSize: 14)
        descriptionLabel.textColor = .secondaryLabel
        descriptionLabel.numberOfLines = 0

        titleStack.addArrangedSubview(titleLabel)
        titleStack.addArrangedSubview(descriptionLabel)

        headerStack.addArrangedSubview(emojiLabel)
        headerStack.addArrangedSubview(titleStack)
        headerStack.addArrangedSubview(UIView()) // Spacer to push content left

        // Coins info
        let coinsStack = UIStackView()
        coinsStack.axis = .horizontal
        coinsStack.spacing = 8
        coinsStack.alignment = .center

        let coinsLabel = UILabel()
        coinsLabel.text = package.displayTitle
        coinsLabel.font = .systemFont(ofSize: 24, weight: .bold)
        coinsLabel.textColor = .systemPink

        let priceLabel = UILabel()
        priceLabel.text = package.price
        priceLabel.font = .systemFont(ofSize: 18, weight: .semibold)
        priceLabel.textColor = .label

        coinsStack.addArrangedSubview(coinsLabel)
        coinsStack.addArrangedSubview(UIView()) // Spacer
        coinsStack.addArrangedSubview(priceLabel)

        // Features list
        let featuresStack = UIStackView()
        featuresStack.axis = .vertical
        featuresStack.spacing = 6
        featuresStack.alignment = .leading

        for feature in package.features {
            let featureStack = UIStackView()
            featureStack.axis = .horizontal
            featureStack.spacing = 8
            featureStack.alignment = .center

            let checkmarkLabel = UILabel()
            checkmarkLabel.text = "✓"
            checkmarkLabel.font = .systemFont(ofSize: 14, weight: .bold)
            checkmarkLabel.textColor = .systemGreen

            let featureLabel = UILabel()
            featureLabel.text = feature
            featureLabel.font = .systemFont(ofSize: 14)
            featureLabel.textColor = .secondaryLabel
            featureLabel.numberOfLines = 0

            featureStack.addArrangedSubview(checkmarkLabel)
            featureStack.addArrangedSubview(featureLabel)
            featuresStack.addArrangedSubview(featureStack)
        }

        // Purchase button
        let purchaseButton = UIButton()
        var config = UIButton.Configuration.filled()
        config.title = "Purchase Now"
        config.baseBackgroundColor = .systemPink
        config.baseForegroundColor = .white
        config.cornerStyle = .medium
        config.contentInsets = NSDirectionalEdgeInsets(top: 14, leading: 20, bottom: 14, trailing: 20)

        purchaseButton.configuration = config
        purchaseButton.titleLabel?.font = .systemFont(ofSize: 16, weight: .semibold)
        purchaseButton.layer.shadowColor = UIColor.systemPink.cgColor
        purchaseButton.layer.shadowOffset = CGSize(width: 0, height: 2)
        purchaseButton.layer.shadowRadius = 4
        purchaseButton.layer.shadowOpacity = 0.3

        purchaseButton.addTarget(self, action: #selector(purchaseButtonTapped(_:)), for: .touchUpInside)
        purchaseButton.tag = purchasePackages.firstIndex(where: { $0.id == package.id }) ?? 0

        contentStack.addArrangedSubview(headerStack)
        contentStack.addArrangedSubview(coinsStack)
        contentStack.addArrangedSubview(featuresStack)
        contentStack.addArrangedSubview(purchaseButton)

        cardView.addSubview(contentStack)
        contentStack.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(20)
        }

        // Add badge for popular/best value positioned at top-right corner
        if package.isPopular || package.isBestValue {
            let badgeLabel = UILabel()
            badgeLabel.text = package.isPopular ? "🔥 POPULAR" : "💎 BEST VALUE"
            badgeLabel.font = .systemFont(ofSize: 11, weight: .bold)
            badgeLabel.textColor = .white
            badgeLabel.backgroundColor = package.isPopular ? .systemOrange : .systemGreen
            badgeLabel.textAlignment = .center
            badgeLabel.layer.cornerRadius = 12
            badgeLabel.clipsToBounds = true

            cardView.addSubview(badgeLabel)
            badgeLabel.snp.makeConstraints { make in
                make.top.equalToSuperview().offset(8) // 向上移动，距离卡片顶部8px
                make.trailing.equalToSuperview().offset(-8) // 距离右边8px
                make.width.greaterThanOrEqualTo(package.isPopular ? 90 : 100) // POPULAR: 90px, BEST VALUE: 100px
                make.height.equalTo(24) // 固定高度
            }
        }

        return cardView
    }

    private func setupNavigationBar() {
        title = "💰 Get Coins"
        navigationController?.navigationBar.tintColor = .systemPink
        navigationController?.navigationBar.prefersLargeTitles = false

        // Add transaction history button
        let historyButton = UIBarButtonItem(
            image: UIImage(systemName: "clock.arrow.circlepath"),
            style: .plain,
            target: self,
            action: #selector(showTransactionHistory)
        )

        #if DEBUG
        // Add debug menu button in debug builds
        let debugButton = UIBarButtonItem(
            image: UIImage(systemName: "gear"),
            style: .plain,
            target: self,
            action: #selector(showDebugMenu)
        )
        navigationItem.rightBarButtonItems = [historyButton, debugButton]
        #else
        navigationItem.rightBarButtonItem = historyButton
        #endif
    }

    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(coinBalanceDidUpdate),
            name: .coinBalanceDidUpdate,
            object: nil
        )
    }

    private func updateCoinsDisplay() {
        // Only update display if user is logged in
        guard UserAuthenticationManager.shared.isUserLoggedIn() else {
            print("UI Debug: User not logged in, skipping display update")
            return
        }

        let user = AppViewModel.shared.currentUser
        print("UI Debug: Updating display - Coins: \(user.coins), Free AI: \(user.freeProcessingCount), Purchased AI: \(user.purchasedProcessingCount), Total AI: \(user.totalAvailableProcessing)")
        print("UI Debug: User details - ID: \(user.id), LoginType: \(user.loginType ?? "nil"), IsGuestUser: \(user.isGuestUser), IsAppleUser: \(user.isAppleUser)")
        currentCoinsLabel.text = "\(user.coins) ✨"

        // Show total available AI processing count with simplified text
        let totalProcessing = user.totalAvailableProcessing
        if totalProcessing > 0 {
            if user.freeProcessingCount > 0 && user.purchasedProcessingCount > 0 {
                // Users with both free and purchased processing
                freeProcessingLabel.text = "🤖 \(totalProcessing) AI analyses\n(\(user.freeProcessingCount) free + \(user.purchasedProcessingCount) paid)"
            } else if user.freeProcessingCount > 0 {
                // Only free processing available
                freeProcessingLabel.text = "🆓 \(user.freeProcessingCount) free AI analyses"
            } else {
                // Only purchased processing available
                freeProcessingLabel.text = "🤖 \(totalProcessing) AI analyses"
            }
            freeProcessingLabel.isHidden = false
        } else {
            freeProcessingLabel.text = "🤖 No AI analyses\nPurchase to unlock"
            freeProcessingLabel.isHidden = false
        }

        // Update daily bonus button
        dailyBonusButton.isHidden = !user.canClaimDailyBonus
        if user.canClaimDailyBonus {
            dailyBonusButton.configuration?.title = "🎁 Claim Daily Bonus (+\(CoinSystemConfig.dailyBonusAmount) coins)"
        }
    }

    @objc private func coinBalanceDidUpdate() {
        DispatchQueue.main.async {
            self.updateCoinsDisplay()
        }
    }

    @objc private func purchaseButtonTapped(_ sender: UIButton) {
        // Prevent multiple taps during loading
        guard !isLoading else { return }

        let package = purchasePackages[sender.tag]

        // Check login status before purchase
        if UserAuthenticationManager.shared.isUserLoggedIn() {
            // User is logged in, proceed with purchase
            purchaseCoins(package: package)
        } else {
            // User not logged in, show login options
            showLoginRequiredForPurchase(package: package)
        }
    }

    @objc private func claimDailyBonusTapped() {
        // Prevent multiple taps during loading
        guard !isLoading else { return }

        // Show loading state
        showDailyBonusLoading(true)

        // Simulate a brief delay for better UX (daily bonus is instant but feels too fast)
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            let result = CoinTransactionManager.shared.claimDailyBonus(for: AppViewModel.shared.currentUser.id)

            // Hide loading state
            self.showDailyBonusLoading(false)

            switch result {
            case .success(let transaction):
                let alert = UIAlertController(
                    title: "Daily Bonus Claimed! 🎉",
                    message: "You received \(transaction.amount) coins!",
                    preferredStyle: .alert
                )
                alert.addAction(UIAlertAction(title: "Awesome!", style: .default))
                self.present(alert, animated: true)

            case .failure(let error):
                let alert = UIAlertController(
                    title: "Bonus Not Available",
                    message: error.localizedDescription,
                    preferredStyle: .alert
                )
                alert.addAction(UIAlertAction(title: "OK", style: .default))
                self.present(alert, animated: true)
            }
        }
    }

    private func showDailyBonusLoading(_ show: Bool) {
        dailyBonusButton.isEnabled = !show

        if show {
            // Store original title
            let originalTitle = dailyBonusButton.configuration?.title

            // Update button to show loading state
            var config = dailyBonusButton.configuration
            config?.title = "🔄 Claiming..."
            config?.showsActivityIndicator = true
            dailyBonusButton.configuration = config

            // Store original title for restoration
            dailyBonusButton.accessibilityValue = originalTitle
        } else {
            // Restore original title
            var config = dailyBonusButton.configuration
            config?.title = dailyBonusButton.accessibilityValue
            config?.showsActivityIndicator = false
            dailyBonusButton.configuration = config

            // Clear stored title
            dailyBonusButton.accessibilityValue = nil
        }
    }

    private func showLoginRequiredForPurchase(package: CoinPurchasePackage) {
        let alert = UIAlertController(
            title: "Login Required",
            message: "Please log in to purchase coins and unlock premium features.",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        alert.addAction(UIAlertAction(title: "Guest Login", style: .default) { _ in
            AppViewModel.shared.login()

            // After login, update UI and proceed with purchase
            DispatchQueue.main.async {
                self.refreshUIAfterLogin()
                self.purchaseCoins(package: package)
            }
        })

        alert.addAction(UIAlertAction(title: "Apple Login", style: .default) { _ in
            self.presentAppleLoginForPurchase(package: package)
        })

        present(alert, animated: true)
    }

    private func presentAppleLoginForPurchase(package: CoinPurchasePackage) {
        // Store the package for after login
        self.pendingPurchasePackage = package

        let loginVC = LoginViewController()
        let navController = UINavigationController(rootViewController: loginVC)
        navController.modalPresentationStyle = .fullScreen
        present(navController, animated: true)
    }

    private func refreshUIAfterLogin() {
        // Update the UI to show logged in state
        setupHeaderBasedOnLoginStatus()
        setupConstraints()
        updateCoinsDisplay()

        // Reload the view to show header
        view.setNeedsLayout()
        view.layoutIfNeeded()
    }

    @objc private func showTransactionHistory() {
        let historyVC = CoinTransactionHistoryViewController()
        navigationController?.pushViewController(historyVC, animated: true)
    }

    private func purchaseCoins(package: CoinPurchasePackage) {
        // User is already logged in at this point (checked in purchaseButtonTapped)
        let authManager = UserAuthenticationManager.shared

        guard authManager.getCurrentUser() != nil else {
            showLoginAlert()
            return
        }

        #if DEBUG
        if isTestMode {
            showTestPurchaseOptions(for: package)
            return
        }
        #endif

        showLoading(true)

        iapManager.purchaseCoinPackage(package) { [weak self] success, error in
            DispatchQueue.main.async {
                self?.showLoading(false)

                if success {
                    self?.showPurchaseSuccessAlert(for: package)
                } else {
                    let errorMessage = error?.localizedDescription ?? "Purchase failed. Please try again."
                    self?.showAlert(title: "Purchase Failed", message: errorMessage)
                }
            }
        }
    }

    private func showPurchaseOptionsAlert(for package: CoinPurchasePackage) {
        let alert = UIAlertController(
            title: "Login Required",
            message: "To purchase coins, please choose an option:",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        alert.addAction(UIAlertAction(title: "Continue as Guest", style: .default) { _ in
            // Login as guest and then proceed with purchase
            AppViewModel.shared.login()
            self.purchaseCoins(package: package)
        })

        alert.addAction(UIAlertAction(title: "Sign in with Apple", style: .default) { _ in
            self.dismiss(animated: true) {
                // Navigate to login page for Apple sign-in
                self.navigateToLogin()
            }
        })

        present(alert, animated: true)
    }

    private func showLoginAlert() {
        let alert = UIAlertController(
            title: "Login Required",
            message: "Please log in to purchase coins",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Login", style: .default) { _ in
            self.dismiss(animated: true) {
                self.navigateToLogin()
            }
        })

        present(alert, animated: true)
    }

    private func navigateToLogin() {
        // Navigate to login page
        if let windowScene = UIApplication.shared.connectedScenes.first as? UIWindowScene,
           let window = windowScene.windows.first {
            let loginVC = LoginViewController()
            window.rootViewController = loginVC
        }
    }

    private func showPurchaseSuccessAlert(for package: CoinPurchasePackage) {
        let alert = UIAlertController(
            title: "🎉 Purchase Successful!",
            message: "You received \(package.totalCoins) coins and \(package.aiAnalysisCount) AI analyses! Ready to unlock amazing AI features ✨",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "Awesome!", style: .default))
        present(alert, animated: true)
    }

    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    private func showLoading(_ show: Bool) {
        isLoading = show

        if show {
            showLoadingOverlay()
        } else {
            hideLoadingOverlay()
        }

        // Disable user interaction during loading
        view.isUserInteractionEnabled = !show

        // Also disable all purchase buttons specifically
        updatePurchaseButtonsState(enabled: !show)
    }

    private func showLoadingOverlay() {
        // Remove existing overlay if any
        hideLoadingOverlay()

        // Create overlay view
        let overlay = UIView()
        overlay.backgroundColor = UIColor.black.withAlphaComponent(0.5)
        overlay.translatesAutoresizingMaskIntoConstraints = false

        // Create loading indicator
        let indicator = UIActivityIndicatorView(style: .large)
        indicator.color = .white
        indicator.translatesAutoresizingMaskIntoConstraints = false
        indicator.startAnimating()

        // Create loading label
        let label = UILabel()
        label.text = "Processing..."
        label.textColor = .white
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textAlignment = .center
        label.numberOfLines = 0 // 允许多行显示
        label.translatesAutoresizingMaskIntoConstraints = false

        // Create container for indicator and label
        let container = UIView()
        container.backgroundColor = UIColor.black.withAlphaComponent(0.8)
        container.layer.cornerRadius = 16
        container.translatesAutoresizingMaskIntoConstraints = false

        // Add views to hierarchy
        overlay.addSubview(container)
        container.addSubview(indicator)
        container.addSubview(label)
        view.addSubview(overlay)

        // Setup constraints
        NSLayoutConstraint.activate([
            // Overlay fills entire view
            overlay.topAnchor.constraint(equalTo: view.topAnchor),
            overlay.leadingAnchor.constraint(equalTo: view.leadingAnchor),
            overlay.trailingAnchor.constraint(equalTo: view.trailingAnchor),
            overlay.bottomAnchor.constraint(equalTo: view.bottomAnchor),

            // Container centered in overlay
            container.centerXAnchor.constraint(equalTo: overlay.centerXAnchor),
            container.centerYAnchor.constraint(equalTo: overlay.centerYAnchor),
            container.widthAnchor.constraint(greaterThanOrEqualToConstant: 200), // 最小宽度200
            container.widthAnchor.constraint(lessThanOrEqualToConstant: 280), // 最大宽度280
            container.heightAnchor.constraint(equalToConstant: 120),

            // Indicator at top of container
            indicator.centerXAnchor.constraint(equalTo: container.centerXAnchor),
            indicator.topAnchor.constraint(equalTo: container.topAnchor, constant: 20),

            // Label below indicator
            label.centerXAnchor.constraint(equalTo: container.centerXAnchor),
            label.topAnchor.constraint(equalTo: indicator.bottomAnchor, constant: 16),
            label.leadingAnchor.constraint(equalTo: container.leadingAnchor, constant: 16),
            label.trailingAnchor.constraint(equalTo: container.trailingAnchor, constant: -16)
        ])

        // Store references
        loadingOverlay = overlay
        loadingIndicator = indicator
        loadingLabel = label

        // Animate in
        overlay.alpha = 0
        UIView.animate(withDuration: 0.3) {
            overlay.alpha = 1
        }
    }

    private func hideLoadingOverlay() {
        guard let overlay = loadingOverlay else { return }

        UIView.animate(withDuration: 0.3, animations: {
            overlay.alpha = 0
        }) { _ in
            overlay.removeFromSuperview()
            self.loadingOverlay = nil
            self.loadingIndicator = nil
            self.loadingLabel = nil
        }
    }

    private func updatePurchaseButtonsState(enabled: Bool) {
        // Find all purchase buttons in the packages stack view and update their state
        for case let cardView in packagesStackView.arrangedSubviews {
            if let stackView = cardView.subviews.first?.subviews.first as? UIStackView {
                // Find the purchase button (should be the last arranged subview)
                if let purchaseButton = stackView.arrangedSubviews.last as? UIButton {
                    purchaseButton.isEnabled = enabled
                    purchaseButton.alpha = enabled ? 1.0 : 0.6

                    // Update button appearance based on loading state
                    if !enabled && isLoading {
                        var config = purchaseButton.configuration
                        config?.title = "Processing..."
                        config?.showsActivityIndicator = true
                        purchaseButton.configuration = config
                    } else if enabled {
                        // Restore original title when enabled
                        var config = purchaseButton.configuration
                        config?.title = "Purchase Now"
                        config?.showsActivityIndicator = false
                        purchaseButton.configuration = config
                    }
                }
            }
        }

        // Also update daily bonus button if visible (but don't change its loading state here)
        if !isLoading { // Only update daily bonus button if we're not in purchase loading state
            dailyBonusButton.isEnabled = enabled
            dailyBonusButton.alpha = enabled ? 1.0 : 0.6
        }
    }

    #if DEBUG
    private func showTestPurchaseOptions(for package: CoinPurchasePackage) {
        let alert = UIAlertController(
            title: "Test Mode",
            message: "Simulate purchase for \(package.title)?",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Simulate Success", style: .default) { _ in
            // Simulate purchase through transaction manager
            let result = CoinTransactionManager.shared.processCoinPurchase(package: package, for: AppViewModel.shared.currentUser.id)

            switch result {
            case .success:
                self.showPurchaseSuccessAlert(for: package)
            case .failure(let error):
                self.showAlert(title: "Test Failed", message: error.localizedDescription)
            }
        })

        present(alert, animated: true)
    }

    @objc private func showDebugMenu() {
        let alert = UIAlertController(
            title: "Debug Menu",
            message: "IAP Testing Options",
            preferredStyle: .actionSheet
        )

        alert.addAction(UIAlertAction(title: "Toggle Test Mode", style: .default) { _ in
            self.isTestMode.toggle()
            self.iapManager.isTestMode = self.isTestMode
            let status = self.isTestMode ? "ON" : "OFF"
            self.showAlert(title: "Test Mode", message: "Test mode is now \(status)")
        })

        alert.addAction(UIAlertAction(title: "Request Products", style: .default) { _ in
            self.iapManager.requestProducts()
            self.showAlert(title: "Debug", message: "Requesting products from App Store...")
        })

        alert.addAction(UIAlertAction(title: "Show Product Info", style: .default) { _ in
            self.showProductDebugInfo()
        })

        alert.addAction(UIAlertAction(title: "Clear Coin Balance", style: .destructive) { _ in
            self.clearCoinBalanceForTesting()
        })

        alert.addAction(UIAlertAction(title: "Add Test Coins", style: .default) { _ in
            self.addTestCoins()
        })

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        // For iPad
        if let popover = alert.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItems?.last
        }

        present(alert, animated: true)
    }

    private func showProductDebugInfo() {
        iapManager.getProducts { products in
            DispatchQueue.main.async {
                var message = "Available Products:\n\n"

                if products.isEmpty {
                    message += "No products loaded from App Store"
                } else {
                    for product in products {
                        message += "• \(product.localizedTitle)\n"
                        message += "  ID: \(product.productIdentifier)\n"
                        message += "  Price: \(self.iapManager.formatPrice(for: product))\n\n"
                    }
                }

                let alert = UIAlertController(
                    title: "Product Debug Info",
                    message: message,
                    preferredStyle: .alert
                )
                alert.addAction(UIAlertAction(title: "OK", style: .default))
                self.present(alert, animated: true)
            }
        }
    }

    private func clearCoinBalanceForTesting() {
        guard let currentUser = UserAuthenticationManager.shared.getCurrentUser() else { return }

        var balance = CoinTransactionManager.shared.getCurrentBalance(for: currentUser.id)
        balance.totalCoins = 0
        balance.freeProcessingCount = 0

        // Save the cleared balance
        let data = try? JSONEncoder().encode(balance)
        UserDefaults.standard.set(data, forKey: "CoinBalance_\(currentUser.id)")

        // Update AppViewModel
        AppViewModel.shared.currentUser.coins = 0
        AppViewModel.shared.currentUser.freeProcessingCount = 0

        // Post notification
        NotificationCenter.default.post(name: .coinBalanceDidUpdate, object: nil)

        showAlert(title: "Debug", message: "Coin balance cleared for testing")
    }

    private func addTestCoins() {
        guard let currentUser = UserAuthenticationManager.shared.getCurrentUser() else { return }

        let result = CoinTransactionManager.shared.processIAPPurchase(
            coinAmount: 1000,
            productId: "debug_test_coins",
            for: currentUser.id
        )

        switch result {
        case .success:
            showAlert(title: "Debug", message: "Added 1000 test coins")
        case .failure(let error):
            showAlert(title: "Debug Error", message: error.localizedDescription)
        }
    }
    #endif

    private func loadProductPrices() {
        iapManager.getProducts { [weak self] products in
            DispatchQueue.main.async {
                self?.updatePackageCardsWithPrices(products)
            }
        }
    }

    private func updatePackageCardsWithPrices(_ products: [SKProduct]) {
        for (index, package) in purchasePackages.enumerated() {
            guard let cardView = packagesStackView.arrangedSubviews[safe: index] as? UIView else { continue }

            // Find the price label in the card view
            if let priceLabel = findPriceLabel(in: cardView) {
                if let product = products.first(where: { $0.productIdentifier == package.productId }) {
                    priceLabel.text = iapManager.formatPrice(for: product)
                } else {
                    priceLabel.text = package.price // Fallback to static price
                }
            }
        }
    }

    private func findPriceLabel(in view: UIView) -> UILabel? {
        for subview in view.subviews {
            if let label = subview as? UILabel, label.text?.contains("$") == true {
                return label
            } else if let foundLabel = findPriceLabel(in: subview) {
                return foundLabel
            }
        }
        return nil
    }

    private func animatePurchaseButton(for package: CoinPurchasePackage) {
        guard let index = purchasePackages.firstIndex(where: { $0.id == package.id }),
              let cardView = packagesStackView.arrangedSubviews[safe: index] else { return }

        UIView.animate(withDuration: 0.1, animations: {
            cardView.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                cardView.transform = .identity
            }
        }
    }
}

extension Array {
    subscript(safe index: Index) -> Element? {
        return indices.contains(index) ? self[index] : nil
    }
}