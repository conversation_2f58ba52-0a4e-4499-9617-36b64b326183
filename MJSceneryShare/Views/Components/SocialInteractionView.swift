import UIKit
import SnapKit

protocol SocialInteractionViewDelegate: AnyObject {
    func socialInteractionView(_ view: SocialInteractionView, didTapLike share: Share)
    func socialInteractionView(_ view: SocialInteractionView, didTapComment share: Share)
    func socialInteractionView(_ view: SocialInteractionView, didTapShare share: Share)
    func socialInteractionView(_ view: SocialInteractionView, didTapFavorite share: Share)
}

class SocialInteractionView: UIView {
    
    weak var delegate: SocialInteractionViewDelegate?
    private var currentShare: Share?
    
    private lazy var stackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 8
        return stackView
    }()
    
    private lazy var likeButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "heart"), for: .normal)
        button.setTitleColor(.secondaryLabel, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.addTarget(self, action: #selector(likeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var commentButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "message"), for: .normal)
        button.setTitleColor(.secondaryLabel, for: .normal)
        button.tintColor = .secondaryLabel
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.addTarget(self, action: #selector(commentButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var shareButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "square.and.arrow.up"), for: .normal)
        button.setTitleColor(.secondaryLabel, for: .normal)
        button.tintColor = .secondaryLabel
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.addTarget(self, action: #selector(shareButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var favoriteButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "star"), for: .normal)
        button.setTitleColor(.secondaryLabel, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.addTarget(self, action: #selector(favoriteButtonTapped), for: .touchUpInside)
        return button
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        addSubview(stackView)
        
        stackView.addArrangedSubview(likeButton)
        stackView.addArrangedSubview(commentButton)
        stackView.addArrangedSubview(favoriteButton)
        stackView.addArrangedSubview(shareButton)
    }
    
    private func setupConstraints() {
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(44)
        }
    }
    
    func configure(with share: Share) {
        currentShare = share
        
        // Update like button
        likeButton.setTitle(" \(share.likes)", for: .normal)
        likeButton.setImage(UIImage(systemName: share.isLiked ? "heart.fill" : "heart"), for: .normal)
        likeButton.tintColor = share.isLiked ? .systemPink : .secondaryLabel
        
        // Update comment button
        commentButton.setTitle(" \(share.comments)", for: .normal)
        
        // Update favorite button
        favoriteButton.setTitle(" \(share.favorites)", for: .normal)
        favoriteButton.setImage(UIImage(systemName: share.isFavorited ? "star.fill" : "star"), for: .normal)
        favoriteButton.tintColor = share.isFavorited ? .systemYellow : .secondaryLabel
    }
    
    @objc private func likeButtonTapped() {
        guard let share = currentShare else { return }
        
        // Add beautiful animation for like
        animateLike()
        delegate?.socialInteractionView(self, didTapLike: share)
    }
    
    @objc private func commentButtonTapped() {
        guard let share = currentShare else { return }
        delegate?.socialInteractionView(self, didTapComment: share)
    }
    
    @objc private func shareButtonTapped() {
        guard let share = currentShare else { return }
        delegate?.socialInteractionView(self, didTapShare: share)
    }
    
    @objc private func favoriteButtonTapped() {
        guard let share = currentShare else { return }
        
        // Add beautiful animation for favorite
        animateFavorite()
        delegate?.socialInteractionView(self, didTapFavorite: share)
    }
    
    private func animateLike() {
        // Create floating hearts animation
        let heartEmoji = UILabel()
        heartEmoji.text = "💕"
        heartEmoji.font = .systemFont(ofSize: 20)
        heartEmoji.alpha = 0
        
        addSubview(heartEmoji)
        heartEmoji.snp.makeConstraints { make in
            make.center.equalTo(likeButton)
        }
        
        UIView.animate(withDuration: 0.6, delay: 0, options: [.curveEaseOut], animations: {
            heartEmoji.alpha = 1
            heartEmoji.transform = CGAffineTransform(translationX: 0, y: -30).scaledBy(x: 1.5, y: 1.5)
        }) { _ in
            UIView.animate(withDuration: 0.3, animations: {
                heartEmoji.alpha = 0
            }) { _ in
                heartEmoji.removeFromSuperview()
            }
        }
        
        // Button bounce animation
        UIView.animate(withDuration: 0.1, animations: {
            self.likeButton.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.likeButton.transform = .identity
            }
        }
    }
    
    private func animateFavorite() {
        // Create floating stars animation
        let starEmoji = UILabel()
        starEmoji.text = "✨"
        starEmoji.font = .systemFont(ofSize: 18)
        starEmoji.alpha = 0
        
        addSubview(starEmoji)
        starEmoji.snp.makeConstraints { make in
            make.center.equalTo(favoriteButton)
        }
        
        UIView.animate(withDuration: 0.6, delay: 0, options: [.curveEaseOut], animations: {
            starEmoji.alpha = 1
            starEmoji.transform = CGAffineTransform(translationX: 0, y: -25).scaledBy(x: 1.3, y: 1.3)
        }) { _ in
            UIView.animate(withDuration: 0.3, animations: {
                starEmoji.alpha = 0
            }) { _ in
                starEmoji.removeFromSuperview()
            }
        }
        
        // Button bounce animation
        UIView.animate(withDuration: 0.1, animations: {
            self.favoriteButton.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.favoriteButton.transform = .identity
            }
        }
    }
}
