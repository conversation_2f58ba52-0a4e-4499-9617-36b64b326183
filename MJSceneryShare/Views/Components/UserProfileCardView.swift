import UIKit
import SnapKit

class UserProfileCardView: UIView {
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.systemPink.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 4)
        view.layer.shadowRadius = 8
        return view
    }()
    
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 35 // Half of 70px width/height for perfect circle
        imageView.backgroundColor = .systemGray5
        imageView.tintColor = .systemPink
        // Add elegant border
        imageView.layer.borderWidth = 3
        imageView.layer.borderColor = UIColor.systemPink.withAlphaComponent(0.3).cgColor
        return imageView
    }()
    
    private lazy var nicknameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 18, weight: .semibold)
        label.textColor = .label
        return label
    }()
    
    private lazy var regionLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .systemPink
        return label
    }()
    
    private lazy var timeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12)
        label.textColor = .tertiaryLabel
        return label
    }()
    
    private lazy var followButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Follow", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemPink
        button.layer.cornerRadius = 12
        button.titleLabel?.font = .systemFont(ofSize: 12, weight: .semibold)
        button.contentEdgeInsets = UIEdgeInsets(top: 6, left: 12, bottom: 6, right: 12)
        return button
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        addSubview(containerView)
        
        containerView.addSubview(avatarImageView)
        containerView.addSubview(nicknameLabel)
        containerView.addSubview(regionLabel)
        containerView.addSubview(timeLabel)
        containerView.addSubview(followButton)
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(8)
            make.height.greaterThanOrEqualTo(90) // Ensure minimum height for better spacing
        }
        
        avatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(20) // Increased from 16 to 20
            make.centerY.equalToSuperview()
            make.width.height.equalTo(70) // Increased from 60 to 70
        }
        
        nicknameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(16) // Increased from 12 to 16
            make.top.equalTo(avatarImageView.snp.top).offset(8) // Increased from 4 to 8
            make.right.lessThanOrEqualTo(followButton.snp.left).offset(-8)
        }

        regionLabel.snp.makeConstraints { make in
            make.left.equalTo(nicknameLabel)
            make.top.equalTo(nicknameLabel.snp.bottom).offset(4) // Increased from 2 to 4
            make.right.lessThanOrEqualTo(followButton.snp.left).offset(-8)
        }

        timeLabel.snp.makeConstraints { make in
            make.left.equalTo(nicknameLabel)
            make.top.equalTo(regionLabel.snp.bottom).offset(4) // Increased from 2 to 4
            make.right.lessThanOrEqualTo(followButton.snp.left).offset(-8)
        }
        
        followButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-20) // Increased from -16 to -20
            make.centerY.equalToSuperview()
            make.height.equalTo(32) // Increased from 28 to 32 for better touch target
        }
    }
    
    func configure(with author: Share.Author, publishTime: Date) {
        // Set avatar using AvatarManager
        avatarImageView.image = AvatarManager.shared.getAvatarImageWithFallback(named: author.avatar)

        // Ensure corner radius is applied after image is set
        avatarImageView.layer.cornerRadius = 35
        
        nicknameLabel.text = author.nickname
        regionLabel.text = "📍 \(author.region)"
        timeLabel.text = formatDate(publishTime)
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "en_US")
        return formatter.localizedString(for: date, relativeTo: Date())
    }
}

// MARK: - Enhanced User Stats View
class UserStatsView: UIView {
    
    private lazy var stackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 16
        return stackView
    }()
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        addSubview(stackView)
        
        let postsView = createStatView(title: "Posts", value: "24", icon: "photo.on.rectangle")
        let followersView = createStatView(title: "Followers", value: "1.2K", icon: "heart")
        let followingView = createStatView(title: "Following", value: "456", icon: "person.2")
        
        stackView.addArrangedSubview(postsView)
        stackView.addArrangedSubview(followersView)
        stackView.addArrangedSubview(followingView)
    }
    
    private func setupConstraints() {
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    private func createStatView(title: String, value: String, icon: String) -> UIView {
        let containerView = UIView()
        
        let iconImageView = UIImageView()
        iconImageView.image = UIImage(systemName: icon)
        iconImageView.tintColor = .systemPink
        iconImageView.contentMode = .scaleAspectFit
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = .systemFont(ofSize: 18, weight: .bold)
        valueLabel.textColor = .label
        valueLabel.textAlignment = .center
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 12, weight: .medium)
        titleLabel.textColor = .secondaryLabel
        titleLabel.textAlignment = .center
        
        containerView.addSubview(iconImageView)
        containerView.addSubview(valueLabel)
        containerView.addSubview(titleLabel)
        
        iconImageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
            make.width.height.equalTo(20)
        }
        
        valueLabel.snp.makeConstraints { make in
            make.top.equalTo(iconImageView.snp.bottom).offset(4)
            make.centerX.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(valueLabel.snp.bottom).offset(2)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview()
        }
        
        return containerView
    }
}
