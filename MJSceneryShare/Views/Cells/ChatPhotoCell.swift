import UIKit
import SnapKit

class ChatPhotoCell: UITableViewCell {
    
    static let identifier = "ChatPhotoCell"
    
    weak var delegate: ChatPhotoCellDelegate?
    private var currentMessage: ChatMessage?
    
    // MARK: - UI Components
    private lazy var messageBubbleView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 18
        view.clipsToBounds = true
        return view
    }()
    
    private lazy var photoImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 12
        imageView.backgroundColor = .systemGray6
        return imageView
    }()
    
    private lazy var captionLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.numberOfLines = 0
        label.isHidden = true
        return label
    }()
    
    private lazy var timeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private lazy var statusImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = .systemPink
        return imageView
    }()
    
    private lazy var loadingIndicator: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .medium)
        indicator.color = .white
        indicator.hidesWhenStopped = true
        return indicator
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
        setupGestures()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(messageBubbleView)
        messageBubbleView.addSubview(photoImageView)
        messageBubbleView.addSubview(captionLabel)
        messageBubbleView.addSubview(loadingIndicator)
        contentView.addSubview(timeLabel)
        contentView.addSubview(statusImageView)
    }
    
    private func setupConstraints() {
        messageBubbleView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(4)
            make.bottom.equalTo(timeLabel.snp.top).offset(-4)
            make.width.equalTo(200)
            make.height.equalTo(200)
        }
        
        photoImageView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview().inset(4)
            make.height.equalTo(150)
        }
        
        captionLabel.snp.makeConstraints { make in
            make.top.equalTo(photoImageView.snp.bottom).offset(8)
            make.left.right.bottom.equalToSuperview().inset(12)
        }
        
        loadingIndicator.snp.makeConstraints { make in
            make.center.equalTo(photoImageView)
        }
        
        timeLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-8)
        }
        
        statusImageView.snp.makeConstraints { make in
            make.centerY.equalTo(timeLabel)
            make.width.height.equalTo(12)
        }
    }
    
    private func setupGestures() {
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(photoTapped))
        photoImageView.addGestureRecognizer(tapGesture)
        photoImageView.isUserInteractionEnabled = true
    }
    
    // MARK: - Configuration
    func configure(with message: ChatMessage) {
        currentMessage = message
        
        timeLabel.text = message.formattedTime
        
        // Configure layout based on sender
        configureLayout(for: message)
        
        // Load photo
        loadPhoto(from: message.content)
        
        // Configure status
        configureStatus(for: message)
        
        // Handle caption if present (for future use)
        captionLabel.isHidden = true
    }
    
    private func configureLayout(for message: ChatMessage) {
        if message.isSentByCurrentUser {
            // Sent message (right side)
            messageBubbleView.backgroundColor = .systemPink.withAlphaComponent(0.1)
            captionLabel.textColor = .label
            
            messageBubbleView.snp.remakeConstraints { make in
                make.top.equalToSuperview().offset(4)
                make.bottom.equalTo(timeLabel.snp.top).offset(-4)
                make.width.equalTo(200)
                make.height.equalTo(200)
                make.trailing.equalToSuperview().offset(-16)
            }
            
            timeLabel.snp.remakeConstraints { make in
                make.trailing.equalTo(messageBubbleView)
                make.bottom.equalToSuperview().offset(-8)
            }
            
            statusImageView.snp.remakeConstraints { make in
                make.trailing.equalTo(timeLabel.snp.leading).offset(-4)
                make.centerY.equalTo(timeLabel)
                make.width.height.equalTo(12)
            }
            
        } else {
            // Received message (left side)
            messageBubbleView.backgroundColor = .systemBackground
            captionLabel.textColor = .label
            
            messageBubbleView.snp.remakeConstraints { make in
                make.top.equalToSuperview().offset(4)
                make.bottom.equalTo(timeLabel.snp.top).offset(-4)
                make.width.equalTo(200)
                make.height.equalTo(200)
                make.leading.equalToSuperview().offset(16)
            }
            
            timeLabel.snp.remakeConstraints { make in
                make.leading.equalTo(messageBubbleView)
                make.bottom.equalToSuperview().offset(-8)
            }
            
            statusImageView.snp.remakeConstraints { make in
                make.leading.equalTo(timeLabel.snp.trailing).offset(4)
                make.centerY.equalTo(timeLabel)
                make.width.height.equalTo(12)
            }
        }
        
        // Add shadow
        messageBubbleView.layer.shadowColor = UIColor.black.cgColor
        messageBubbleView.layer.shadowOpacity = 0.1
        messageBubbleView.layer.shadowOffset = CGSize(width: 0, height: 2)
        messageBubbleView.layer.shadowRadius = 4
    }
    
    private func loadPhoto(from urlString: String) {
        loadingIndicator.startAnimating()
        
        // Simple image loading (in a real app, use SDWebImage or similar)
        if let url = URL(string: urlString) {
            DispatchQueue.global().async {
                if let data = try? Data(contentsOf: url),
                   let image = UIImage(data: data) {
                    DispatchQueue.main.async {
                        self.photoImageView.image = image
                        self.loadingIndicator.stopAnimating()
                        self.addPhotoLoadAnimation()
                    }
                }
            }
        } else {
            loadingIndicator.stopAnimating()
        }
    }
    
    private func addPhotoLoadAnimation() {
        photoImageView.alpha = 0
        photoImageView.transform = CGAffineTransform(scaleX: 0.8, y: 0.8)
        
        UIView.animate(withDuration: 0.3, delay: 0, options: [.curveEaseOut], animations: {
            self.photoImageView.alpha = 1
            self.photoImageView.transform = .identity
        })
    }
    
    private func configureStatus(for message: ChatMessage) {
        if message.isSentByCurrentUser {
            if message.isRead {
                statusImageView.image = UIImage(systemName: "checkmark.circle.fill")
                statusImageView.tintColor = .systemPink
            } else if message.isDelivered {
                statusImageView.image = UIImage(systemName: "checkmark.circle")
                statusImageView.tintColor = .systemGray
            } else {
                statusImageView.image = UIImage(systemName: "clock")
                statusImageView.tintColor = .systemGray
            }
            statusImageView.isHidden = false
        } else {
            statusImageView.isHidden = true
        }
    }
    
    // MARK: - Actions
    @objc private func photoTapped() {
        guard let message = currentMessage else { return }
        
        // Add tap animation
        UIView.animate(withDuration: 0.1, animations: {
            self.photoImageView.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.photoImageView.transform = .identity
            }
        }
        
        delegate?.chatPhotoCell(self, didTapPhoto: message)
    }
    
    // MARK: - Reuse
    override func prepareForReuse() {
        super.prepareForReuse()
        currentMessage = nil
        photoImageView.image = nil
        photoImageView.alpha = 1
        photoImageView.transform = .identity
        captionLabel.text = nil
        captionLabel.isHidden = true
        timeLabel.text = nil
        statusImageView.image = nil
        statusImageView.isHidden = true
        loadingIndicator.stopAnimating()
    }
}

// MARK: - Typing Indicator Cell
class TypingIndicatorCell: UITableViewCell {
    
    static let identifier = "TypingIndicatorCell"
    
    // MARK: - UI Components
    private lazy var messageBubbleView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 18
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 1)
        view.layer.shadowRadius = 2
        return view
    }()
    
    private lazy var typingDotsView: TypingDotsView = {
        let view = TypingDotsView()
        return view
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(messageBubbleView)
        messageBubbleView.addSubview(typingDotsView)
    }
    
    private func setupConstraints() {
        messageBubbleView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(4)
            make.bottom.equalToSuperview().offset(-4)
            make.leading.equalToSuperview().offset(16)
            make.width.equalTo(60)
            make.height.equalTo(40)
        }
        
        typingDotsView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.equalTo(40)
            make.height.equalTo(20)
        }
    }
    
    // MARK: - Configuration
    func configure(with friend: Friend) {
        typingDotsView.startAnimating()
    }
    
    // MARK: - Reuse
    override func prepareForReuse() {
        super.prepareForReuse()
        typingDotsView.stopAnimating()
    }
}

// MARK: - Typing Dots Animation View
class TypingDotsView: UIView {
    
    private var dots: [UIView] = []
    private var animationTimer: Timer?
    
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupDots()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupDots() {
        for i in 0..<3 {
            let dot = UIView()
            dot.backgroundColor = .systemGray
            dot.layer.cornerRadius = 3
            addSubview(dot)
            dots.append(dot)
            
            dot.snp.makeConstraints { make in
                make.width.height.equalTo(6)
                make.centerY.equalToSuperview()
                make.left.equalToSuperview().offset(i * 12)
            }
        }
    }
    
    func startAnimating() {
        animationTimer = Timer.scheduledTimer(withTimeInterval: 0.6, repeats: true) { _ in
            self.animateDots()
        }
    }
    
    func stopAnimating() {
        animationTimer?.invalidate()
        animationTimer = nil
        
        dots.forEach { dot in
            dot.alpha = 0.3
            dot.transform = .identity
        }
    }
    
    private func animateDots() {
        for (index, dot) in dots.enumerated() {
            UIView.animate(withDuration: 0.3, delay: Double(index) * 0.1, options: [.curveEaseInOut], animations: {
                dot.alpha = 1.0
                dot.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
            }) { _ in
                UIView.animate(withDuration: 0.3) {
                    dot.alpha = 0.3
                    dot.transform = .identity
                }
            }
        }
    }
    
    deinit {
        stopAnimating()
    }
}
