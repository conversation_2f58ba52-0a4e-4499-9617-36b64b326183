import UIKit
import SnapKit

class FriendSelectionCell: UICollectionViewCell {
    
    static let identifier = "FriendSelectionCell"
    
    // MARK: - UI Components
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 12
        view.layer.borderWidth = 2
        view.layer.borderColor = UIColor.systemGray5.cgColor
        return view
    }()
    
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 20 // Half of 40px for perfect circle
        imageView.backgroundColor = .systemGray5
        imageView.tintColor = .systemPink
        return imageView
    }()
    
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 13, weight: .medium)
        label.textColor = .label
        label.textAlignment = .center
        label.numberOfLines = 1
        return label
    }()
    
    private lazy var checkmarkImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "checkmark.circle.fill")
        imageView.tintColor = .systemPink
        imageView.backgroundColor = .systemBackground
        imageView.layer.cornerRadius = 10
        imageView.isHidden = true
        return imageView
    }()
    
    private lazy var statusIndicator: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 4
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.systemBackground.cgColor
        view.isHidden = true
        return view
    }()
    
    // MARK: - Properties
    override var isSelected: Bool {
        didSet {
            updateSelectionState()
        }
    }
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        contentView.addSubview(containerView)
        containerView.addSubview(avatarImageView)
        containerView.addSubview(nameLabel)
        containerView.addSubview(checkmarkImageView)
        containerView.addSubview(statusIndicator)
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        avatarImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(40) // Increased from 32 to 40
        }
        
        statusIndicator.snp.makeConstraints { make in
            make.bottom.right.equalTo(avatarImageView)
            make.width.height.equalTo(8)
        }
        
        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView.snp.bottom).offset(4)
            make.left.right.equalToSuperview().inset(4)
            make.bottom.equalToSuperview().offset(-8)
        }
        
        checkmarkImageView.snp.makeConstraints { make in
            make.top.right.equalToSuperview().inset(4)
            make.width.height.equalTo(20)
        }
    }
    
    // MARK: - Configuration
    func configure(with friend: Friend) {
        // Set avatar using AvatarManager
        avatarImageView.image = AvatarManager.shared.getAvatarImageWithFallback(named: friend.avatar)

        // Ensure corner radius is applied after image is set for perfect circle
        avatarImageView.layer.cornerRadius = 20

        // Set name
        nameLabel.text = friend.displayName

        // Configure status indicator
        configureStatusIndicator(for: friend.status)

        // Update selection state
        updateSelectionState()
    }
    
    private func configureStatusIndicator(for status: FriendStatus) {
        switch status {
        case .online:
            statusIndicator.backgroundColor = .systemGreen
            statusIndicator.isHidden = false
        case .recentlyActive:
            statusIndicator.backgroundColor = .systemOrange
            statusIndicator.isHidden = false
        case .doNotDisturb:
            statusIndicator.backgroundColor = .systemPurple
            statusIndicator.isHidden = false
        case .offline:
            statusIndicator.isHidden = true
        }
    }
    
    private func updateSelectionState() {
        UIView.animate(withDuration: 0.2, delay: 0, options: [.curveEaseInOut], animations: {
            if self.isSelected {
                self.containerView.layer.borderColor = UIColor.systemPink.cgColor
                self.containerView.backgroundColor = UIColor.systemPink.withAlphaComponent(0.1)
                self.checkmarkImageView.isHidden = false
                self.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
            } else {
                self.containerView.layer.borderColor = UIColor.systemGray5.cgColor
                self.containerView.backgroundColor = .systemBackground
                self.checkmarkImageView.isHidden = true
                self.transform = .identity
            }
        })
    }
    
    // MARK: - Reuse
    override func prepareForReuse() {
        super.prepareForReuse()
        avatarImageView.image = nil
        nameLabel.text = nil
        checkmarkImageView.isHidden = true
        statusIndicator.isHidden = true
        containerView.layer.borderColor = UIColor.systemGray5.cgColor
        containerView.backgroundColor = .systemBackground
        transform = .identity
    }
}

// MARK: - Photo Preview Cell
class PhotoPreviewCell: UICollectionViewCell {
    
    static let identifier = "PhotoPreviewCell"
    
    // MARK: - UI Components
    private lazy var imageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 8
        imageView.backgroundColor = .systemGray6
        return imageView
    }()
    
    private lazy var selectionOverlay: UIView = {
        let view = UIView()
        view.backgroundColor = UIColor.systemPink.withAlphaComponent(0.3)
        view.layer.cornerRadius = 8
        view.isHidden = true
        return view
    }()
    
    private lazy var checkmarkImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "checkmark.circle.fill")
        imageView.tintColor = .white
        imageView.backgroundColor = .systemPink
        imageView.layer.cornerRadius = 12
        imageView.isHidden = true
        return imageView
    }()
    
    // MARK: - Properties
    override var isSelected: Bool {
        didSet {
            updateSelectionState()
        }
    }
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        contentView.addSubview(imageView)
        contentView.addSubview(selectionOverlay)
        contentView.addSubview(checkmarkImageView)
    }
    
    private func setupConstraints() {
        imageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        selectionOverlay.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        checkmarkImageView.snp.makeConstraints { make in
            make.top.right.equalToSuperview().inset(8)
            make.width.height.equalTo(24)
        }
    }
    
    // MARK: - Configuration
    func configure(with image: UIImage) {
        imageView.image = image
        updateSelectionState()
    }
    
    func configure(with imageURL: String) {
        // In a real app, use SDWebImage or similar for URL loading
        if let url = URL(string: imageURL) {
            DispatchQueue.global().async {
                if let data = try? Data(contentsOf: url),
                   let image = UIImage(data: data) {
                    DispatchQueue.main.async {
                        self.imageView.image = image
                    }
                }
            }
        }
        updateSelectionState()
    }
    
    private func updateSelectionState() {
        UIView.animate(withDuration: 0.2) {
            self.selectionOverlay.isHidden = !self.isSelected
            self.checkmarkImageView.isHidden = !self.isSelected
            
            if self.isSelected {
                self.transform = CGAffineTransform(scaleX: 0.9, y: 0.9)
            } else {
                self.transform = .identity
            }
        }
    }
    
    // MARK: - Reuse
    override func prepareForReuse() {
        super.prepareForReuse()
        imageView.image = nil
        selectionOverlay.isHidden = true
        checkmarkImageView.isHidden = true
        transform = .identity
    }
}
