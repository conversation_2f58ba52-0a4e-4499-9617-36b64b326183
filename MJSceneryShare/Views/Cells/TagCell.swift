//
//  TagCell.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-04.
//

import UIKit
import SnapKit

class TagCell: UICollectionViewCell {
    
    // MARK: - UI Components
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemGray6
        view.layer.cornerRadius = 12
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.clear.cgColor
        return view
    }()
    
    private lazy var tagLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .medium)
        label.textColor = .label
        label.textAlignment = .center
        return label
    }()
    
    // MARK: - Properties
    private var isTagSelected: Bool = false {
        didSet {
            updateAppearance()
        }
    }
    
    // MARK: - Initialization
    override init(frame: CGRect) {
        super.init(frame: frame)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupUI() {
        contentView.addSubview(containerView)
        containerView.addSubview(tagLabel)
        
        setupConstraints()
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.height.equalTo(24)
        }
        
        tagLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(8)
        }
    }
    
    // MARK: - Configuration
    func configure(with tag: ImageTag, isSelected: Bool = false) {
        tagLabel.text = tag.name
        self.isTagSelected = isSelected
        updateAppearance()
    }
    
    func configure(with tagName: String, isSelected: Bool = false) {
        tagLabel.text = tagName
        self.isTagSelected = isSelected
        updateAppearance()
    }
    
    // MARK: - Appearance
    private func updateAppearance() {
        if isTagSelected {
            containerView.backgroundColor = .systemPink
            containerView.layer.borderColor = UIColor.systemPink.cgColor
            tagLabel.textColor = .white
        } else {
            containerView.backgroundColor = .systemGray6
            containerView.layer.borderColor = UIColor.clear.cgColor
            tagLabel.textColor = .label
        }
    }
    
    // MARK: - Selection
    func setSelected(_ selected: Bool) {
        isTagSelected = selected
    }
    
    override func prepareForReuse() {
        super.prepareForReuse()
        tagLabel.text = nil
        isTagSelected = false
        updateAppearance()
    }
}

// MARK: - Size Calculation
extension TagCell {
    static func estimatedSize(for text: String) -> CGSize {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .medium)
        label.text = text
        label.sizeToFit()
        
        return CGSize(width: label.frame.width + 16, height: 24)
    }
}
