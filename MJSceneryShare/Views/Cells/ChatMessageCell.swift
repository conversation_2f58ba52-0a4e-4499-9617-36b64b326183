import UIKit
import SnapKit

class ChatMessageCell: UITableViewCell {
    
    static let identifier = "ChatMessageCell"
    
    weak var delegate: ChatMessageCellDelegate?
    private var currentMessage: ChatMessage?
    
    // MARK: - UI Components
    private lazy var messageBubbleView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 18
        return view
    }()
    
    private lazy var messageLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16)
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var timeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private lazy var statusImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.tintColor = .systemPink
        return imageView
    }()
    
    private lazy var reactionsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 4
        stackView.isHidden = true
        return stackView
    }()
    
    // MARK: - Constraints
    private var bubbleLeadingConstraint: Constraint?
    private var bubbleTrailingConstraint: Constraint?
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
        setupGestures()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(messageBubbleView)
        messageBubbleView.addSubview(messageLabel)
        contentView.addSubview(timeLabel)
        contentView.addSubview(statusImageView)
        contentView.addSubview(reactionsStackView)
    }
    
    private func setupConstraints() {
        messageBubbleView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(4)
            make.bottom.equalTo(timeLabel.snp.top).offset(-4)
            make.width.lessThanOrEqualTo(280)
            bubbleLeadingConstraint = make.leading.greaterThanOrEqualToSuperview().offset(60).constraint
            bubbleTrailingConstraint = make.trailing.lessThanOrEqualToSuperview().offset(-16).constraint
        }
        
        messageLabel.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 12, left:16, bottom: 12, right: 16))
        }
        
        timeLabel.snp.makeConstraints { make in
            make.bottom.equalToSuperview().offset(-8)
        }
        
        statusImageView.snp.makeConstraints { make in
            make.centerY.equalTo(timeLabel)
            make.width.height.equalTo(12)
        }
        
        reactionsStackView.snp.makeConstraints { make in
            make.top.equalTo(messageBubbleView.snp.bottom).offset(4)
            make.leading.equalTo(messageBubbleView)
        }
    }
    
    private func setupGestures() {
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(bubbleTapped))
        messageBubbleView.addGestureRecognizer(tapGesture)
        
        let longPressGesture = UILongPressGestureRecognizer(target: self, action: #selector(bubbleLongPressed))
        messageBubbleView.addGestureRecognizer(longPressGesture)
        
        messageBubbleView.isUserInteractionEnabled = true
    }
    
    // MARK: - Configuration
    func configure(with message: ChatMessage) {
        currentMessage = message
        
        messageLabel.text = message.content
        timeLabel.text = message.formattedTime
        
        configureLayout(for: message)
        configureReactions(for: message)
        configureStatus(for: message)
    }
    
    private func configureLayout(for message: ChatMessage) {
        if message.isSentByCurrentUser {
            // Sent message (right side)
            messageBubbleView.backgroundColor = .systemPink
            messageLabel.textColor = .white
            
            bubbleLeadingConstraint?.update(offset: 60)
            bubbleTrailingConstraint?.update(offset: -16)
            
            messageBubbleView.snp.remakeConstraints { make in
                make.top.equalToSuperview().offset(4)
                make.bottom.equalTo(timeLabel.snp.top).offset(-4)
                make.width.lessThanOrEqualTo(280)
                make.trailing.equalToSuperview().offset(-16)
                make.leading.greaterThanOrEqualToSuperview().offset(60)
            }
            
            timeLabel.snp.remakeConstraints { make in
                make.trailing.equalTo(messageBubbleView)
                make.bottom.equalToSuperview().offset(-8)
            }
            
            statusImageView.snp.remakeConstraints { make in
                make.trailing.equalTo(timeLabel.snp.leading).offset(-4)
                make.centerY.equalTo(timeLabel)
                make.width.height.equalTo(12)
            }
            
        } else {
            // Received message (left side)
            messageBubbleView.backgroundColor = .systemBackground
            messageLabel.textColor = .label
            
            messageBubbleView.snp.remakeConstraints { make in
                make.top.equalToSuperview().offset(4)
                make.bottom.equalTo(timeLabel.snp.top).offset(-4)
                make.width.lessThanOrEqualTo(280)
                make.leading.equalToSuperview().offset(16)
                make.trailing.lessThanOrEqualToSuperview().offset(-60)
            }
            
            timeLabel.snp.remakeConstraints { make in
                make.leading.equalTo(messageBubbleView)
                make.bottom.equalToSuperview().offset(-8)
            }
            
            statusImageView.snp.remakeConstraints { make in
                make.leading.equalTo(timeLabel.snp.trailing).offset(4)
                make.centerY.equalTo(timeLabel)
                make.width.height.equalTo(12)
            }
        }
        
        // Add subtle shadow
        messageBubbleView.layer.shadowColor = UIColor.black.cgColor
        messageBubbleView.layer.shadowOpacity = 0.1
        messageBubbleView.layer.shadowOffset = CGSize(width: 0, height: 1)
        messageBubbleView.layer.shadowRadius = 2
    }
    
    private func configureReactions(for message: ChatMessage) {
        // Clear existing reactions
        reactionsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        
        if !message.reactions.isEmpty {
            reactionsStackView.isHidden = false
            
            // Group reactions by emoji
            let groupedReactions = Dictionary(grouping: message.reactions) { $0.emoji }
            
            for (emoji, reactions) in groupedReactions {
                let reactionView = createReactionView(emoji: emoji, count: reactions.count)
                reactionsStackView.addArrangedSubview(reactionView)
            }
        } else {
            reactionsStackView.isHidden = true
        }
    }
    
    private func createReactionView(emoji: String, count: Int) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .systemGray6
        containerView.layer.cornerRadius = 12
        
        let label = UILabel()
        label.text = "\(emoji) \(count)"
        label.font = .systemFont(ofSize: 12, weight: .medium)
        label.textColor = .label
        
        containerView.addSubview(label)
        label.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 4, left: 8, bottom: 4, right: 8))
        }
        
        return containerView
    }
    
    private func configureStatus(for message: ChatMessage) {
        if message.isSentByCurrentUser {
            if message.isRead {
                statusImageView.image = UIImage(systemName: "checkmark.circle.fill")
                statusImageView.tintColor = .systemPink
            } else if message.isDelivered {
                statusImageView.image = UIImage(systemName: "checkmark.circle")
                statusImageView.tintColor = .systemGray
            } else {
                statusImageView.image = UIImage(systemName: "clock")
                statusImageView.tintColor = .systemGray
            }
            statusImageView.isHidden = false
        } else {
            statusImageView.isHidden = true
        }
    }
    
    // MARK: - Actions
    @objc private func bubbleTapped() {
        guard let message = currentMessage else { return }
        
        // Add tap animation
        UIView.animate(withDuration: 0.1, animations: {
            self.messageBubbleView.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.messageBubbleView.transform = .identity
            }
        }
        
        delegate?.chatMessageCell(self, didTapReaction: message)
    }
    
    @objc private func bubbleLongPressed(_ gesture: UILongPressGestureRecognizer) {
        guard gesture.state == .began, let message = currentMessage else { return }
        
        // Add haptic feedback
        let impactFeedback = UIImpactFeedbackGenerator(style: .medium)
        impactFeedback.impactOccurred()
        
        // Add long press animation
        UIView.animate(withDuration: 0.2, animations: {
            self.messageBubbleView.transform = CGAffineTransform(scaleX: 1.05, y: 1.05)
        }) { _ in
            UIView.animate(withDuration: 0.2) {
                self.messageBubbleView.transform = .identity
            }
        }
        
        delegate?.chatMessageCell(self, didLongPress: message)
    }
    
    // MARK: - Reuse
    override func prepareForReuse() {
        super.prepareForReuse()
        currentMessage = nil
        messageLabel.text = nil
        timeLabel.text = nil
        statusImageView.image = nil
        statusImageView.isHidden = true
        reactionsStackView.isHidden = true
        reactionsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        messageBubbleView.transform = .identity
    }
}
