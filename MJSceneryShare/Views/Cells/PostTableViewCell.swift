//
//  PostTableViewCell.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-04.
//

import UIKit
import SnapKit

class PostTableViewCell: UITableViewCell {
    
    // MARK: - UI Components
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 12
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 4
        return view
    }()
    
    private lazy var postImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 8
        imageView.backgroundColor = .systemGray6
        return imageView
    }()
    
    private lazy var contentStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 4
        stackView.alignment = .leading
        return stackView
    }()
    
    private lazy var captionLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textColor = .label
        label.numberOfLines = 2
        return label
    }()
    
    private lazy var contentTypeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .semibold)
        label.textColor = .systemPink
        return label
    }()
    
    private lazy var timestampLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private lazy var tagsLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12)
        label.textColor = .systemBlue
        label.numberOfLines = 1
        return label
    }()
    
    private lazy var confidenceLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 11, weight: .medium)
        label.textColor = .systemGreen
        return label
    }()
    
    private lazy var chevronImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "chevron.right")
        imageView.tintColor = .systemGray3
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        containerView.addSubview(postImageView)
        containerView.addSubview(contentStackView)
        containerView.addSubview(chevronImageView)
        
        contentStackView.addArrangedSubview(captionLabel)
        contentStackView.addArrangedSubview(contentTypeLabel)
        contentStackView.addArrangedSubview(timestampLabel)
        contentStackView.addArrangedSubview(tagsLabel)
        contentStackView.addArrangedSubview(confidenceLabel)
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16))
        }
        
        postImageView.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview().inset(12)
            make.width.equalTo(80)
            make.height.equalTo(80)
        }
        
        contentStackView.snp.makeConstraints { make in
            make.left.equalTo(postImageView.snp.right).offset(12)
            make.top.equalToSuperview().offset(12)
            make.bottom.lessThanOrEqualToSuperview().offset(-12)
        }
        
        chevronImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(16)
            make.left.greaterThanOrEqualTo(contentStackView.snp.right).offset(8)
        }
    }
    
    // MARK: - Configuration
    func configure(with post: PostData) {
        // Load and set image
        if let image = post.getImage() {
            postImageView.image = image
        } else {
            postImageView.image = UIImage(systemName: "photo")
            postImageView.tintColor = .systemGray3
        }
        
        // Set caption (clean version without hashtags)
        captionLabel.text = post.cleanCaption.isEmpty ? "No caption" : post.cleanCaption
        
        // Set content type with emoji
        let contentType = ImageContentType(rawValue: post.contentType) ?? .unknown
        contentTypeLabel.text = "\(contentType.emoji) \(post.displayContentType)"
        
        // Set timestamp
        timestampLabel.text = post.formattedTimestamp
        
        // Set tags
        if post.tags.isEmpty {
            tagsLabel.text = "No tags"
            tagsLabel.textColor = .systemGray
        } else {
            let tagText = post.tags.prefix(3).map { "#\($0)" }.joined(separator: " ")
            let additionalCount = max(0, post.tags.count - 3)
            tagsLabel.text = additionalCount > 0 ? "\(tagText) +\(additionalCount)" : tagText
            tagsLabel.textColor = .systemBlue
        }
        
        // Set confidence
        confidenceLabel.text = "AI: \(post.confidencePercentage)"
        
        // Update confidence color based on value
        if post.confidence >= 0.8 {
            confidenceLabel.textColor = .systemGreen
        } else if post.confidence >= 0.6 {
            confidenceLabel.textColor = .systemOrange
        } else {
            confidenceLabel.textColor = .systemRed
        }
    }
    
    // MARK: - Reuse
    override func prepareForReuse() {
        super.prepareForReuse()
        postImageView.image = nil
        captionLabel.text = nil
        contentTypeLabel.text = nil
        timestampLabel.text = nil
        tagsLabel.text = nil
        confidenceLabel.text = nil
    }
    
    // MARK: - Highlight
    override func setHighlighted(_ highlighted: Bool, animated: Bool) {
        super.setHighlighted(highlighted, animated: animated)
        
        UIView.animate(withDuration: 0.1) {
            self.containerView.transform = highlighted ? CGAffineTransform(scaleX: 0.98, y: 0.98) : .identity
            self.containerView.alpha = highlighted ? 0.8 : 1.0
        }
    }
}
