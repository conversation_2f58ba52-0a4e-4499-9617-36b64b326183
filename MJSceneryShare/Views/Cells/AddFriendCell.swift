import UIKit
import SnapKit

class AddFriendCell: UITableViewCell {
    
    static let identifier = "AddFriendCell"
    
    weak var delegate: AddFriendCellDelegate?
    private var currentUser: Friend?
    
    // MARK: - UI Components
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 12 // Reduced from 16 to 12
        view.layer.shadowColor = UIColor.systemPink.cgColor
        view.layer.shadowOpacity = 0.08
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 4
        return view
    }()
    
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 28 // Half of 56px for perfect circle
        imageView.backgroundColor = .systemGray5
        imageView.tintColor = .systemPink
        imageView.layer.borderWidth = 2
        imageView.layer.borderColor = UIColor.systemPink.withAlphaComponent(0.3).cgColor
        return imageView
    }()
    
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .label
        return label
    }()
    
    private lazy var usernameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .systemPink
        return label
    }()
    
    private lazy var bioLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 13)
        label.textColor = .secondaryLabel
        label.numberOfLines = 1
        return label
    }()
    
    private lazy var mutualFriendsLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12)
        label.textColor = .tertiaryLabel
        return label
    }()
    
    private lazy var addButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Add", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemPink
        button.layer.cornerRadius = 16
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .semibold)
        button.addTarget(self, action: #selector(addButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var statusIndicator: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 4
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.systemBackground.cgColor
        view.isHidden = true
        return view
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        containerView.addSubview(avatarImageView)
        containerView.addSubview(statusIndicator)
        containerView.addSubview(nameLabel)
        containerView.addSubview(usernameLabel)
        containerView.addSubview(bioLabel)
        containerView.addSubview(mutualFriendsLabel)
        containerView.addSubview(addButton)
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 4, left: 16, bottom: 4, right: 16))
        }
        
        avatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(56) // Increased from 44 to 56
        }
        
        statusIndicator.snp.makeConstraints { make in
            make.bottom.right.equalTo(avatarImageView)
            make.width.height.equalTo(8)
        }
        
        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(12)
            make.top.equalTo(avatarImageView.snp.top)
            make.right.lessThanOrEqualTo(addButton.snp.left).offset(-8)
        }
        
        usernameLabel.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(1)
            make.right.lessThanOrEqualTo(addButton.snp.left).offset(-8)
        }
        
        bioLabel.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(usernameLabel.snp.bottom).offset(2)
            make.right.lessThanOrEqualTo(addButton.snp.left).offset(-8)
        }
        
        mutualFriendsLabel.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.bottom.equalTo(avatarImageView.snp.bottom)
            make.right.lessThanOrEqualTo(addButton.snp.left).offset(-8)
        }
        
        addButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(60)
            make.height.equalTo(32)
        }
    }
    
    // MARK: - Configuration
    func configure(with user: Friend) {
        currentUser = user

        // Set avatar using AvatarManager
        avatarImageView.image = AvatarManager.shared.getAvatarImageWithFallback(named: user.avatar)

        // Ensure corner radius is applied after image is set for perfect circle
        avatarImageView.layer.cornerRadius = 28

        // Set user info
        nameLabel.text = user.displayName
        usernameLabel.text = "@\(user.username)"
        bioLabel.text = user.bio ?? "BeautySpots user ✨"
        
        // Set mutual friends
        if user.mutualFriends > 0 {
            mutualFriendsLabel.text = "\(user.mutualFriends) mutual friend\(user.mutualFriends > 1 ? "s" : "")"
            mutualFriendsLabel.isHidden = false
        } else {
            mutualFriendsLabel.isHidden = true
        }
        
        // Configure status indicator
        configureStatusIndicator(for: user.status)
        
        // Add entrance animation
        animateAppearance()
    }
    
    private func configureStatusIndicator(for status: FriendStatus) {
        switch status {
        case .online:
            statusIndicator.backgroundColor = .systemGreen
            statusIndicator.isHidden = false
        case .recentlyActive:
            statusIndicator.backgroundColor = .systemOrange
            statusIndicator.isHidden = false
        case .doNotDisturb:
            statusIndicator.backgroundColor = .systemPurple
            statusIndicator.isHidden = false
        case .offline:
            statusIndicator.isHidden = true
        }
    }
    
    private func animateAppearance() {
        containerView.alpha = 0
        containerView.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        
        UIView.animate(withDuration: 0.3, delay: 0, options: [.curveEaseOut], animations: {
            self.containerView.alpha = 1
            self.containerView.transform = .identity
        })
    }
    
    // MARK: - Actions
    @objc private func addButtonTapped() {
        guard let user = currentUser else { return }
        
        // Add button animation
        UIView.animate(withDuration: 0.1, animations: {
            self.addButton.transform = CGAffineTransform(scaleX: 1.1, y: 1.1)
        }) { _ in
            UIView.animate(withDuration: 0.1, animations: {
                self.addButton.transform = .identity
            }) { _ in
                self.delegate?.addFriendCell(self, didTapAddFriend: user)
            }
        }
    }
    
    // MARK: - Reuse
    override func prepareForReuse() {
        super.prepareForReuse()
        currentUser = nil
        avatarImageView.image = nil
        nameLabel.text = nil
        usernameLabel.text = nil
        bioLabel.text = nil
        mutualFriendsLabel.text = nil
        mutualFriendsLabel.isHidden = false
        statusIndicator.isHidden = true
        containerView.alpha = 1
        containerView.transform = .identity
        addButton.transform = .identity
    }
}

// MARK: - QR Code Cell
class QRCodeCell: UITableViewCell {
    
    static let identifier = "QRCodeCell"
    
    weak var delegate: QRCodeCellDelegate?
    
    // MARK: - UI Components
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.systemPink.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 4
        return view
    }()
    
    private lazy var qrIconImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "qrcode")
        imageView.tintColor = .systemPink
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "Quick Add with QR Code"
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .label
        return label
    }()
    
    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = "Show your QR code or scan someone else's"
        label.font = .systemFont(ofSize: 13)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private lazy var showQRButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Show QR", for: .normal)
        button.setTitleColor(.systemPink, for: .normal)
        button.backgroundColor = .systemPink.withAlphaComponent(0.1)
        button.layer.cornerRadius = 12
        button.titleLabel?.font = .systemFont(ofSize: 12, weight: .semibold)
        button.addTarget(self, action: #selector(showQRTapped), for: .touchUpInside)
        return button
    }()
    

    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        containerView.addSubview(qrIconImageView)
        containerView.addSubview(titleLabel)
        containerView.addSubview(subtitleLabel)
        containerView.addSubview(showQRButton)
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 8, left: 16, bottom: 8, right: 16))
        }
        
        qrIconImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(32)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(qrIconImageView.snp.right).offset(12)
            make.top.equalToSuperview().offset(12)
            make.right.lessThanOrEqualTo(showQRButton.snp.left).offset(-8)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(2)
            make.right.lessThanOrEqualTo(showQRButton.snp.left).offset(-8)
            make.bottom.equalToSuperview().offset(-12)
        }
        
        showQRButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.equalTo(70)
            make.height.equalTo(28)
        }
    }
    
    // MARK: - Actions
    @objc private func showQRTapped() {
        delegate?.qrCodeCellDidTapShowQR(self)
    }
}
