import UIKit
import SnapKit

class ShareToFriendCell: UITableViewCell {
    
    static let identifier = "ShareToFriendCell"
    
    // MARK: - UI Components
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .secondarySystemGroupedBackground
        view.layer.cornerRadius = 12
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.05
        view.layer.shadowOffset = CGSize(width: 0, height: 1)
        view.layer.shadowRadius = 2
        return view
    }()
    
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 25
        imageView.backgroundColor = .systemGray5
        imageView.tintColor = .systemPink
        return imageView
    }()
    
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .label
        return label
    }()
    
    private lazy var usernameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private lazy var checkmarkImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "circle")
        imageView.tintColor = .systemGray3
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private lazy var statusIndicator: UIView = {
        let view = UIView()
        view.backgroundColor = .systemGreen
        view.layer.cornerRadius = 6
        view.layer.borderWidth = 2
        view.layer.borderColor = UIColor.systemBackground.cgColor
        view.isHidden = true
        return view
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        containerView.addSubview(avatarImageView)
        containerView.addSubview(nameLabel)
        containerView.addSubview(usernameLabel)
        containerView.addSubview(checkmarkImageView)
        containerView.addSubview(statusIndicator)
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.top.bottom.equalToSuperview().inset(4)
            make.left.right.equalToSuperview().inset(16)
        }
        
        avatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(50)
        }
        
        statusIndicator.snp.makeConstraints { make in
            make.bottom.right.equalTo(avatarImageView)
            make.width.height.equalTo(12)
        }
        
        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(12)
            make.top.equalTo(avatarImageView.snp.top).offset(4)
            make.right.equalTo(checkmarkImageView.snp.left).offset(-12)
        }
        
        usernameLabel.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(2)
            make.right.equalTo(nameLabel)
        }
        
        checkmarkImageView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-12)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(24)
        }
    }
    
    // MARK: - Configuration
    func configure(with friend: Friend, isSelected: Bool) {
        // Set avatar using AvatarManager
        avatarImageView.image = AvatarManager.shared.getAvatarImageWithFallback(named: friend.avatar)
        
        // Set friend info
        nameLabel.text = friend.displayName
        usernameLabel.text = "@\(friend.username)"
        
        // Update selection state
        updateSelectionState(isSelected: isSelected)
        
        // Show online status if friend is online
        statusIndicator.isHidden = !friend.isOnline
        
        // Add entrance animation
        animateAppearance()
    }
    
    private func updateSelectionState(isSelected: Bool) {
        if isSelected {
            checkmarkImageView.image = UIImage(systemName: "checkmark.circle.fill")
            checkmarkImageView.tintColor = .systemPink
            containerView.layer.borderWidth = 2
            containerView.layer.borderColor = UIColor.systemPink.withAlphaComponent(0.3).cgColor
            containerView.backgroundColor = .systemPink.withAlphaComponent(0.05)
        } else {
            checkmarkImageView.image = UIImage(systemName: "circle")
            checkmarkImageView.tintColor = .systemGray3
            containerView.layer.borderWidth = 0
            containerView.backgroundColor = .secondarySystemGroupedBackground
        }
        
        // Add selection animation
        UIView.animate(withDuration: 0.2, delay: 0, options: [.curveEaseInOut], animations: {
            self.checkmarkImageView.transform = isSelected ? 
                CGAffineTransform(scaleX: 1.2, y: 1.2) : .identity
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.checkmarkImageView.transform = .identity
            }
        }
    }
    
    private func animateAppearance() {
        containerView.alpha = 0
        containerView.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        
        UIView.animate(withDuration: 0.3, delay: 0, options: [.curveEaseOut], animations: {
            self.containerView.alpha = 1
            self.containerView.transform = .identity
        })
    }
    
    // MARK: - Reuse
    override func prepareForReuse() {
        super.prepareForReuse()
        avatarImageView.image = nil
        nameLabel.text = nil
        usernameLabel.text = nil
        statusIndicator.isHidden = true
        updateSelectionState(isSelected: false)
    }
}
