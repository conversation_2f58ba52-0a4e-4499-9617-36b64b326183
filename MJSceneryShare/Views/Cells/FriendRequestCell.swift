import UIKit
import SnapKit

class FriendRequestCell: UITableViewCell {
    
    static let identifier = "FriendRequestCell"
    
    weak var delegate: FriendRequestCellDelegate?
    private var currentRequest: FriendRequest?
    private var isReceivedRequest = true
    
    // MARK: - UI Components
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 12 // Reduced from 16 to 12
        view.layer.shadowColor = UIColor.systemPink.cgColor
        view.layer.shadowOpacity = 0.08
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 4
        return view
    }()
    
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 30 // Half of 60px for perfect circle
        imageView.backgroundColor = .systemGray5
        imageView.tintColor = .systemPink
        imageView.layer.borderWidth = 2
        imageView.layer.borderColor = UIColor.systemPink.withAlphaComponent(0.3).cgColor
        return imageView
    }()
    
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .label
        return label
    }()
    
    private lazy var usernameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .systemPink
        return label
    }()
    
    private lazy var bioLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 13)
        label.textColor = .secondaryLabel
        label.numberOfLines = 1
        return label
    }()
    
    private lazy var messageLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 13, weight: .medium)
        label.textColor = .label
        label.numberOfLines = 2
        label.isHidden = true
        return label
    }()
    
    private lazy var mutualFriendsLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12)
        label.textColor = .tertiaryLabel
        return label
    }()
    
    private lazy var timeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12)
        label.textColor = .tertiaryLabel
        return label
    }()
    
    private lazy var acceptButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Accept", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemPink
        button.layer.cornerRadius = 16
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .semibold)
        button.addTarget(self, action: #selector(acceptButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var declineButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Decline", for: .normal)
        button.setTitleColor(.systemPink, for: .normal)
        button.backgroundColor = .systemPink.withAlphaComponent(0.1)
        button.layer.cornerRadius = 16
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.systemPink.withAlphaComponent(0.3).cgColor
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .semibold)
        button.addTarget(self, action: #selector(declineButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var cancelButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Cancel", for: .normal)
        button.setTitleColor(.systemRed, for: .normal)
        button.backgroundColor = .systemRed.withAlphaComponent(0.1)
        button.layer.cornerRadius = 16
        button.layer.borderWidth = 1
        button.layer.borderColor = UIColor.systemRed.withAlphaComponent(0.3).cgColor
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .semibold)
        button.addTarget(self, action: #selector(cancelButtonTapped), for: .touchUpInside)
        button.isHidden = true
        return button
    }()
    
    private lazy var pendingLabel: UILabel = {
        let label = UILabel()
        label.text = "Pending..."
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .systemOrange
        label.isHidden = true
        return label
    }()
    
    private lazy var buttonsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 8
        stackView.distribution = .fillEqually
        return stackView
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
        setupGestures()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        containerView.addSubview(avatarImageView)
        containerView.addSubview(nameLabel)
        containerView.addSubview(usernameLabel)
        containerView.addSubview(bioLabel)
        containerView.addSubview(messageLabel)
        containerView.addSubview(mutualFriendsLabel)
        containerView.addSubview(timeLabel)
        containerView.addSubview(buttonsStackView)
        containerView.addSubview(pendingLabel)
        
        buttonsStackView.addArrangedSubview(acceptButton)
        buttonsStackView.addArrangedSubview(declineButton)
        buttonsStackView.addArrangedSubview(cancelButton)
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 4, left: 16, bottom: 4, right: 16))
        }
        
        avatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.top.equalToSuperview().offset(16)
            make.width.height.equalTo(60) // Increased from 50 to 60
        }
        
        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(12)
            make.top.equalTo(avatarImageView.snp.top)
            make.right.lessThanOrEqualTo(timeLabel.snp.left).offset(-8)
        }
        
        usernameLabel.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(nameLabel.snp.bottom).offset(1)
            make.right.lessThanOrEqualTo(timeLabel.snp.left).offset(-8)
        }
        
        bioLabel.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(usernameLabel.snp.bottom).offset(2)
            make.right.lessThanOrEqualToSuperview().offset(-16)
        }
        
        messageLabel.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(bioLabel.snp.bottom).offset(4)
            make.right.lessThanOrEqualToSuperview().offset(-16)
        }
        
        mutualFriendsLabel.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.top.equalTo(messageLabel.snp.bottom).offset(2)
            make.right.lessThanOrEqualToSuperview().offset(-16)
        }
        
        timeLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.top.equalToSuperview().offset(16)
        }
        
        buttonsStackView.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-16)
            make.width.equalTo(140)
            make.height.equalTo(32)
        }
        
        pendingLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    private func setupGestures() {
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(profileTapped))
        avatarImageView.addGestureRecognizer(tapGesture)
        avatarImageView.isUserInteractionEnabled = true
    }
    
    // MARK: - Configuration
    func configure(with request: FriendRequest, isReceived: Bool) {
        currentRequest = request
        isReceivedRequest = isReceived

        let user = request.senderInfo

        // Set avatar using AvatarManager
        avatarImageView.image = AvatarManager.shared.getAvatarImageWithFallback(named: user.avatar)

        // Ensure corner radius is applied after image is set for perfect circle
        avatarImageView.layer.cornerRadius = 30

        // Set user info
        nameLabel.text = user.displayName
        usernameLabel.text = "@\(user.username)"
        bioLabel.text = user.bio ?? "BeautySpots user ✨"
        
        // Set message if present
        if let message = request.message, !message.isEmpty {
            messageLabel.text = "💬 \"\(message)\""
            messageLabel.isHidden = false
        } else {
            messageLabel.isHidden = true
        }
        
        // Set mutual friends
        if user.mutualFriends > 0 {
            mutualFriendsLabel.text = "\(user.mutualFriends) mutual friend\(user.mutualFriends > 1 ? "s" : "")"
            mutualFriendsLabel.isHidden = false
        } else {
            mutualFriendsLabel.isHidden = true
        }
        
        // Set time
        timeLabel.text = formatDate(request.createdDate)
        
        // Configure buttons based on type
        configureButtons(for: isReceived)
        
        // Add entrance animation
        animateAppearance()
    }
    
    private func configureButtons(for isReceived: Bool) {
        if isReceived {
            // Received request - show accept/decline buttons
            acceptButton.isHidden = false
            declineButton.isHidden = false
            cancelButton.isHidden = true
            pendingLabel.isHidden = true
            buttonsStackView.isHidden = false
        } else {
            // Sent request - show cancel button or pending label
            acceptButton.isHidden = true
            declineButton.isHidden = true
            cancelButton.isHidden = false
            pendingLabel.isHidden = true
            buttonsStackView.isHidden = false
        }
    }
    
    private func formatDate(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "en_US")
        return formatter.localizedString(for: date, relativeTo: Date())
    }
    
    private func animateAppearance() {
        containerView.alpha = 0
        containerView.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        
        UIView.animate(withDuration: 0.3, delay: 0, options: [.curveEaseOut], animations: {
            self.containerView.alpha = 1
            self.containerView.transform = .identity
        })
    }
    
    // MARK: - Actions
    @objc private func acceptButtonTapped() {
        guard let request = currentRequest else { return }
        
        // Add button animation
        UIView.animate(withDuration: 0.1, animations: {
            self.acceptButton.transform = CGAffineTransform(scaleX: 1.1, y: 1.1)
        }) { _ in
            UIView.animate(withDuration: 0.1, animations: {
                self.acceptButton.transform = .identity
            }) { _ in
                self.delegate?.friendRequestCell(self, didTapAccept: request)
            }
        }
    }
    
    @objc private func declineButtonTapped() {
        guard let request = currentRequest else { return }
        
        // Add button animation
        UIView.animate(withDuration: 0.1, animations: {
            self.declineButton.transform = CGAffineTransform(scaleX: 1.1, y: 1.1)
        }) { _ in
            UIView.animate(withDuration: 0.1, animations: {
                self.declineButton.transform = .identity
            }) { _ in
                self.delegate?.friendRequestCell(self, didTapDecline: request)
            }
        }
    }
    
    @objc private func cancelButtonTapped() {
        guard let request = currentRequest else { return }
        
        // Add button animation
        UIView.animate(withDuration: 0.1, animations: {
            self.cancelButton.transform = CGAffineTransform(scaleX: 1.1, y: 1.1)
        }) { _ in
            UIView.animate(withDuration: 0.1, animations: {
                self.cancelButton.transform = .identity
            }) { _ in
                self.delegate?.friendRequestCell(self, didTapCancel: request)
            }
        }
    }
    
    @objc private func profileTapped() {
        guard let request = currentRequest else { return }
        
        // Add avatar animation
        UIView.animate(withDuration: 0.1, animations: {
            self.avatarImageView.transform = CGAffineTransform(scaleX: 1.1, y: 1.1)
        }) { _ in
            UIView.animate(withDuration: 0.1, animations: {
                self.avatarImageView.transform = .identity
            }) { _ in
                self.delegate?.friendRequestCell(self, didTapProfile: request)
            }
        }
    }
    
    // MARK: - Reuse
    override func prepareForReuse() {
        super.prepareForReuse()
        currentRequest = nil
        avatarImageView.image = nil
        nameLabel.text = nil
        usernameLabel.text = nil
        bioLabel.text = nil
        messageLabel.text = nil
        messageLabel.isHidden = true
        mutualFriendsLabel.text = nil
        mutualFriendsLabel.isHidden = false
        timeLabel.text = nil
        acceptButton.isHidden = false
        declineButton.isHidden = false
        cancelButton.isHidden = true
        pendingLabel.isHidden = true
        buttonsStackView.isHidden = false
        containerView.alpha = 1
        containerView.transform = .identity
        acceptButton.transform = .identity
        declineButton.transform = .identity
        cancelButton.transform = .identity
        avatarImageView.transform = .identity
    }
}
