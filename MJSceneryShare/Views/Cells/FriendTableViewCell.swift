import UIKit
import SnapKit

class FriendTableViewCell: UITableViewCell {
    
    static let identifier = "FriendTableViewCell"
    
    weak var delegate: FriendTableViewCellDelegate?
    private var currentFriend: Friend?
    
    // MARK: - UI Components
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 12 // Reduced from 16 to 12
        view.layer.shadowColor = UIColor.systemPink.cgColor
        view.layer.shadowOpacity = 0.08
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 4
        return view
    }()
    
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 28 // Half of 56px for perfect circle
        imageView.backgroundColor = .systemGray5
        imageView.tintColor = .systemPink
        imageView.layer.borderWidth = 2
        imageView.layer.borderColor = UIColor.systemPink.withAlphaComponent(0.3).cgColor
        return imageView
    }()
    
    private lazy var statusIndicator: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 6
        view.layer.borderWidth = 2
        view.layer.borderColor = UIColor.systemBackground.cgColor
        view.isHidden = true
        return view
    }()
    
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .label
        return label
    }()
    
    private lazy var statusLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 13, weight: .medium)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private lazy var unreadBadge: UIView = {
        let view = UIView()
        view.backgroundColor = .systemPink
        view.layer.cornerRadius = 8
        view.isHidden = true
        return view
    }()
    
    private lazy var unreadCountLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .bold)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    private lazy var messageButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "message.circle"), for: .normal)
        button.tintColor = .systemPink
        button.addTarget(self, action: #selector(messageButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var sharePhotoButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "photo.circle"), for: .normal)
        button.tintColor = .systemPink
        button.addTarget(self, action: #selector(sharePhotoButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Initialization
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        backgroundColor = .clear
        selectionStyle = .none

        contentView.addSubview(containerView)
        containerView.addSubview(avatarImageView)
        containerView.addSubview(statusIndicator)
        containerView.addSubview(nameLabel)
        containerView.addSubview(statusLabel)
        // Remove unread badge from UI setup
        // containerView.addSubview(unreadBadge)
        // unreadBadge.addSubview(unreadCountLabel)
        containerView.addSubview(messageButton)
        containerView.addSubview(sharePhotoButton)

        // Add tap gesture for profile viewing
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(profileTapped))
        avatarImageView.addGestureRecognizer(tapGesture)
        avatarImageView.isUserInteractionEnabled = true
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(UIEdgeInsets(top: 4, left: 16, bottom: 4, right: 16))
        }
        
        avatarImageView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(56) // Increased from 44 to 56
        }
        
        statusIndicator.snp.makeConstraints { make in
            make.bottom.right.equalTo(avatarImageView)
            make.width.height.equalTo(12)
        }
        
        nameLabel.snp.makeConstraints { make in
            make.left.equalTo(avatarImageView.snp.right).offset(12)
            make.centerY.equalToSuperview().offset(-10) // Center vertically with slight offset up
            make.right.lessThanOrEqualTo(messageButton.snp.left).offset(-8)
        }

        statusLabel.snp.makeConstraints { make in
            make.left.equalTo(nameLabel)
            make.centerY.equalToSuperview().offset(10) // Center vertically with slight offset down
            make.right.lessThanOrEqualTo(messageButton.snp.left).offset(-8)
        }
        
        sharePhotoButton.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(32)
        }
        
        messageButton.snp.makeConstraints { make in
            make.right.equalTo(sharePhotoButton.snp.left).offset(-8)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(32)
        }
    }
    
    // MARK: - Configuration
    func configure(with friend: Friend) {
        currentFriend = friend

        // Set avatar using AvatarManager
        avatarImageView.image = AvatarManager.shared.getAvatarImageWithFallback(named: friend.avatar)

        // Ensure corner radius is applied after image is set for perfect circle
        avatarImageView.layer.cornerRadius = 28

        // Set name and status
        nameLabel.text = friend.displayName
        statusLabel.text = friend.statusDisplayText
        
        // Configure status indicator
        configureStatusIndicator(for: friend.status)

        // Remove unread badge configuration - no longer showing message count badges

        // Add subtle animation
        animateAppearance()
    }
    
    private func configureStatusIndicator(for status: FriendStatus) {
        switch status {
        case .online:
            statusIndicator.backgroundColor = .systemGreen
            statusIndicator.isHidden = false
        case .recentlyActive:
            statusIndicator.backgroundColor = .systemOrange
            statusIndicator.isHidden = false
        case .doNotDisturb:
            statusIndicator.backgroundColor = .systemPurple
            statusIndicator.isHidden = false
        case .offline:
            statusIndicator.isHidden = true
        }
    }
    
    private func animateAppearance() {
        containerView.alpha = 0
        containerView.transform = CGAffineTransform(scaleX: 0.95, y: 0.95)
        
        UIView.animate(withDuration: 0.3, delay: 0, options: [.curveEaseOut], animations: {
            self.containerView.alpha = 1
            self.containerView.transform = .identity
        })
    }
    
    // MARK: - Actions
    @objc private func messageButtonTapped() {
        guard let friend = currentFriend else { return }
        
        // Add button animation
        UIView.animate(withDuration: 0.1, animations: {
            self.messageButton.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.messageButton.transform = .identity
            }
        }
        
        delegate?.friendCell(self, didTapMessage: friend)
    }
    
    @objc private func sharePhotoButtonTapped() {
        guard let friend = currentFriend else { return }
        
        // Add button animation
        UIView.animate(withDuration: 0.1, animations: {
            self.sharePhotoButton.transform = CGAffineTransform(scaleX: 1.2, y: 1.2)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.sharePhotoButton.transform = .identity
            }
        }
        
        delegate?.friendCell(self, didTapSharePhoto: friend)
    }
    
    @objc private func profileTapped() {
        guard let friend = currentFriend else { return }
        
        // Add avatar animation
        UIView.animate(withDuration: 0.1, animations: {
            self.avatarImageView.transform = CGAffineTransform(scaleX: 1.1, y: 1.1)
        }) { _ in
            UIView.animate(withDuration: 0.1) {
                self.avatarImageView.transform = .identity
            }
        }
        
        delegate?.friendCell(self, didTapProfile: friend)
    }
    
    // MARK: - Reuse
    override func prepareForReuse() {
        super.prepareForReuse()
        currentFriend = nil
        avatarImageView.image = nil
        nameLabel.text = nil
        statusLabel.text = nil
        // Remove unreadBadge reset - no longer using message count badges
        statusIndicator.isHidden = true
        containerView.alpha = 1
        containerView.transform = .identity
    }
}
