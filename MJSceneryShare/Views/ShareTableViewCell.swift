import UIKit
import SnapKit

protocol ShareTableViewCellDelegate: AnyObject {
    func shareCell(_ cell: ShareTableViewCell, didTapLike share: Share)
    func shareCell(_ cell: ShareTableViewCell, didTapComment share: Share)
    func shareCell(_ cell: ShareTableViewCell, didTapShare share: Share)
    func shareCell(_ cell: ShareTableViewCell, didTapFavorite share: Share)
    func shareCell(_ cell: ShareTableViewCell, didTapReport share: Share)
    func shareCell(_ cell: ShareTableViewCell, didTapBlock share: Share)
}

class ShareTableViewCell: UITableViewCell {
    
    static let identifier = "ShareTableViewCell"
    
    weak var delegate: ShareTableViewCellDelegate?
    private var currentShare: Share?
    
    private lazy var containerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.systemPink.cgColor
        view.layer.shadowOpacity = 0.08
        view.layer.shadowOffset = CGSize(width: 0, height: 4)
        view.layer.shadowRadius = 8
        return view
    }()
    
    private lazy var shareImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.backgroundColor = .systemGray6
        imageView.layer.cornerRadius = 12
        return imageView
    }()

    private lazy var placeholderIconView: UIImageView = {
        let imageView = UIImageView()
        let config = UIImage.SymbolConfiguration(pointSize: 70, weight: .light)
        imageView.image = UIImage(systemName: "mountain.2", withConfiguration: config)
        imageView.tintColor = .systemGray4
        imageView.contentMode = .scaleAspectFit
        imageView.isHidden = true
        return imageView
    }()
    
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 20 // Half of 40px for perfect circle
        imageView.backgroundColor = .systemGray5
        imageView.tintColor = .systemPink
        // Add subtle border for elegance
        imageView.layer.borderWidth = 2
        imageView.layer.borderColor = UIColor.systemPink.withAlphaComponent(0.3).cgColor
        return imageView
    }()
    
    private lazy var nicknameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 15, weight: .semibold)
        label.textColor = .label
        return label
    }()
    
    private lazy var regionLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private lazy var timeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12)
        label.textColor = .secondaryLabel
        label.textAlignment = .right
        return label
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .bold)
        label.numberOfLines = 2
        return label
    }()
    
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .secondaryLabel
        label.numberOfLines = 3
        return label
    }()
    
    private lazy var actionStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 0
        return stackView
    }()
    
    private lazy var likeButton: UIButton = {
        let button = createActionButton(image: "heart", title: "0")
        button.addTarget(self, action: #selector(likeButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var commentButton: UIButton = {
        let button = createActionButton(image: "message", title: "0")
        button.addTarget(self, action: #selector(commentButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var shareButton: UIButton = {
        let button = createActionButton(image: "square.and.arrow.up", title: "")
        button.addTarget(self, action: #selector(shareButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var favoriteButton: UIButton = {
        let button = createActionButton(image: "star", title: "0")
        button.addTarget(self, action: #selector(favoriteButtonTapped), for: .touchUpInside)
        return button
    }()

    private lazy var moreButton: UIButton = {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: "ellipsis"), for: .normal)
        button.tintColor = .secondaryLabel
        button.addTarget(self, action: #selector(moreButtonTapped), for: .touchUpInside)
        return button
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        contentView.backgroundColor = .systemGroupedBackground
        selectionStyle = .none
        
        contentView.addSubview(containerView)
        containerView.addSubview(shareImageView)
        shareImageView.addSubview(placeholderIconView)
        containerView.addSubview(avatarImageView)
        containerView.addSubview(nicknameLabel)
        containerView.addSubview(regionLabel)
        containerView.addSubview(timeLabel)
        containerView.addSubview(moreButton)
        containerView.addSubview(titleLabel)
        containerView.addSubview(descriptionLabel)
        containerView.addSubview(actionStackView)
        
        actionStackView.addArrangedSubview(likeButton)
        actionStackView.addArrangedSubview(commentButton)
        actionStackView.addArrangedSubview(favoriteButton)
        actionStackView.addArrangedSubview(shareButton)
    }
    
    private func setupConstraints() {
        containerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(8)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-8)
        }
        
        shareImageView.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
            make.height.equalTo(250)
        }

        placeholderIconView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.width.height.equalTo(100)
        }
        
        avatarImageView.snp.makeConstraints { make in
            make.top.equalTo(shareImageView.snp.bottom).offset(16) // Increased from 12 to 16
            make.left.equalToSuperview().offset(16) // Increased from 12 to 16
            make.width.height.equalTo(40) // Increased from 30 to 40
        }
        
        nicknameLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView).offset(2) // Add slight offset from top
            make.left.equalTo(avatarImageView.snp.right).offset(12) // Increased from 8 to 12
        }

        regionLabel.snp.makeConstraints { make in
            make.bottom.equalTo(avatarImageView).offset(-2) // Add slight offset from bottom
            make.left.equalTo(nicknameLabel)
        }

        timeLabel.snp.makeConstraints { make in
            make.centerY.equalTo(avatarImageView)
            make.right.equalTo(moreButton.snp.left).offset(-12) // Increased from -8 to -12
        }

        moreButton.snp.makeConstraints { make in
            make.centerY.equalTo(avatarImageView)
            make.right.equalToSuperview().offset(-16) // Increased from -12 to -16
            make.width.height.equalTo(32) // Increased from 30 to 32
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView.snp.bottom).offset(16) // Increased from 12 to 16
            make.left.right.equalToSuperview().inset(16) // Increased from 12 to 16
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(16) // Increased from 12 to 16
        }

        actionStackView.snp.makeConstraints { make in
            make.top.equalTo(descriptionLabel.snp.bottom).offset(16) // Increased from 12 to 16
            make.left.right.equalToSuperview().inset(16) // Increased from 12 to 16
            make.height.equalTo(44) // Increased from 40 to 44
            make.bottom.equalToSuperview().offset(-16) // Increased from -12 to -16
        }
    }
    
    private func createActionButton(image: String, title: String) -> UIButton {
        let button = UIButton(type: .system)
        button.setImage(UIImage(systemName: image), for: .normal)
        button.setTitle(title, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14)
        button.tintColor = .secondaryLabel
        return button
    }
    
    func configure(with share: Share) {
        currentShare = share
        
        // 加载图片
        loadImage(from: share.imageURL)
        
        // 设置头像 using AvatarManager
        avatarImageView.image = AvatarManager.shared.getAvatarImageWithFallback(named: share.author.avatar)

        // Ensure corner radius is applied after image is set
        avatarImageView.layer.cornerRadius = 20
        
        nicknameLabel.text = share.author.nickname
        regionLabel.text = share.author.region
        timeLabel.text = formatDate(share.publishTime)
        titleLabel.text = share.title
        descriptionLabel.text = share.description
        
        // 更新按钮状态
        likeButton.setTitle(" \(share.likes)", for: .normal)
        likeButton.setImage(UIImage(systemName: share.isLiked ? "heart.fill" : "heart"), for: .normal)
        likeButton.tintColor = share.isLiked ? .systemRed : .secondaryLabel
        
        commentButton.setTitle(" \(share.comments)", for: .normal)
        
        favoriteButton.setTitle(" \(share.favorites)", for: .normal)
        favoriteButton.setImage(UIImage(systemName: share.isFavorited ? "star.fill" : "star"), for: .normal)
        favoriteButton.tintColor = share.isFavorited ? .systemYellow : .secondaryLabel
    }
    
    private func loadImage(from imageURL: String) {
        // Check if it's a local image
        if imageURL.hasPrefix("local://") {
            // Local image file - extract filename
            let fileName = String(imageURL.dropFirst(8)) // Remove "local://" prefix
            if let image = PostStorageManager.shared.loadImage(fileName: fileName) {
                shareImageView.image = image
                placeholderIconView.isHidden = true
            } else {
                shareImageView.image = nil
                placeholderIconView.isHidden = false
            }
        } else if imageURL.contains("Documents") {
            // Legacy local file path
            if let url = URL(string: imageURL),
               let image = UIImage(contentsOfFile: url.path) {
                shareImageView.image = image
                placeholderIconView.isHidden = true
            } else {
                shareImageView.image = nil
                placeholderIconView.isHidden = false
            }
        } else if let url = URL(string: imageURL) {
            // Use image caching to ensure consistency
            ImageCacheManager.shared.loadImage(from: url) { [weak self] image in
                DispatchQueue.main.async {
                    if let image = image {
                        self?.shareImageView.image = image
                        self?.placeholderIconView.isHidden = true
                    } else {
                        self?.shareImageView.image = nil
                        self?.placeholderIconView.isHidden = false
                    }
                }
            }
        } else {
            shareImageView.image = nil
            placeholderIconView.isHidden = false
        }
    }



    private func formatDate(_ date: Date) -> String {
        let formatter = RelativeDateTimeFormatter()
        formatter.locale = Locale(identifier: "en_US")
        return formatter.localizedString(for: date, relativeTo: Date())
    }
    
    @objc private func likeButtonTapped() {
        guard let share = currentShare else { return }
        delegate?.shareCell(self, didTapLike: share)
    }
    
    @objc private func commentButtonTapped() {
        guard let share = currentShare else { return }
        delegate?.shareCell(self, didTapComment: share)
    }
    
    @objc private func shareButtonTapped() {
        guard let share = currentShare else { return }
        delegate?.shareCell(self, didTapShare: share)
    }
    
    @objc private func favoriteButtonTapped() {
        guard let share = currentShare else { return }
        delegate?.shareCell(self, didTapFavorite: share)
    }

    @objc private func moreButtonTapped() {
        guard let share = currentShare else { return }
        showMoreOptions(for: share)
    }

    private func showMoreOptions(for share: Share) {
        guard let viewController = findViewController() else { return }

        let alertController = UIAlertController(title: nil, message: nil, preferredStyle: .actionSheet)

        // Report action
        let reportAction = UIAlertAction(title: "Report Content", style: .destructive) { [weak self] _ in
            guard let self = self else { return }
            self.delegate?.shareCell(self, didTapReport: share)
        }

        // Block action
        let blockAction = UIAlertAction(title: "Block User", style: .destructive) { [weak self] _ in
            guard let self = self else { return }
            self.delegate?.shareCell(self, didTapBlock: share)
        }

        let cancelAction = UIAlertAction(title: "Cancel", style: .cancel)

        alertController.addAction(reportAction)
        alertController.addAction(blockAction)
        alertController.addAction(cancelAction)

        // For iPad support
        if let popover = alertController.popoverPresentationController {
            popover.sourceView = moreButton
            popover.sourceRect = moreButton.bounds
        }

        viewController.present(alertController, animated: true)
    }
}

// MARK: - Helper Extension
extension UIView {
    func findViewController() -> UIViewController? {
        if let nextResponder = self.next as? UIViewController {
            return nextResponder
        } else if let nextResponder = self.next as? UIView {
            return nextResponder.findViewController()
        } else {
            return nil
        }
    }
}
