import UIKit
import SnapKit

class FriendProfileViewController: UIViewController {
    
    // MARK: - Properties
    private let friend: Friend
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.alwaysBounceVertical = true
        return scrollView
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var headerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 20
        view.layer.shadowColor = UIColor.systemPink.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 4)
        view.layer.shadowRadius = 8
        return view
    }()
    
    private lazy var avatarImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 60 // Increased from 50 to 60
        imageView.backgroundColor = .systemGray5
        imageView.tintColor = .systemPink
        imageView.layer.borderWidth = 4
        imageView.layer.borderColor = UIColor.systemPink.withAlphaComponent(0.3).cgColor
        return imageView
    }()
    
    private lazy var nameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 24, weight: .bold)
        label.textColor = .label
        label.textAlignment = .center
        return label
    }()
    
    private lazy var usernameLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textColor = .systemPink
        label.textAlignment = .center
        return label
    }()
    
    private lazy var bioLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16)
        label.textColor = .secondaryLabel
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var locationLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .tertiaryLabel
        label.textAlignment = .center
        return label
    }()
    
    private lazy var statusLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .secondaryLabel
        label.textAlignment = .center
        return label
    }()
    
    private lazy var actionsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.spacing = 12
        stackView.distribution = .fillEqually
        return stackView
    }()
    
    private lazy var messageButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Message", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemPink
        button.layer.cornerRadius = 20
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .semibold)
        button.addTarget(self, action: #selector(messageButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var sharePhotoButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Share Photo", for: .normal)
        button.setTitleColor(.systemPink, for: .normal)
        button.backgroundColor = .systemPink.withAlphaComponent(0.1)
        button.layer.cornerRadius = 20
        button.layer.borderWidth = 2
        button.layer.borderColor = UIColor.systemPink.withAlphaComponent(0.3).cgColor
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .semibold)
        button.addTarget(self, action: #selector(sharePhotoButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var mutualFriendsView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.systemPink.cgColor
        view.layer.shadowOpacity = 0.08
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 4
        return view
    }()
    
    private lazy var mutualFriendsLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .label
        return label
    }()
    
    // MARK: - Initialization
    init(friend: Friend) {
        self.friend = friend
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        configureWithFriend()
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        contentView.addSubview(headerView)
        headerView.addSubview(avatarImageView)
        headerView.addSubview(nameLabel)
        headerView.addSubview(usernameLabel)
        headerView.addSubview(bioLabel)
        headerView.addSubview(locationLabel)
        headerView.addSubview(statusLabel)

        contentView.addSubview(actionsStackView)
        actionsStackView.addArrangedSubview(messageButton)
        actionsStackView.addArrangedSubview(sharePhotoButton)
        
        contentView.addSubview(mutualFriendsView)
        mutualFriendsView.addSubview(mutualFriendsLabel)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        headerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.right.equalToSuperview().inset(16)
        }
        
        avatarImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(24)
            make.centerX.equalToSuperview()
            make.width.height.equalTo(120) // Increased from 100 to 120
        }
        
        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(20)
        }
        
        usernameLabel.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(4)
            make.left.right.equalToSuperview().inset(20)
        }
        
        bioLabel.snp.makeConstraints { make in
            make.top.equalTo(usernameLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(20)
        }
        
        locationLabel.snp.makeConstraints { make in
            make.top.equalTo(bioLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(20)
        }
        
        statusLabel.snp.makeConstraints { make in
            make.top.equalTo(locationLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-24)
        }

        actionsStackView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(44)
        }
        
        mutualFriendsView.snp.makeConstraints { make in
            make.top.equalTo(actionsStackView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(60)
            make.bottom.equalToSuperview().offset(-20)
        }
        
        mutualFriendsLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(20)
        }
    }
    
    private func setupNavigationBar() {
        title = friend.displayName
        navigationController?.navigationBar.tintColor = .systemPink
        
        let moreButton = UIBarButtonItem(
            image: UIImage(systemName: "ellipsis.circle"),
            style: .plain,
            target: self,
            action: #selector(moreButtonTapped)
        )
        navigationItem.rightBarButtonItem = moreButton
    }
    
    private func configureWithFriend() {
        // Set avatar using AvatarManager
        avatarImageView.image = AvatarManager.shared.getAvatarImageWithFallback(named: friend.avatar)

        // Ensure corner radius is applied after image is set for perfect circle
        avatarImageView.layer.cornerRadius = 60 // Half of 120px for perfect circle

        // Set basic info
        nameLabel.text = friend.displayName
        usernameLabel.text = "@\(friend.username)"
        bioLabel.text = friend.bio
        locationLabel.text = "📍 \(friend.location ?? "Unknown")"
        statusLabel.text = friend.statusDisplayText
        
        // Set mutual friends
        if friend.mutualFriends > 0 {
            mutualFriendsLabel.text = "\(friend.mutualFriends) mutual friend\(friend.mutualFriends > 1 ? "s" : "") 👥"
        } else {
            mutualFriendsLabel.text = "No mutual friends yet"
        }
        
        // Add entrance animation
        animateAppearance()
    }
    
    private func animateAppearance() {
        headerView.alpha = 0
        headerView.transform = CGAffineTransform(translationX: 0, y: 50)
        
        UIView.animate(withDuration: 0.6, delay: 0, usingSpringWithDamping: 0.8, initialSpringVelocity: 0.5, options: [.curveEaseOut], animations: {
            self.headerView.alpha = 1
            self.headerView.transform = .identity
        })
    }
    
    // MARK: - Actions
    @objc private func messageButtonTapped() {
        let chatVC = ChatViewController(friend: friend)
        navigationController?.pushViewController(chatVC, animated: true)
    }
    
    @objc private func sharePhotoButtonTapped() {
        let photoSharingVC = PhotoSharingViewController(friend: friend)
        let navController = UINavigationController(rootViewController: photoSharingVC)
        present(navController, animated: true)
    }
    
    @objc private func moreButtonTapped() {
        let alert = UIAlertController(title: nil, message: nil, preferredStyle: .actionSheet)
        
        alert.addAction(UIAlertAction(title: "Block User", style: .destructive) { _ in
            self.showBlockConfirmation()
        })
        
        alert.addAction(UIAlertAction(title: "Report User", style: .destructive) { _ in
            self.showReportOptions()
        })
        
        alert.addAction(UIAlertAction(title: "Remove Friend", style: .destructive) { _ in
            self.showRemoveFriendConfirmation()
        })
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        if let popover = alert.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItem
        }
        
        present(alert, animated: true)
    }
    
    private func showBlockConfirmation() {
        let alert = UIAlertController(
            title: "Block \(friend.displayName)?",
            message: "They won't be able to message you or see your posts.",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Block", style: .destructive) { _ in
            // Remove friend from friends list
            FriendsManager.shared.removeFriend(userId: self.friend.userId)

            // Clear friend activity data
            FriendActivityManager.shared.clearActivity(for: self.friend.userId)

            // Clear all chat messages with this friend
            self.clearChatHistory(with: self.friend)

            // Show success message and go back
            self.showActionCompletedMessage(action: "blocked") {
                self.navigationController?.popViewController(animated: true)
            }
        })

        present(alert, animated: true)
    }
    
    private func showReportOptions() {
        let alert = UIAlertController(title: "Report User", message: "Why are you reporting this user?", preferredStyle: .actionSheet)
        
        alert.addAction(UIAlertAction(title: "Inappropriate Content", style: .default) { _ in
            self.submitReport(reason: "Inappropriate Content")
        })
        
        alert.addAction(UIAlertAction(title: "Harassment", style: .default) { _ in
            self.submitReport(reason: "Harassment")
        })
        
        alert.addAction(UIAlertAction(title: "Spam", style: .default) { _ in
            self.submitReport(reason: "Spam")
        })
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        if let popover = alert.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItem
        }
        
        present(alert, animated: true)
    }
    
    private func showRemoveFriendConfirmation() {
        let alert = UIAlertController(
            title: "Remove \(friend.displayName)?",
            message: "You can always add them back later.",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Remove", style: .destructive) { _ in
            // Remove friend from friends list
            FriendsManager.shared.removeFriend(userId: self.friend.userId)

            // Clear friend activity data
            FriendActivityManager.shared.clearActivity(for: self.friend.userId)

            // Clear all chat messages with this friend
            self.clearChatHistory(with: self.friend)

            // Show success message and go back
            self.showActionCompletedMessage(action: "removed") {
                self.navigationController?.popViewController(animated: true)
            }
        })

        present(alert, animated: true)
    }
    
    private func submitReport(reason: String) {
        // Remove friend from friends list when reporting
        FriendsManager.shared.removeFriend(userId: self.friend.userId)

        // Clear friend activity data
        FriendActivityManager.shared.clearActivity(for: self.friend.userId)

        // Clear all chat messages with this friend
        clearChatHistory(with: self.friend)

        let alert = UIAlertController(
            title: "Report Submitted",
            message: "Thank you for helping keep BeautySpots safe. We'll review this report.",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "OK", style: .default) { _ in
            self.navigationController?.popViewController(animated: true)
        })
        present(alert, animated: true)
    }

    private func clearChatHistory(with friend: Friend) {
        let conversationId = "conv_\(friend.userId)"

        // Delete all messages for this conversation
        ChatStorageManager.shared.deleteMessages(for: conversationId)

        // Delete conversation info
        ChatStorageManager.shared.deleteConversationInfo(for: conversationId)

        print("🗑️ [FriendProfile] Cleared chat history with \(friend.displayName) (conversation: \(conversationId))")
    }

    private func showActionCompletedMessage(action: String, completion: @escaping () -> Void) {
        let alert = UIAlertController(
            title: "Friend \(action.capitalized)",
            message: "\(friend.displayName) has been \(action) and removed from your friends list. All chat history has been cleared.",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "OK", style: .default) { _ in
            completion()
        })
        present(alert, animated: true)
    }
}
