import UIKit
import SnapKit
import SafariServices
import WebKit

class ProfileViewController: UIViewController {
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        return tableView
    }()
    
    private enum Section: Int, CaseIterable {
        case profile
        case account
        case userInfo
        case app
        case logout
        case danger

        var title: String? {
            switch self {
            case .profile: return nil
            case .account: return "Account"
            case .userInfo: return "User Information"
            case .app: return "App"
            case .logout: return nil
            case .danger: return "Danger Zone"
            }
        }
    }

    private enum UserInfoRow: Int, CaseIterable {
        case email
        case loginType
        case registrationDate
        case lastLogin

        var title: String {
            switch self {
            case .email: return "Email"
            case .loginType: return "Login Type"
            case .registrationDate: return "Member Since"
            case .lastLogin: return "Last Login"
            }
        }

        var icon: String {
            switch self {
            case .email: return "envelope"
            case .loginType: return "person.badge.key"
            case .registrationDate: return "calendar.badge.plus"
            case .lastLogin: return "clock"
            }
        }
    }
    
    private enum AccountRow: Int, CaseIterable {
        case myPosts
        case myLikes
        case myFavorites
        case coinAnalytics
        case purchase

        var title: String {
            switch self {
            case .myPosts: return "My Posts"
            case .myLikes: return "My Likes"
            case .myFavorites: return "My Favorites"
            case .coinAnalytics: return "Coin Analytics"
            case .purchase: return "Get Coins"
            }
        }

        var icon: String {
            switch self {
            case .myPosts: return "photo.on.rectangle.angled"
            case .myLikes: return "heart.fill"
            case .myFavorites: return "star.fill"
            case .coinAnalytics: return "chart.bar.fill"
            case .purchase: return "sparkles"
            }
        }
    }
    
    private enum AppRow: Int, CaseIterable {
        case about
        case privacy
        case terms
        
        var title: String {
            switch self {
            case .about: return "About"
            case .privacy: return "Privacy Policy"
            case .terms: return "Terms of Service"
            }
        }

        var icon: String {
            switch self {
            case .about: return "info.circle"
            case .privacy: return "lock.shield"
            case .terms: return "doc.text"
            }
        }
    }
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        setupConstraints()
        setupNavigationBar()
        setupNotifications()
    }
    
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        view.addSubview(tableView)
        setupTableHeader()
    }

    private func setupTableHeader() {
        let isLoggedIn = UserAuthenticationManager.shared.isUserLoggedIn()

        if isLoggedIn {
            setupLoggedInHeader()
        } else {
            setupNotLoggedInHeader()
        }
    }

    private func setupLoggedInHeader() {
        let headerView = UIView(frame: CGRect(x: 0, y: 0, width: view.bounds.width, height: 350))

        let containerView = UIView()
        containerView.layer.cornerRadius = 24
        containerView.clipsToBounds = true

        // Create gradient background
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.systemPink.cgColor,
            UIColor.systemPurple.cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        gradientLayer.cornerRadius = 24
        containerView.layer.insertSublayer(gradientLayer, at: 0)

        // Set gradient frame after layout
        DispatchQueue.main.async {
            gradientLayer.frame = containerView.bounds
            gradientLayer.cornerRadius = 24
        }

        let avatarImageView = UIImageView()
        avatarImageView.contentMode = .scaleAspectFill
        avatarImageView.clipsToBounds = true
        avatarImageView.backgroundColor = .clear
        avatarImageView.layer.borderWidth = 4
        avatarImageView.layer.borderColor = UIColor.white.cgColor
        avatarImageView.layer.masksToBounds = true
        avatarImageView.translatesAutoresizingMaskIntoConstraints = false

        let nameLabel = UILabel()
        nameLabel.font = .systemFont(ofSize: 24, weight: .bold)
        nameLabel.textColor = .white
        nameLabel.textAlignment = .center

        let statsStackView = UIStackView()
        statsStackView.axis = .horizontal
        statsStackView.distribution = .fillEqually
        statsStackView.spacing = 16

        // Logged in user stats
        let coinsView = createStatView(title: "Coins", value: "\(AppViewModel.shared.currentUser.coins)", icon: "💰", action: #selector(coinsStatTapped))
        let postsCount = PostStorageManager.shared.loadAllPosts().count
        let postsView = createStatView(title: "Posts", value: "\(postsCount)", icon: "📸", action: #selector(postsStatTapped))
        let likesCount = AppViewModel.shared.likedShares.count
        let likesView = createStatView(title: "Likes", value: "\(likesCount)", icon: "❤️", action: #selector(likesStatTapped))

        statsStackView.addArrangedSubview(coinsView)
        statsStackView.addArrangedSubview(postsView)
        statsStackView.addArrangedSubview(likesView)

        headerView.addSubview(containerView)
        containerView.addSubview(avatarImageView)
        containerView.addSubview(nameLabel)
        containerView.addSubview(statsStackView)

        containerView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
            make.height.greaterThanOrEqualTo(320)
        }

        avatarImageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(24)
            make.centerX.equalToSuperview()
            make.width.equalTo(120).priority(.required)
            make.height.equalTo(120).priority(.required)
            make.width.equalTo(avatarImageView.snp.height)
        }

        nameLabel.snp.makeConstraints { make in
            make.top.equalTo(avatarImageView.snp.bottom).offset(16)
            make.left.right.equalToSuperview().inset(20)
        }

        statsStackView.snp.makeConstraints { make in
            make.top.equalTo(nameLabel.snp.bottom).offset(24)
            make.left.right.equalToSuperview().inset(24)
            make.bottom.equalToSuperview().offset(-24)
            make.height.equalTo(70).priority(.high)
        }

        // Set avatar and name
        avatarImageView.image = AvatarManager.shared.getAvatarImageWithFallback(named: AppViewModel.shared.currentUser.avatar)
        nameLabel.text = AppViewModel.shared.currentUser.nickname
        avatarImageView.layer.cornerRadius = 60

        headerView.layoutIfNeeded()
        tableView.tableHeaderView = headerView

        DispatchQueue.main.async {
            let size = headerView.systemLayoutSizeFitting(UIView.layoutFittingCompressedSize)
            headerView.frame.size.height = size.height
            self.tableView.tableHeaderView = headerView
        }
    }

    private func setupNotLoggedInHeader() {
        let headerView = UIView(frame: CGRect(x: 0, y: 0, width: view.bounds.width, height: 280))

        let containerView = UIView()
        containerView.layer.cornerRadius = 24
        containerView.clipsToBounds = true

        // Create gradient background
        let gradientLayer = CAGradientLayer()
        gradientLayer.colors = [
            UIColor.systemPink.cgColor,
            UIColor.systemPurple.cgColor
        ]
        gradientLayer.startPoint = CGPoint(x: 0, y: 0)
        gradientLayer.endPoint = CGPoint(x: 1, y: 1)
        gradientLayer.cornerRadius = 24
        containerView.layer.insertSublayer(gradientLayer, at: 0)

        // Set gradient frame after layout
        DispatchQueue.main.async {
            gradientLayer.frame = containerView.bounds
            gradientLayer.cornerRadius = 24
        }

        let welcomeLabel = UILabel()
        welcomeLabel.text = "Welcome to BeautySpots"
        welcomeLabel.font = .systemFont(ofSize: 22, weight: .bold)
        welcomeLabel.textColor = .white
        welcomeLabel.textAlignment = .center

        let subtitleLabel = UILabel()
        subtitleLabel.text = "Sign in to unlock all features"
        subtitleLabel.font = .systemFont(ofSize: 16, weight: .medium)
        subtitleLabel.textColor = UIColor.white.withAlphaComponent(0.9)
        subtitleLabel.textAlignment = .center

        let loginButton = UIButton(type: .system)
        loginButton.setTitle("Sign In", for: .normal)
        loginButton.setTitleColor(.systemPink, for: .normal)
        loginButton.titleLabel?.font = .systemFont(ofSize: 20, weight: .bold)
        loginButton.backgroundColor = .white
        loginButton.layer.cornerRadius = 25
        loginButton.layer.shadowColor = UIColor.black.cgColor
        loginButton.layer.shadowOpacity = 0.2
        loginButton.layer.shadowOffset = CGSize(width: 0, height: 4)
        loginButton.layer.shadowRadius = 8
        loginButton.addTarget(self, action: #selector(loginButtonTapped), for: .touchUpInside)

        headerView.addSubview(containerView)
        containerView.addSubview(welcomeLabel)
        containerView.addSubview(subtitleLabel)
        containerView.addSubview(loginButton)

        containerView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
            make.height.greaterThanOrEqualTo(250)
        }

        welcomeLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(40)
            make.left.right.equalToSuperview().inset(20)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(welcomeLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(20)
        }

        loginButton.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(32)
            make.centerX.equalToSuperview()
            make.width.equalTo(200)
            make.height.equalTo(50)
            make.bottom.lessThanOrEqualToSuperview().offset(-40)
        }

        headerView.layoutIfNeeded()
        tableView.tableHeaderView = headerView

        DispatchQueue.main.async {
            let size = headerView.systemLayoutSizeFitting(UIView.layoutFittingCompressedSize)
            headerView.frame.size.height = size.height
            self.tableView.tableHeaderView = headerView
        }
    }

    private func createStatView(title: String, value: String, icon: String, action: Selector? = nil) -> UIView {
        let containerView = UIView()
        containerView.backgroundColor = .white.withAlphaComponent(0.25)
        containerView.layer.cornerRadius = 16
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOpacity = 0.1
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowRadius = 4

        let iconLabel = UILabel()
        iconLabel.text = icon
        iconLabel.font = .systemFont(ofSize: 24)
        iconLabel.textAlignment = .center

        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = .systemFont(ofSize: 20, weight: .bold)
        valueLabel.textColor = .white
        valueLabel.textAlignment = .center

        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 13, weight: .medium)
        titleLabel.textColor = .white.withAlphaComponent(0.9)
        titleLabel.textAlignment = .center

        containerView.addSubview(iconLabel)
        containerView.addSubview(valueLabel)
        containerView.addSubview(titleLabel)

        iconLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(10)
            make.centerX.equalToSuperview()
        }

        valueLabel.snp.makeConstraints { make in
            make.top.equalTo(iconLabel.snp.bottom).offset(4)
            make.centerX.equalToSuperview()
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(valueLabel.snp.bottom).offset(3)
            make.centerX.equalToSuperview()
            make.bottom.equalToSuperview().offset(-10)
        }

        // Add tap gesture if action is provided
        if let action = action {
            let tapGesture = UITapGestureRecognizer(target: self, action: action)
            containerView.addGestureRecognizer(tapGesture)
            containerView.isUserInteractionEnabled = true

            // Add visual feedback for tappable views
            containerView.layer.borderWidth = 1
            containerView.layer.borderColor = UIColor.white.withAlphaComponent(0.3).cgColor
        }

        return containerView
    }
    
    private func setupConstraints() {
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    private func setupNavigationBar() {
        title = "Profile"
        navigationController?.navigationBar.prefersLargeTitles = true
        navigationController?.navigationBar.tintColor = .systemPink

        // Add coins button to navigation bar
        let coinsButton = UIBarButtonItem(title: "💰 0", style: .plain, target: self, action: #selector(coinsPurchaseButtonTapped))
        coinsButton.setTitleTextAttributes([
            .foregroundColor: UIColor.systemPink,
            .font: UIFont.systemFont(ofSize: 16, weight: .semibold)
        ], for: .normal)

        navigationItem.rightBarButtonItem = coinsButton
        updateCoinsDisplay()
    }
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(self, selector: #selector(handleUserChange), name: .userDidChange, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(handleDataUpdate), name: .postsDidUpdate, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(handleDataUpdate), name: .coinBalanceDidUpdate, object: nil)
    }

    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        refreshProfileHeader()
        updateCoinsDisplay()
    }
    
    @objc private func handleUserChange() {
        // Ensure UI updates happen on main thread to prevent crashes
        DispatchQueue.main.async {
            self.refreshProfileHeader()
            self.updateCoinsDisplay()
            // Reload entire table view to handle login state changes safely
            self.tableView.reloadData()
        }
    }

    @objc private func handleDataUpdate() {
        refreshProfileHeader()
        updateCoinsDisplay()
    }

    private func refreshProfileHeader() {
        setupTableHeader()

        // Update gradient layer frame after layout
        DispatchQueue.main.async {
            if let headerView = self.tableView.tableHeaderView,
               let containerView = headerView.subviews.first,
               let gradientLayer = containerView.layer.sublayers?.first as? CAGradientLayer {
                gradientLayer.frame = containerView.bounds
            }
        }
    }

    private func updateCoinsDisplay() {
        let isLoggedIn = UserAuthenticationManager.shared.isUserLoggedIn()

        if isLoggedIn {
            // 已登录：创建或更新硬币按钮
            let user = AppViewModel.shared.currentUser
            let coinText = "💰 \(user.coins)"

            // 显示总AI次数，包括免费和购买的
            let totalAI = user.totalAvailableProcessing
            let aiText = totalAI > 0 ? " (🤖\(totalAI))" : ""

            if navigationItem.rightBarButtonItem == nil {
                // 重新创建按钮
                let coinsButton = UIBarButtonItem(title: coinText + aiText, style: .plain, target: self, action: #selector(coinsPurchaseButtonTapped))
                coinsButton.setTitleTextAttributes([
                    .foregroundColor: UIColor.systemPink,
                    .font: UIFont.systemFont(ofSize: 16, weight: .semibold)
                ], for: .normal)
                navigationItem.rightBarButtonItem = coinsButton
            } else {
                // 更新现有按钮
                navigationItem.rightBarButtonItem?.title = coinText + aiText
            }
            navigationItem.rightBarButtonItem?.isEnabled = true
        } else {
            // 未登录时不显示右上角按钮
            navigationItem.rightBarButtonItem = nil
        }
    }

    @objc private func coinsPurchaseButtonTapped() {
        let isLoggedIn = UserAuthenticationManager.shared.isUserLoggedIn()

        if isLoggedIn {
            // Show purchase page for logged in users
            let purchaseVC = PurchaseViewController()
            let navController = UINavigationController(rootViewController: purchaseVC)
            navController.modalPresentationStyle = .pageSheet

            // Configure the sheet presentation
            if let sheet = navController.sheetPresentationController {
                sheet.detents = [.large()]
                sheet.prefersGrabberVisible = true
                sheet.preferredCornerRadius = 20
            }

            present(navController, animated: true)
        } else {
            // Show login page for not logged in users
            loginButtonTapped()
        }
    }

    @objc private func loginButtonTapped() {
        let loginVC = LoginViewController()
        let navController = UINavigationController(rootViewController: loginVC)
        navController.modalPresentationStyle = .fullScreen
        present(navController, animated: true)
    }

    // MARK: - Stat Button Actions
    @objc private func coinsStatTapped() {
        // Navigate to coin purchase page
        let purchaseVC = PurchaseViewController()
        navigationController?.pushViewController(purchaseVC, animated: true)
    }

    @objc private func postsStatTapped() {
        // Navigate to my posts page
        let myPostsVC = MyPostsViewController()
        navigationController?.pushViewController(myPostsVC, animated: true)
    }

    @objc private func likesStatTapped() {
        // Navigate to my likes page
        let myLikesVC = MyLikesViewController()
        navigationController?.pushViewController(myLikesVC, animated: true)
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - UITableViewDataSource
extension ProfileViewController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return Section.allCases.count
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        guard let sectionType = Section(rawValue: section) else { return 0 }

        let isLoggedIn = UserAuthenticationManager.shared.isUserLoggedIn()

        switch sectionType {
        case .profile: return 1
        case .userInfo: return isLoggedIn ? UserInfoRow.allCases.count : 0 // Hide user info section when not logged in
        case .account: return isLoggedIn ? AccountRow.allCases.count : 1 // Show only Get Coins when not logged in
        case .app: return AppRow.allCases.count
        case .logout: return isLoggedIn ? 1 : 0 // Hide logout when not logged in
        case .danger: return isLoggedIn ? 1 : 0 // Hide delete account when not logged in
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        guard let sectionType = Section(rawValue: indexPath.section) else {
            return UITableViewCell()
        }
        
        switch sectionType {
        case .profile:
            let cell = UITableViewCell(style: .subtitle, reuseIdentifier: nil)
            let user = AppViewModel.shared.currentUser

            // Display user information based on login type
            if user.isAppleUser {
                cell.textLabel?.text = user.nickname
                cell.textLabel?.font = .systemFont(ofSize: 18, weight: .semibold)

                // Show email and login type for Apple users
                let emailText = user.displayEmail
                let loginTypeText = user.loginTypeDisplayName
                cell.detailTextLabel?.text = "\(emailText) • \(loginTypeText)"
                cell.detailTextLabel?.textColor = .secondaryLabel
                cell.detailTextLabel?.font = .systemFont(ofSize: 14)
            } else {
                // Guest user display
                cell.textLabel?.text = user.nickname
                cell.textLabel?.font = .systemFont(ofSize: 18, weight: .semibold)
                cell.detailTextLabel?.text = user.loginTypeDisplayName
                cell.detailTextLabel?.textColor = .secondaryLabel
                cell.detailTextLabel?.font = .systemFont(ofSize: 14)
            }

            cell.accessoryType = .none // Remove arrow since this cell is not clickable
            cell.selectionStyle = .none // Disable selection since it's not interactive
            return cell

        case .userInfo:
            // Only show user info when logged in (section returns 0 rows when not logged in)
            let cell = UITableViewCell(style: .value1, reuseIdentifier: nil)
            let user = AppViewModel.shared.currentUser

            if let row = UserInfoRow(rawValue: indexPath.row) {
                cell.textLabel?.text = row.title
                cell.imageView?.image = UIImage(systemName: row.icon)
                cell.imageView?.tintColor = .systemPink

                switch row {
                case .email:
                    cell.detailTextLabel?.text = user.displayEmail
                case .loginType:
                    cell.detailTextLabel?.text = user.loginTypeDisplayName
                case .registrationDate:
                    let formatter = DateFormatter()
                    formatter.dateStyle = .medium
                    cell.detailTextLabel?.text = formatter.string(from: user.registrationDate)
                case .lastLogin:
                    if let lastLogin = user.lastLoginDate {
                        let formatter = DateFormatter()
                        formatter.dateStyle = .short
                        formatter.timeStyle = .short
                        cell.detailTextLabel?.text = formatter.string(from: lastLogin)
                    } else {
                        cell.detailTextLabel?.text = "Never"
                    }
                }

                cell.detailTextLabel?.textColor = .secondaryLabel
                cell.selectionStyle = .none
            }
            return cell

        case .account:
            let cell = UITableViewCell(style: .default, reuseIdentifier: nil)
            let isLoggedIn = UserAuthenticationManager.shared.isUserLoggedIn()

            if isLoggedIn {
                // 已登录：显示所有账户选项
                if let row = AccountRow(rawValue: indexPath.row) {
                    cell.textLabel?.text = row.title
                    cell.imageView?.image = UIImage(systemName: row.icon)
                    cell.accessoryType = .disclosureIndicator
                }
            } else {
                // 未登录：只显示 Get Coins
                cell.textLabel?.text = "Get Coins"
                cell.imageView?.image = UIImage(systemName: "dollarsign.circle")
                cell.accessoryType = .disclosureIndicator
            }
            return cell
            
        case .app:
            let cell = UITableViewCell(style: .default, reuseIdentifier: nil)
            if let row = AppRow(rawValue: indexPath.row) {
                cell.textLabel?.text = row.title
                cell.imageView?.image = UIImage(systemName: row.icon)
                cell.accessoryType = .disclosureIndicator
            }
            return cell
            
        case .logout:
            let cell = UITableViewCell(style: .default, reuseIdentifier: nil)
            cell.textLabel?.text = "Sign Out"
            cell.textLabel?.textAlignment = .center
            cell.textLabel?.textColor = .systemRed
            return cell

        case .danger:
            let cell = UITableViewCell(style: .default, reuseIdentifier: nil)
            cell.textLabel?.text = "Delete Account"
            cell.textLabel?.textAlignment = .center
            cell.textLabel?.textColor = .systemRed
            cell.textLabel?.font = .systemFont(ofSize: 16, weight: .medium)
            cell.imageView?.image = UIImage(systemName: "trash.circle")
            cell.imageView?.tintColor = .systemRed
            return cell
        }
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        guard let sectionType = Section(rawValue: section) else { return nil }
        let isLoggedIn = UserAuthenticationManager.shared.isUserLoggedIn()

        // 未登录时隐藏特定表头
        if !isLoggedIn {
            switch sectionType {
            case .userInfo, .danger:
                return nil // 不显示表头
            case .profile, .account, .app, .logout:
                return sectionType.title
            }
        }

        // 已登录时显示所有表头
        return sectionType.title
    }
}

// MARK: - UITableViewDelegate
extension ProfileViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        guard let sectionType = Section(rawValue: indexPath.section) else { return }
        
        switch sectionType {
        case .profile:
            break

        case .userInfo:
            // User info cells are not interactive
            break

        case .account:
            let isLoggedIn = UserAuthenticationManager.shared.isUserLoggedIn()

            if isLoggedIn {
                // 已登录：处理所有账户选项
                if let row = AccountRow(rawValue: indexPath.row) {
                    switch row {
                    case .myPosts:
                        let myPostsVC = MyPostsViewController()
                        navigationController?.pushViewController(myPostsVC, animated: true)
                    case .myLikes:
                        let myLikesVC = MyLikesViewController()
                        navigationController?.pushViewController(myLikesVC, animated: true)
                    case .myFavorites:
                        let myFavoritesVC = MyFavoritesViewController()
                        navigationController?.pushViewController(myFavoritesVC, animated: true)
                    case .coinAnalytics:
                        let analyticsVC = CoinAnalyticsViewController()
                        navigationController?.pushViewController(analyticsVC, animated: true)
                    case .purchase:
                        let purchaseVC = PurchaseViewController()
                        navigationController?.pushViewController(purchaseVC, animated: true)
                    }
                }
            } else {
                // 未登录：只处理 Get Coins（直接进入购买页面）
                let purchaseVC = PurchaseViewController()
                navigationController?.pushViewController(purchaseVC, animated: true)
            }
            
        case .app:
            if let row = AppRow(rawValue: indexPath.row) {
                switch row {
                case .about:
                    showAboutApp()
                    break
                case .privacy:
                    showPrivacyPolicy()
                case .terms:
                    showTermsOfService()
                }
            }
            
        case .logout:
            showLogoutAlert()

        case .danger:
            showDeleteAccountAlert()
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if indexPath.section == Section.profile.rawValue {
            return 80
        }
        return 44
    }
    
    private func showAboutApp() {
        let aboutVC = AboutAppViewController()
        navigationController?.pushViewController(aboutVC, animated: true)
    }

    private func showPrivacyPolicy() {
        if let path = Bundle.main.path(forResource: "privacy_policy", ofType: "html") {
            let url = URL(fileURLWithPath: path)
            showWebViewController(url: url, title: "Privacy Policy")
        } else {
            // Fallback to a simple alert with privacy information
            let alert = UIAlertController(
                title: "Privacy Policy",
                message: "Your privacy is important to us. All data is stored locally on your device. AI processing is done securely and no personal data is shared with third parties.",
                preferredStyle: .alert
            )
            alert.addAction(UIAlertAction(title: "OK", style: .default))
            present(alert, animated: true)
        }
    }

    private func showTermsOfService() {
        if let path = Bundle.main.path(forResource: "terms_of_service", ofType: "html") {
            let url = URL(fileURLWithPath: path)
            showWebViewController(url: url, title: "Terms of Service")
        } else {
            // Fallback to a simple alert with terms information
            let alert = UIAlertController(
                title: "Terms of Service",
                message: "By using this app, you agree to our terms. The app provides photo sharing and AI processing features. Please use responsibly.",
                preferredStyle: .alert
            )
            alert.addAction(UIAlertAction(title: "OK", style: .default))
            present(alert, animated: true)
        }
    }

    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    private func showWebViewController(url: URL, title: String) {
        let webViewController = WebViewController(url: url, title: title)
        let navController = UINavigationController(rootViewController: webViewController)
        present(navController, animated: true)
    }

    private func showLogoutAlert() {
        let alert = UIAlertController(title: "Sign Out", message: "Are you sure you want to sign out?", preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Sign Out", style: .destructive) { _ in
            AppViewModel.shared.logout()

            // Refresh the profile view to show logged out state on main thread
            DispatchQueue.main.async {
                self.refreshProfileHeader()
                self.updateCoinsDisplay()
                self.tableView.reloadData()
            }
        })
        present(alert, animated: true)
    }



    private func showDeleteAccountAlert() {
        let alert = UIAlertController(
            title: "Delete Account",
            message: "This action cannot be undone. All your data, photos, and account information will be permanently deleted.",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Delete Account", style: .destructive) { _ in
            self.confirmDeleteAccount()
        })

        present(alert, animated: true)
    }

    private func confirmDeleteAccount() {
        let confirmAlert = UIAlertController(
            title: "Final Confirmation",
            message: "Type 'DELETE' to confirm account deletion",
            preferredStyle: .alert
        )

        confirmAlert.addTextField { textField in
            textField.placeholder = "Type DELETE here"
            textField.autocapitalizationType = .allCharacters
        }

        confirmAlert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        confirmAlert.addAction(UIAlertAction(title: "Delete Forever", style: .destructive) { _ in
            guard let textField = confirmAlert.textFields?.first,
                  let text = textField.text,
                  text.uppercased() == "DELETE" else {
                self.showDeleteAccountError()
                return
            }

            self.performAccountDeletion()
        })

        present(confirmAlert, animated: true)
    }

    private func showDeleteAccountError() {
        let errorAlert = UIAlertController(
            title: "Incorrect Input",
            message: "Please type 'DELETE' exactly to confirm account deletion.",
            preferredStyle: .alert
        )
        errorAlert.addAction(UIAlertAction(title: "OK", style: .default) { _ in
            self.confirmDeleteAccount() // Show the confirmation dialog again
        })
        present(errorAlert, animated: true)
    }

    private func performAccountDeletion() {
        // Show loading indicator
        let loadingAlert = UIAlertController(title: "Deleting Account...", message: "Please wait", preferredStyle: .alert)
        present(loadingAlert, animated: true)

        // Simulate account deletion process
        DispatchQueue.main.asyncAfter(deadline: .now() + 2.0) {
            loadingAlert.dismiss(animated: true) {
                // Clear all user data
                AppViewModel.shared.deleteAccount()

                // Show success message and update UI to logged out state
                let successAlert = UIAlertController(
                    title: "Account Deleted",
                    message: "Your account has been permanently deleted.",
                    preferredStyle: .alert
                )
                successAlert.addAction(UIAlertAction(title: "OK", style: .default) { _ in
                    // Update UI to show logged out state
                    DispatchQueue.main.async {
                        self.refreshProfileHeader()
                        self.updateCoinsDisplay()
                        self.tableView.reloadData()
                    }
                })
                self.present(successAlert, animated: true)
            }
        }
    }
}



