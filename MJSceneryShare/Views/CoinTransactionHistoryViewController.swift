//
//  CoinTransactionHistoryViewController.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-04.
//

import UIKit
import SnapKit

class CoinTransactionHistoryViewController: UIViewController {
    
    // MARK: - Properties
    private var transactions: [CoinTransaction] = []
    private var statistics: CoinUsageStatistics?
    
    // MARK: - UI Components
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(TransactionTableViewCell.self, forCellReuseIdentifier: "TransactionCell")
        tableView.backgroundColor = .systemGroupedBackground
        return tableView
    }()
    
    private lazy var emptyStateView: UIView = {
        let view = UIView()
        view.isHidden = true
        return view
    }()
    
    private lazy var emptyImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "clock.arrow.circlepath")
        imageView.tintColor = .systemGray3
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private lazy var emptyLabel: UILabel = {
        let label = UILabel()
        label.text = "No Transactions Yet"
        label.font = .systemFont(ofSize: 18, weight: .semibold)
        label.textColor = .secondaryLabel
        label.textAlignment = .center
        return label
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        loadTransactions()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        
        view.addSubview(tableView)
        view.addSubview(emptyStateView)
        
        emptyStateView.addSubview(emptyImageView)
        emptyStateView.addSubview(emptyLabel)
        
        setupTableHeader()
    }
    
    private func setupConstraints() {
        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        emptyStateView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(40)
        }
        
        emptyImageView.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.width.height.equalTo(60)
        }
        
        emptyLabel.snp.makeConstraints { make in
            make.top.equalTo(emptyImageView.snp.bottom).offset(16)
            make.left.right.bottom.equalToSuperview()
        }
    }
    
    private func setupNavigationBar() {
        title = "Transaction History"
        navigationController?.navigationBar.tintColor = .systemPink
    }
    
    private func setupTableHeader() {
        guard let stats = statistics else { return }
        
        let headerView = UIView(frame: CGRect(x: 0, y: 0, width: view.bounds.width, height: 120))
        headerView.backgroundColor = .clear
        
        let containerView = UIView()
        containerView.backgroundColor = .systemBackground
        containerView.layer.cornerRadius = 12
        containerView.layer.shadowColor = UIColor.black.cgColor
        containerView.layer.shadowOpacity = 0.1
        containerView.layer.shadowOffset = CGSize(width: 0, height: 2)
        containerView.layer.shadowRadius = 4
        
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 16
        
        let totalSpentView = createStatView(title: "Total Spent", value: "\(stats.totalSpent)", color: .systemRed)
        let totalEarnedView = createStatView(title: "Total Earned", value: "\(stats.totalEarned)", color: .systemGreen)
        let processingCountView = createStatView(title: "AI Processing", value: "\(stats.aiProcessingCount)", color: .systemBlue)
        
        stackView.addArrangedSubview(totalSpentView)
        stackView.addArrangedSubview(totalEarnedView)
        stackView.addArrangedSubview(processingCountView)
        
        headerView.addSubview(containerView)
        containerView.addSubview(stackView)
        
        containerView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }
        
        stackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }
        
        tableView.tableHeaderView = headerView
    }
    
    private func createStatView(title: String, value: String, color: UIColor) -> UIView {
        let view = UIView()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 12)
        titleLabel.textColor = .secondaryLabel
        titleLabel.textAlignment = .center
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = .systemFont(ofSize: 20, weight: .bold)
        valueLabel.textColor = color
        valueLabel.textAlignment = .center
        
        view.addSubview(titleLabel)
        view.addSubview(valueLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview()
        }
        
        valueLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(4)
            make.left.right.bottom.equalToSuperview()
        }
        
        return view
    }
    
    // MARK: - Data Loading
    private func loadTransactions() {
        let userId = AppViewModel.shared.currentUser.id
        transactions = CoinTransactionManager.shared.getTransactionHistory(for: userId)
        statistics = CoinTransactionManager.shared.getStatistics(for: userId)
        
        setupTableHeader()
        updateEmptyState()
        tableView.reloadData()
    }
    
    private func updateEmptyState() {
        let isEmpty = transactions.isEmpty
        emptyStateView.isHidden = !isEmpty
        tableView.isHidden = isEmpty
    }
}

// MARK: - UITableViewDataSource
extension CoinTransactionHistoryViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return transactions.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "TransactionCell", for: indexPath) as! TransactionTableViewCell
        let transaction = transactions[indexPath.row]
        cell.configure(with: transaction)
        return cell
    }
}

// MARK: - UITableViewDelegate
extension CoinTransactionHistoryViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 70
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        return transactions.isEmpty ? nil : "Recent Transactions"
    }
}

// MARK: - Transaction Table View Cell
class TransactionTableViewCell: UITableViewCell {
    
    private lazy var iconView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 20
        view.backgroundColor = .systemGray6
        return view
    }()
    
    private lazy var iconLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16)
        label.textAlignment = .center
        return label
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textColor = .label
        return label
    }()
    
    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .secondaryLabel
        return label
    }()
    
    private lazy var amountLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textAlignment = .right
        return label
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        selectionStyle = .none
        
        contentView.addSubview(iconView)
        iconView.addSubview(iconLabel)
        contentView.addSubview(titleLabel)
        contentView.addSubview(subtitleLabel)
        contentView.addSubview(amountLabel)
        
        iconView.snp.makeConstraints { make in
            make.left.equalToSuperview().offset(16)
            make.centerY.equalToSuperview()
            make.width.height.equalTo(40)
        }
        
        iconLabel.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconView.snp.right).offset(12)
            make.top.equalToSuperview().offset(12)
            make.right.lessThanOrEqualTo(amountLabel.snp.left).offset(-8)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(2)
            make.right.lessThanOrEqualTo(amountLabel.snp.left).offset(-8)
        }
        
        amountLabel.snp.makeConstraints { make in
            make.right.equalToSuperview().offset(-16)
            make.centerY.equalToSuperview()
            make.width.greaterThanOrEqualTo(60)
        }
    }
    
    func configure(with transaction: CoinTransaction) {
        titleLabel.text = transaction.type.displayName
        subtitleLabel.text = transaction.formattedTimestamp
        amountLabel.text = transaction.formattedAmount
        
        // Set icon and colors based on transaction type
        switch transaction.type {
        case .purchase:
            iconLabel.text = "💰"
            iconView.backgroundColor = .systemGreen.withAlphaComponent(0.2)
            amountLabel.textColor = .systemGreen
            
        case .aiProcessing:
            iconLabel.text = "🤖"
            iconView.backgroundColor = .systemBlue.withAlphaComponent(0.2)
            amountLabel.textColor = .systemRed
            
        case .dailyBonus:
            iconLabel.text = "🎁"
            iconView.backgroundColor = .systemOrange.withAlphaComponent(0.2)
            amountLabel.textColor = .systemGreen

        case .contentPublishing:
            iconLabel.text = "📤"
            iconView.backgroundColor = .systemPurple.withAlphaComponent(0.2)
            amountLabel.textColor = .systemRed
            
        case .referralBonus:
            iconLabel.text = "👥"
            iconView.backgroundColor = .systemPurple.withAlphaComponent(0.2)
            amountLabel.textColor = .systemGreen
            
        case .refund:
            iconLabel.text = "↩️"
            iconView.backgroundColor = .systemGray.withAlphaComponent(0.2)
            amountLabel.textColor = .systemGreen
        }
    }
}
