//
//  CameraViewController.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-04.
//

import UIKit
import SnapKit
import AuthenticationServices
import Photos
import AVFoundation

class CameraViewController: UIViewController {
    
    // MARK: - Properties
    private var selectedImage: UIImage?
    private var analysisResult: ImageRecognitionResult?
    private var pendingSourceType: UIImagePickerController.SourceType?
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.alwaysBounceVertical = true
        scrollView.backgroundColor = .systemGroupedBackground
        return scrollView
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var headerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 8
        return view
    }()
    
    private lazy var titleLabel: UILabel = {
        let label = UILabel()
        label.text = "Publish New Post"
        label.font = .systemFont(ofSize: 24, weight: .bold)
        label.textColor = .label
        label.textAlignment = .center
        return label
    }()
    
    private lazy var subtitleLabel: UILabel = {
        let label = UILabel()
        label.text = "Capture or select a photo to publish with AI-powered insights"
        label.font = .systemFont(ofSize: 16)
        label.textColor = .secondaryLabel
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var imageContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemGray6
        view.layer.cornerRadius = 20
        view.clipsToBounds = true
        view.layer.borderWidth = 2
        view.layer.borderColor = UIColor.systemGray5.cgColor
        return view
    }()
    
    private lazy var selectedImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.isHidden = true
        return imageView
    }()
    
    private lazy var placeholderView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var placeholderImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "camera.fill")
        imageView.tintColor = .systemGray3
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private lazy var placeholderLabel: UILabel = {
        let label = UILabel()
        label.text = "Tap to add photo"
        label.font = .systemFont(ofSize: 18, weight: .medium)
        label.textColor = .systemGray3
        label.textAlignment = .center
        return label
    }()
    
    private lazy var actionButtonsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .horizontal
        stackView.distribution = .fillEqually
        stackView.spacing = 16
        return stackView
    }()
    
    private lazy var cameraButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("📷 Camera", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .semibold)
        button.backgroundColor = .systemPink
        button.layer.cornerRadius = 12
        button.addTarget(self, action: #selector(cameraButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var libraryButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("🖼️ Library", for: .normal)
        button.setTitleColor(.systemPink, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .semibold)
        button.backgroundColor = .clear
        button.layer.cornerRadius = 12
        button.layer.borderWidth = 2
        button.layer.borderColor = UIColor.systemPink.cgColor
        button.addTarget(self, action: #selector(libraryButtonTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var analysisContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 8
        view.isHidden = true
        return view
    }()
    
    private lazy var analysisLoadingView: UIView = {
        let view = UIView()
        view.isHidden = true
        return view
    }()
    
    private lazy var loadingIndicator: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .large)
        indicator.color = .systemPink
        return indicator
    }()
    
    private lazy var loadingLabel: UILabel = {
        let label = UILabel()
        label.text = "Analyzing image with AI..."
        label.font = .systemFont(ofSize: 16, weight: .medium)
        label.textColor = .secondaryLabel
        label.textAlignment = .center
        return label
    }()
    
    private lazy var continueButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Continue to Publish Post", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 18, weight: .semibold)
        button.backgroundColor = .systemPink
        button.layer.cornerRadius = 12
        button.isHidden = true
        button.addTarget(self, action: #selector(continueButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        setupGestures()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        contentView.addSubview(headerView)
        headerView.addSubview(titleLabel)
        headerView.addSubview(subtitleLabel)
        
        contentView.addSubview(imageContainerView)
        imageContainerView.addSubview(selectedImageView)
        imageContainerView.addSubview(placeholderView)
        
        placeholderView.addSubview(placeholderImageView)
        placeholderView.addSubview(placeholderLabel)
        
        contentView.addSubview(actionButtonsStackView)
        actionButtonsStackView.addArrangedSubview(cameraButton)
        actionButtonsStackView.addArrangedSubview(libraryButton)
        
        contentView.addSubview(analysisLoadingView)
        analysisLoadingView.addSubview(loadingIndicator)
        analysisLoadingView.addSubview(loadingLabel)
        
        contentView.addSubview(analysisContainerView)
        contentView.addSubview(continueButton)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        headerView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.right.equalToSuperview().inset(16)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(24)
            make.left.right.equalToSuperview().inset(20)
        }
        
        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-24)
        }
        
        imageContainerView.snp.makeConstraints { make in
            make.top.equalTo(headerView.snp.bottom).offset(24)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(300)
        }
        
        selectedImageView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        placeholderView.snp.makeConstraints { make in
            make.center.equalToSuperview()
        }
        
        placeholderImageView.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.width.height.equalTo(60)
        }
        
        placeholderLabel.snp.makeConstraints { make in
            make.top.equalTo(placeholderImageView.snp.bottom).offset(12)
            make.centerX.bottom.equalToSuperview()
        }
        
        actionButtonsStackView.snp.makeConstraints { make in
            make.top.equalTo(imageContainerView.snp.bottom).offset(24)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(50)
        }
        
        analysisLoadingView.snp.makeConstraints { make in
            make.top.equalTo(actionButtonsStackView.snp.bottom).offset(24)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(100)
        }
        
        loadingIndicator.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(20)
        }
        
        loadingLabel.snp.makeConstraints { make in
            make.top.equalTo(loadingIndicator.snp.bottom).offset(12)
            make.left.right.equalToSuperview()
        }
        
        analysisContainerView.snp.makeConstraints { make in
            make.top.equalTo(actionButtonsStackView.snp.bottom).offset(24)
            make.left.right.equalToSuperview().inset(16)
            make.height.greaterThanOrEqualTo(120)
        }
        
        continueButton.snp.makeConstraints { make in
            make.top.equalTo(analysisContainerView.snp.bottom).offset(24)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(50)
            make.bottom.equalToSuperview().offset(-24)
        }
    }
    
    private func setupNavigationBar() {
        title = "Post"
        navigationController?.navigationBar.prefersLargeTitles = true
        navigationController?.navigationBar.tintColor = .systemPink
    }
    
    private func setupGestures() {
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(imageContainerTapped))
        imageContainerView.addGestureRecognizer(tapGesture)
        imageContainerView.isUserInteractionEnabled = true
    }
    
    // MARK: - Actions
    @objc private func imageContainerTapped() {
        checkLoginBeforeImageSelection()
    }

    @objc private func cameraButtonTapped() {
        checkLoginBeforeImageSelection(sourceType: .camera)
    }

    @objc private func libraryButtonTapped() {
        checkLoginBeforeImageSelection(sourceType: .photoLibrary)
    }
    
    @objc private func continueButtonTapped() {
        guard let image = selectedImage, let result = analysisResult else { return }

        let postComposerVC = PostComposerViewController(image: image, analysisResult: result)
        let navController = UINavigationController(rootViewController: postComposerVC)
        navController.modalPresentationStyle = .fullScreen
        present(navController, animated: true)
    }

    // MARK: - Helper Methods
    private func checkLoginBeforeImageSelection(sourceType: UIImagePickerController.SourceType? = nil) {
        // Check if user is logged in before allowing image selection
        if !UserAuthenticationManager.shared.isUserLoggedIn() {
            showLoginRequiredForImageSelection(sourceType: sourceType)
            return
        }

        // User is logged in, proceed with image selection
        if let sourceType = sourceType {
            presentImagePicker(sourceType: sourceType)
        } else {
            showImageSelectionOptions()
        }
    }

    private func showLoginRequiredForImageSelection(sourceType: UIImagePickerController.SourceType? = nil) {
        let alert = UIAlertController(
            title: "Login Required",
            message: "Please log in to capture and publish photos with AI analysis.",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        alert.addAction(UIAlertAction(title: "Guest Login", style: .default) { _ in
            AppViewModel.shared.login()
            // After login, proceed with image selection
            DispatchQueue.main.async {
                if let sourceType = sourceType {
                    self.presentImagePicker(sourceType: sourceType)
                } else {
                    self.showImageSelectionOptions()
                }
            }
        })

        alert.addAction(UIAlertAction(title: "Apple Login", style: .default) { _ in
            self.presentAppleLoginForImageSelection(sourceType: sourceType)
        })

        present(alert, animated: true)
    }

    private func presentAppleLoginForImageSelection(sourceType: UIImagePickerController.SourceType? = nil) {
        // Store the source type for after login
        self.pendingSourceType = sourceType

        let request = ASAuthorizationAppleIDProvider().createRequest()
        request.requestedScopes = [.fullName, .email]

        let authorizationController = ASAuthorizationController(authorizationRequests: [request])
        authorizationController.delegate = self
        authorizationController.presentationContextProvider = self
        authorizationController.performRequests()
    }

    private func showImageSelectionOptions() {
        let alert = UIAlertController(title: "Select Photo", message: "Choose how you'd like to add a photo", preferredStyle: .actionSheet)

        if UIImagePickerController.isSourceTypeAvailable(.camera) {
            alert.addAction(UIAlertAction(title: "Take Photo", style: .default) { _ in
                self.presentImagePicker(sourceType: .camera)
            })
        }

        alert.addAction(UIAlertAction(title: "Choose from Library", style: .default) { _ in
            self.presentImagePicker(sourceType: .photoLibrary)
        })

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        if let popover = alert.popoverPresentationController {
            popover.sourceView = imageContainerView
            popover.sourceRect = imageContainerView.bounds
        }

        present(alert, animated: true)
    }

    private func presentImagePicker(sourceType: UIImagePickerController.SourceType) {
        guard UIImagePickerController.isSourceTypeAvailable(sourceType) else {
            showAlert(title: "Not Available", message: "This feature is not available on this device.")
            return
        }

        let picker = UIImagePickerController()
        picker.sourceType = sourceType
        picker.delegate = self
        picker.allowsEditing = true

        present(picker, animated: true)
    }

    private func processSelectedImage(_ image: UIImage) {
        selectedImage = image
        selectedImageView.image = image
        selectedImageView.isHidden = false
        placeholderView.isHidden = true

        // Update container appearance
        imageContainerView.layer.borderColor = UIColor.systemPink.cgColor

        // User is already logged in (checked before image selection), proceed with coin balance check
        checkCoinBalanceAndProcess(image)
    }



    private func checkCoinBalanceAndProcess(_ image: UIImage) {
        let user = AppViewModel.shared.currentUser
        let totalAvailable = user.totalAvailableProcessing

        print("🔍 [CameraVC] Checking AI processing availability:")
        print("   - Free processing: \(user.freeProcessingCount)")
        print("   - Purchased processing: \(user.purchasedProcessingCount)")
        print("   - Total available: \(totalAvailable)")

        if user.freeProcessingCount > 0 {
            // Has free processing available
            let message: String
            if user.purchasedProcessingCount > 0 {
                message = "Free processing available (\(user.freeProcessingCount) free + \(user.purchasedProcessingCount) purchased = \(totalAvailable) total)"
            } else {
                message = "Free processing available (\(user.freeProcessingCount) remaining)"
            }
            showAIProcessingConfirmation(message: message) { [weak self] in
                self?.proceedWithAIAnalysis(image)
            }
        } else if user.purchasedProcessingCount > 0 {
            // Only purchased processing available
            let message = "Purchased AI processing available (\(user.purchasedProcessingCount) analyses remaining)"
            showAIProcessingConfirmation(message: message) { [weak self] in
                self?.proceedWithAIAnalysis(image)
            }
        } else {
            // No processing available
            let message = user.isGuestUser ?
                "No free processing left. Sign in to purchase more AI analyses" :
                "No AI analyses available. Purchase more to continue using AI features"
            showInsufficientAIAlert(message: message)
        }
    }

    private func showAIProcessingConfirmation(message: String, completion: @escaping () -> Void) {
        let alert = UIAlertController(
            title: "AI Analysis Ready ✨",
            message: "\(message)\n\nProceed with AI analysis?",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel) { _ in
            // Use basic analysis when user cancels AI processing
            self.proceedWithBasicAnalysis()
        })

        alert.addAction(UIAlertAction(title: "Analyze", style: .default) { _ in
            completion()
        })

        present(alert, animated: true)
    }

    private func showInsufficientAIAlert(message: String) {
        let alert = UIAlertController(
            title: "No AI Analyses Available",
            message: message,
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel) { _ in
            self.resetImageSelection()
        })

        alert.addAction(UIAlertAction(title: "Get More AI", style: .default) { _ in
            self.presentPurchaseViewController()
        })

        present(alert, animated: true)
    }

    private func proceedWithAIAnalysis(_ image: UIImage) {
        // Show loading state
        showAnalysisLoading()

        // Start AI analysis
        Task {
            await analyzeImage(image)
        }
    }

    private func proceedWithBasicAnalysis() {
        guard let selectedImage = selectedImage else { return }

        // Show loading state
        showAnalysisLoading()

        // Start basic analysis (no AI, no cost)
        Task {
            await performBasicAnalysis(selectedImage)
        }
    }

    private func resetImageSelection() {
        selectedImage = nil
        selectedImageView.image = nil
        selectedImageView.isHidden = true
        placeholderView.isHidden = false
        imageContainerView.layer.borderColor = UIColor.systemGray5.cgColor
        hideAnalysisLoading()
        analysisContainerView.isHidden = true
        continueButton.isHidden = true
    }

    private func presentPurchaseViewController() {
        let purchaseVC = PurchaseViewController()
        let navController = UINavigationController(rootViewController: purchaseVC)
        present(navController, animated: true)
    }

    private func showAnalysisLoading() {
        analysisLoadingView.isHidden = false
        analysisContainerView.isHidden = true
        continueButton.isHidden = true
        loadingIndicator.startAnimating()
    }

    private func hideAnalysisLoading() {
        analysisLoadingView.isHidden = true
        loadingIndicator.stopAnimating()
    }

    private func analyzeImage(_ image: UIImage) async {
        let userId = AppViewModel.shared.currentUser.id
        var user = AppViewModel.shared.currentUser

        print("🚀 [CameraVC] Starting AI analysis for user: \(userId)")
        print("📊 [CameraVC] User status before analysis:")
        print("   - Free processing: \(user.freeProcessingCount)")
        print("   - Purchased processing: \(user.purchasedProcessingCount)")
        print("   - Total available: \(user.totalAvailableProcessing)")

        // Check if user can afford processing
        guard user.canAffordProcessing else {
            DispatchQueue.main.async {
                self.hideAnalysisLoading()
                self.showAnalysisError("No AI analyses available. Purchase more to continue using AI features.")
            }
            return
        }

        // Deduct AI processing count
        let description: String
        if user.freeProcessingCount > 0 {
            user.freeProcessingCount -= 1
            description = "Free AI Image Processing (Remaining: \(user.freeProcessingCount))"
            print("✅ [CameraVC] Used free processing, remaining: \(user.freeProcessingCount)")
        } else if user.purchasedProcessingCount > 0 {
            user.purchasedProcessingCount -= 1
            description = "AI Image Processing (Purchased count used)"
            print("✅ [CameraVC] Used purchased processing, remaining: \(user.purchasedProcessingCount)")
        } else {
            DispatchQueue.main.async {
                self.hideAnalysisLoading()
                self.showAnalysisError("No AI analyses available.")
            }
            return
        }

        // Update user in AppViewModel
        AppViewModel.shared.currentUser = user
        user.totalProcessingCount += 1

        // Persist the updated user data
        UserAuthenticationManager.shared.updateUserData(user)

        // Create a mock transaction for display purposes
        let transaction = CoinTransaction(
            userId: userId,
            type: .aiProcessing,
            amount: 0, // No coin cost
            balanceAfter: user.coins,
            description: description,
            metadata: [
                "processing_type": "full_analysis",
                "free_processing_used": user.freeProcessingCount > 0 ? "true" : "false"
            ]
        )

        // Proceed with AI analysis (user has already paid for AI processing)
        do {
            let result = await ImageContentRecognitionService.shared.analyzeImageContent(image, useAI: true)
            self.analysisResult = result

            DispatchQueue.main.async {
                self.hideAnalysisLoading()
                self.showAnalysisResult(result, transaction: transaction)
            }
        } catch {
            DispatchQueue.main.async {
                self.hideAnalysisLoading()
                self.showAnalysisError("AI analysis failed. Please try again.")
            }
        }
    }

    private func performBasicAnalysis(_ image: UIImage) async {
        let userId = AppViewModel.shared.currentUser.id

        print("🔧 [CameraVC] Starting basic analysis for user: \(userId)")
        print("📊 [CameraVC] Using fallback analysis (no AI cost)")

        // Create a basic transaction record (no cost)
        let transaction = CoinTransaction(
            userId: userId,
            type: .aiProcessing,
            amount: 0, // No coin cost
            balanceAfter: AppViewModel.shared.currentUser.coins,
            description: "Basic Image Analysis (No AI cost)",
            metadata: [
                "processing_type": "basic_analysis",
                "ai_used": "false"
            ]
        )

        // Proceed with basic analysis (no AI, no cost)
        do {
            let result = await ImageContentRecognitionService.shared.analyzeImageContent(image, useAI: false)
            self.analysisResult = result

            DispatchQueue.main.async {
                self.hideAnalysisLoading()
                self.showAnalysisResult(result, transaction: transaction)
            }
        } catch {
            DispatchQueue.main.async {
                self.hideAnalysisLoading()
                self.showAnalysisError("Basic analysis failed. Please try again.")
            }
        }
    }

    private func showAnalysisError(_ message: String) {
        let alert = UIAlertController(title: "Analysis Failed", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default) { _ in
            self.resetImageSelection()
        })
        present(alert, animated: true)
    }

    private func showAnalysisResult(_ result: ImageRecognitionResult, transaction: CoinTransaction) {
        // Create analysis result UI
        setupAnalysisResultUI(result, transaction: transaction)

        analysisContainerView.isHidden = false
        continueButton.isHidden = false

        // Animate appearance
        analysisContainerView.alpha = 0
        continueButton.alpha = 0

        UIView.animate(withDuration: 0.3) {
            self.analysisContainerView.alpha = 1
            self.continueButton.alpha = 1
        }
    }

    private func setupAnalysisResultUI(_ result: ImageRecognitionResult, transaction: CoinTransaction) {
        // Clear existing subviews
        analysisContainerView.subviews.forEach { $0.removeFromSuperview() }

        let titleLabel = UILabel()
        titleLabel.text = "AI Analysis Complete ✨"
        titleLabel.font = .systemFont(ofSize: 18, weight: .bold)
        titleLabel.textColor = .label

        let costLabel = UILabel()
        let user = AppViewModel.shared.currentUser
        if transaction.description.contains("Free") {
            costLabel.text = "✨ Free AI Analysis Used (\(user.freeProcessingCount) free remaining)"
            costLabel.textColor = .systemGreen
        } else {
            costLabel.text = "🤖 AI Analysis Used (\(user.purchasedProcessingCount) purchased remaining)"
            costLabel.textColor = .systemBlue
        }
        costLabel.font = .systemFont(ofSize: 14, weight: .medium)

        let balanceLabel = UILabel()
        balanceLabel.text = "Balance: \(AppViewModel.shared.currentUser.coins) coins"
        balanceLabel.font = .systemFont(ofSize: 12)
        balanceLabel.textColor = .secondaryLabel

        let contentTypeLabel = UILabel()
        contentTypeLabel.text = result.displayText
        contentTypeLabel.font = .systemFont(ofSize: 16, weight: .semibold)
        contentTypeLabel.textColor = .systemPink

        let descriptionLabel = UILabel()
        descriptionLabel.text = result.description
        descriptionLabel.font = .systemFont(ofSize: 14)
        descriptionLabel.textColor = .secondaryLabel
        descriptionLabel.numberOfLines = 0

        let confidenceLabel = UILabel()
        confidenceLabel.text = "Confidence: \(String(format: "%.0f", result.confidence * 100))%"
        confidenceLabel.font = .systemFont(ofSize: 12, weight: .medium)
        confidenceLabel.textColor = .systemBlue

        analysisContainerView.addSubview(titleLabel)
        analysisContainerView.addSubview(costLabel)
        analysisContainerView.addSubview(balanceLabel)
        analysisContainerView.addSubview(contentTypeLabel)
        analysisContainerView.addSubview(descriptionLabel)
        analysisContainerView.addSubview(confidenceLabel)

        titleLabel.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview().inset(16)
        }

        costLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(16)
        }

        balanceLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(8)
            make.right.equalToSuperview().offset(-16)
        }

        contentTypeLabel.snp.makeConstraints { make in
            make.top.equalTo(costLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
        }

        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(contentTypeLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(16)
        }

        confidenceLabel.snp.makeConstraints { make in
            make.top.equalTo(descriptionLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }

    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }
}

// MARK: - UIImagePickerControllerDelegate
extension CameraViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true)

        if let editedImage = info[.editedImage] as? UIImage {
            processSelectedImage(editedImage)
        } else if let originalImage = info[.originalImage] as? UIImage {
            processSelectedImage(originalImage)
        }
    }

    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true)
    }
}

// MARK: - Apple Sign In Support
extension CameraViewController: ASAuthorizationControllerDelegate, ASAuthorizationControllerPresentationContextProviding {
    func authorizationController(controller: ASAuthorizationController, didCompleteWithAuthorization authorization: ASAuthorization) {
        if let appleIDCredential = authorization.credential as? ASAuthorizationAppleIDCredential {
            AppViewModel.shared.loginWithApple(credential: appleIDCredential)

            // After successful Apple login, proceed with pending action
            DispatchQueue.main.async {
                if let sourceType = self.pendingSourceType {
                    self.presentImagePicker(sourceType: sourceType)
                    self.pendingSourceType = nil
                } else if let image = self.selectedImage {
                    // If there was already a selected image, process it
                    self.checkCoinBalanceAndProcess(image)
                } else {
                    // Show image selection options
                    self.showImageSelectionOptions()
                }
            }
        }
    }

    func authorizationController(controller: ASAuthorizationController, didCompleteWithError error: Error) {
        print("Apple Sign In failed: \(error.localizedDescription)")
        resetImageSelection()
    }

    func presentationAnchor(for controller: ASAuthorizationController) -> ASPresentationAnchor {
        return view.window!
    }
}
