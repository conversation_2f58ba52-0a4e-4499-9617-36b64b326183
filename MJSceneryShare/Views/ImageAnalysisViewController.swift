//
//  ImageAnalysisViewController.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-04.
//

import UIKit
import SnapKit
import Photos

class ImageAnalysisViewController: UIViewController {
    
    // MARK: - Properties
    private var selectedImage: UIImage?
    private var analysisResult: ImageRecognitionResult?
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.alwaysBounceVertical = true
        return scrollView
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var imageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFit
        imageView.backgroundColor = .systemGray6
        imageView.layer.cornerRadius = 12
        imageView.clipsToBounds = true
        imageView.isUserInteractionEnabled = true
        
        let tapGesture = UITapGestureRecognizer(target: self, action: #selector(selectImageTapped))
        imageView.addGestureRecognizer(tapGesture)
        
        return imageView
    }()
    
    private lazy var selectImageButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Select Image to Analyze", for: .normal)
        button.setTitleColor(.systemPink, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .medium)
        button.backgroundColor = .systemBackground
        button.layer.cornerRadius = 8
        button.layer.borderWidth = 2
        button.layer.borderColor = UIColor.systemPink.cgColor
        button.addTarget(self, action: #selector(selectImageTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var analysisResultView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 12
        view.layer.borderWidth = 1
        view.layer.borderColor = UIColor.systemGray4.cgColor
        view.isHidden = true
        return view
    }()
    
    private lazy var contentTypeLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 18, weight: .bold)
        label.textColor = .label
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var descriptionLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 16)
        label.textColor = .secondaryLabel
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var confidenceLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .systemBlue
        return label
    }()
    
    private lazy var tagsLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .label
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var colorsLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .label
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var moodLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14)
        label.textColor = .label
        return label
    }()
    
    private lazy var loadingIndicator: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .large)
        indicator.color = .systemPink
        indicator.hidesWhenStopped = true
        return indicator
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
    }
    
    // MARK: - Setup
    private func setupUI() {
        title = "Image Analysis Demo"
        view.backgroundColor = .systemGroupedBackground
        
        navigationItem.rightBarButtonItem = UIBarButtonItem(
            title: "Clear",
            style: .plain,
            target: self,
            action: #selector(clearResults)
        )
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        contentView.addSubview(imageView)
        contentView.addSubview(selectImageButton)
        contentView.addSubview(loadingIndicator)
        contentView.addSubview(analysisResultView)
        
        analysisResultView.addSubview(contentTypeLabel)
        analysisResultView.addSubview(descriptionLabel)
        analysisResultView.addSubview(confidenceLabel)
        analysisResultView.addSubview(tagsLabel)
        analysisResultView.addSubview(colorsLabel)
        analysisResultView.addSubview(moodLabel)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        imageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(250)
        }
        
        selectImageButton.snp.makeConstraints { make in
            make.center.equalTo(imageView)
            make.width.equalTo(200)
            make.height.equalTo(44)
        }
        
        loadingIndicator.snp.makeConstraints { make in
            make.center.equalTo(imageView)
        }
        
        analysisResultView.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-20)
        }
        
        contentTypeLabel.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview().inset(16)
        }
        
        descriptionLabel.snp.makeConstraints { make in
            make.top.equalTo(contentTypeLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
        }
        
        confidenceLabel.snp.makeConstraints { make in
            make.top.equalTo(descriptionLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
        }
        
        tagsLabel.snp.makeConstraints { make in
            make.top.equalTo(confidenceLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
        }
        
        colorsLabel.snp.makeConstraints { make in
            make.top.equalTo(tagsLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
        }
        
        moodLabel.snp.makeConstraints { make in
            make.top.equalTo(colorsLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
    }
    
    // MARK: - Actions
    @objc private func selectImageTapped() {
        let alert = UIAlertController(title: "Select Image", message: nil, preferredStyle: .actionSheet)
        
        alert.addAction(UIAlertAction(title: "Camera", style: .default) { _ in
            self.presentImagePicker(sourceType: .camera)
        })
        
        alert.addAction(UIAlertAction(title: "Photo Library", style: .default) { _ in
            self.presentImagePicker(sourceType: .photoLibrary)
        })
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        if let popover = alert.popoverPresentationController {
            popover.sourceView = selectImageButton
            popover.sourceRect = selectImageButton.bounds
        }
        
        present(alert, animated: true)
    }
    
    @objc private func clearResults() {
        selectedImage = nil
        analysisResult = nil
        imageView.image = nil
        selectImageButton.isHidden = false
        analysisResultView.isHidden = true
        loadingIndicator.stopAnimating()
    }
    
    // MARK: - Helper Methods
    private func presentImagePicker(sourceType: UIImagePickerController.SourceType) {
        guard UIImagePickerController.isSourceTypeAvailable(sourceType) else { return }
        
        let picker = UIImagePickerController()
        picker.sourceType = sourceType
        picker.delegate = self
        picker.allowsEditing = true
        
        present(picker, animated: true)
    }
    
    private func analyzeImage(_ image: UIImage) {
        selectedImage = image
        imageView.image = image
        selectImageButton.isHidden = true
        analysisResultView.isHidden = true
        loadingIndicator.startAnimating()
        
        Task {
            let result = await ImageContentRecognitionService.shared.analyzeImageContent(image)
            self.analysisResult = result
            
            DispatchQueue.main.async {
                self.displayAnalysisResult(result)
            }
        }
    }
    
    private func displayAnalysisResult(_ result: ImageRecognitionResult) {
        loadingIndicator.stopAnimating()
        analysisResultView.isHidden = false
        
        contentTypeLabel.text = result.displayText
        descriptionLabel.text = result.description
        confidenceLabel.text = "Confidence: \(String(format: "%.1f", result.confidence * 100))%"
        tagsLabel.text = "Tags: \(result.tags.joined(separator: ", "))"
        colorsLabel.text = "Colors: \(result.colors.joined(separator: ", "))"
        moodLabel.text = "Mood: \(result.mood ?? "Unknown")"
        
        // Add animation
        analysisResultView.alpha = 0
        UIView.animate(withDuration: 0.3) {
            self.analysisResultView.alpha = 1
        }
    }
}

// MARK: - UIImagePickerControllerDelegate
extension ImageAnalysisViewController: UIImagePickerControllerDelegate, UINavigationControllerDelegate {
    func imagePickerController(_ picker: UIImagePickerController, didFinishPickingMediaWithInfo info: [UIImagePickerController.InfoKey : Any]) {
        picker.dismiss(animated: true)
        
        if let editedImage = info[.editedImage] as? UIImage {
            analyzeImage(editedImage)
        } else if let originalImage = info[.originalImage] as? UIImage {
            analyzeImage(originalImage)
        }
    }
    
    func imagePickerControllerDidCancel(_ picker: UIImagePickerController) {
        picker.dismiss(animated: true)
    }
}
