//
//  CoinAnalyticsViewController.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-04.
//

import UIKit
import SnapKit

class CoinAnalyticsViewController: UIViewController {
    
    // MARK: - Properties
    private var statistics: CoinUsageStatistics?
    private var recentTransactions: [CoinTransaction] = []
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.alwaysBounceVertical = true
        scrollView.backgroundColor = .systemGroupedBackground
        return scrollView
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var balanceCardView: UIView = {
        let view = UIView()
        view.layer.cornerRadius = 16
        view.clipsToBounds = true
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.2
        view.layer.shadowOffset = CGSize(width: 0, height: 4)
        view.layer.shadowRadius = 8
        return view
    }()

    private var gradientLayer: CAGradientLayer?
    
    private lazy var balanceLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 32, weight: .bold)
        label.textColor = .white
        label.textAlignment = .center
        return label
    }()
    
    private lazy var balanceSubtitleLabel: UILabel = {
        let label = UILabel()
        label.text = "Current Balance"
        label.font = .systemFont(ofSize: 16)
        label.textColor = .white.withAlphaComponent(0.8)
        label.textAlignment = .center
        return label
    }()
    
    private lazy var freeProcessingLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .white
        label.textAlignment = .center
        label.backgroundColor = .white.withAlphaComponent(0.2)
        label.layer.cornerRadius = 12
        label.clipsToBounds = true
        label.numberOfLines = 0 // Allow multiple lines
        label.lineBreakMode = .byWordWrapping
        return label
    }()
    
    private lazy var statsContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 8
        return view
    }()
    
    private lazy var statsStackView: UIStackView = {
        let stackView = UIStackView()
        stackView.axis = .vertical
        stackView.spacing = 16
        return stackView
    }()
    
    private lazy var recentTransactionsView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 8
        return view
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        loadData()
    }

    override func viewDidLayoutSubviews() {
        super.viewDidLayoutSubviews()

        // Update gradient layer frame
        DispatchQueue.main.async {
            self.gradientLayer?.frame = self.balanceCardView.bounds
        }
    }

    override func viewDidAppear(_ animated: Bool) {
        super.viewDidAppear(animated)

        // Ensure gradient is properly sized
        gradientLayer?.frame = balanceCardView.bounds
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadData()
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        contentView.addSubview(balanceCardView)

        // Create and add gradient layer to balance card view
        setupGradientBackground()

        balanceCardView.addSubview(balanceLabel)
        balanceCardView.addSubview(balanceSubtitleLabel)
        balanceCardView.addSubview(freeProcessingLabel)
        
        contentView.addSubview(statsContainerView)
        statsContainerView.addSubview(statsStackView)
        
        contentView.addSubview(recentTransactionsView)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        balanceCardView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(140)
        }
        
        balanceLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(24)
        }
        
        balanceSubtitleLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(balanceLabel.snp.bottom).offset(4)
        }
        
        freeProcessingLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(balanceSubtitleLabel.snp.bottom).offset(12)
            make.height.greaterThanOrEqualTo(24) // Allow flexible height for multiple lines
            make.width.greaterThanOrEqualTo(120)
            make.left.right.equalToSuperview().inset(20) // Add horizontal constraints
        }
        
        statsContainerView.snp.makeConstraints { make in
            make.top.equalTo(balanceCardView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(16)
        }
        
        statsStackView.snp.makeConstraints { make in
            make.edges.equalToSuperview().inset(16)
        }
        
        recentTransactionsView.snp.makeConstraints { make in
            make.top.equalTo(statsContainerView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(200)
            make.bottom.equalToSuperview().offset(-20)
        }
    }

    private func setupGradientBackground() {
        let gradient = CAGradientLayer()
        gradient.colors = [
            UIColor.systemPink.cgColor,
            UIColor.systemPurple.cgColor
        ]
        gradient.startPoint = CGPoint(x: 0, y: 0)
        gradient.endPoint = CGPoint(x: 1, y: 1)
        gradient.frame = balanceCardView.bounds
        gradient.cornerRadius = 16

        balanceCardView.layer.insertSublayer(gradient, at: 0)
        gradientLayer = gradient
    }

    private func setupNavigationBar() {
        title = "Coin Analytics"
        navigationController?.navigationBar.tintColor = .systemPink
        
        let historyButton = UIBarButtonItem(
            title: "History",
            style: .plain,
            target: self,
            action: #selector(showFullHistory)
        )
        navigationItem.rightBarButtonItem = historyButton
    }
    
    // MARK: - Data Loading
    private func loadData() {
        let userId = AppViewModel.shared.currentUser.id
        statistics = CoinTransactionManager.shared.getStatistics(for: userId)
        recentTransactions = Array(CoinTransactionManager.shared.getTransactionHistory(for: userId).prefix(3))
        
        updateUI()
    }
    
    private func updateUI() {
        updateBalanceCard()
        updateStatistics()
        updateRecentTransactions()
    }
    
    private func updateBalanceCard() {
        let user = AppViewModel.shared.currentUser
        balanceLabel.text = "\(user.coins) ✨"
        
        let totalProcessing = user.totalAvailableProcessing
        if totalProcessing > 0 {
            if user.isGuestUser {
                freeProcessingLabel.text = "  \(user.freeProcessingCount) free (Guest)  "
            } else if user.freeProcessingCount > 0 && user.purchasedProcessingCount > 0 {
                freeProcessingLabel.text = "  \(totalProcessing) AI analyses\n(\(user.freeProcessingCount) free + \(user.purchasedProcessingCount) paid)  "
            } else if user.freeProcessingCount > 0 {
                freeProcessingLabel.text = "  \(user.freeProcessingCount) free analyses  "
            } else {
                freeProcessingLabel.text = "  \(totalProcessing) AI analyses  "
            }
            freeProcessingLabel.isHidden = false
        } else {
            freeProcessingLabel.text = "  No AI analyses  "
            freeProcessingLabel.isHidden = false
        }
    }
    
    private func updateStatistics() {
        guard let stats = statistics else { return }
        
        // Clear existing stats
        statsStackView.arrangedSubviews.forEach { $0.removeFromSuperview() }
        
        // Add title
        let titleLabel = UILabel()
        titleLabel.text = "Usage Statistics"
        titleLabel.font = .systemFont(ofSize: 18, weight: .bold)
        titleLabel.textColor = .label
        statsStackView.addArrangedSubview(titleLabel)
        
        // Add stats rows
        let statsData = [
            ("Total Spent", "\(stats.totalSpent) coins", UIColor.systemRed),
            ("Total Earned", "\(stats.totalEarned) coins", UIColor.systemGreen),
            ("AI Processing Count", "\(stats.aiProcessingCount) times", UIColor.systemBlue),
            ("Processing Frequency", stats.processingFrequency, UIColor.systemPurple),
            ("Net Balance", "\(stats.netBalance) coins", stats.netBalance >= 0 ? UIColor.systemGreen : UIColor.systemRed)
        ]
        
        for (title, value, color) in statsData {
            let statRow = createStatRow(title: title, value: value, color: color)
            statsStackView.addArrangedSubview(statRow)
        }
    }
    
    private func createStatRow(title: String, value: String, color: UIColor) -> UIView {
        let containerView = UIView()
        
        let titleLabel = UILabel()
        titleLabel.text = title
        titleLabel.font = .systemFont(ofSize: 14)
        titleLabel.textColor = .secondaryLabel
        
        let valueLabel = UILabel()
        valueLabel.text = value
        valueLabel.font = .systemFont(ofSize: 16, weight: .semibold)
        valueLabel.textColor = color
        valueLabel.textAlignment = .right
        
        containerView.addSubview(titleLabel)
        containerView.addSubview(valueLabel)
        
        titleLabel.snp.makeConstraints { make in
            make.left.top.bottom.equalToSuperview()
            make.right.lessThanOrEqualTo(valueLabel.snp.left).offset(-8)
        }
        
        valueLabel.snp.makeConstraints { make in
            make.right.top.bottom.equalToSuperview()
            make.width.greaterThanOrEqualTo(100)
        }
        
        containerView.snp.makeConstraints { make in
            make.height.equalTo(24)
        }
        
        return containerView
    }
    
    private func updateRecentTransactions() {
        // Clear existing transaction views
        recentTransactionsView.subviews.forEach { $0.removeFromSuperview() }
        
        let titleLabel = UILabel()
        titleLabel.text = "Recent Transactions"
        titleLabel.font = .systemFont(ofSize: 18, weight: .bold)
        titleLabel.textColor = .label
        
        recentTransactionsView.addSubview(titleLabel)
        titleLabel.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview().inset(16)
        }
        
        if recentTransactions.isEmpty {
            let emptyLabel = UILabel()
            emptyLabel.text = "No transactions yet"
            emptyLabel.font = .systemFont(ofSize: 14)
            emptyLabel.textColor = .secondaryLabel
            emptyLabel.textAlignment = .center
            
            recentTransactionsView.addSubview(emptyLabel)
            emptyLabel.snp.makeConstraints { make in
                make.center.equalToSuperview()
            }
        } else {
            var previousView: UIView = titleLabel
            
            for transaction in recentTransactions {
                let transactionView = createTransactionRow(transaction: transaction)
                recentTransactionsView.addSubview(transactionView)
                
                transactionView.snp.makeConstraints { make in
                    make.top.equalTo(previousView.snp.bottom).offset(12)
                    make.left.right.equalToSuperview().inset(16)
                    make.height.equalTo(40)
                }
                
                previousView = transactionView
            }
        }
    }
    
    private func createTransactionRow(transaction: CoinTransaction) -> UIView {
        let containerView = UIView()
        
        let iconLabel = UILabel()
        iconLabel.font = .systemFont(ofSize: 16)
        iconLabel.textAlignment = .center
        
        let titleLabel = UILabel()
        titleLabel.text = transaction.type.displayName
        titleLabel.font = .systemFont(ofSize: 14, weight: .medium)
        titleLabel.textColor = .label
        
        let timeLabel = UILabel()
        timeLabel.text = transaction.formattedTimestamp
        timeLabel.font = .systemFont(ofSize: 12)
        timeLabel.textColor = .secondaryLabel
        
        let amountLabel = UILabel()
        amountLabel.text = transaction.formattedAmount
        amountLabel.font = .systemFont(ofSize: 14, weight: .semibold)
        amountLabel.textAlignment = .right
        
        // Set icon and colors
        switch transaction.type {
        case .purchase:
            iconLabel.text = "💰"
            amountLabel.textColor = .systemGreen
        case .aiProcessing:
            iconLabel.text = "🤖"
            amountLabel.textColor = .systemRed
        case .dailyBonus:
            iconLabel.text = "🎁"
            amountLabel.textColor = .systemGreen
        case .referralBonus:
            iconLabel.text = "👥"
            amountLabel.textColor = .systemGreen
        case .refund:
            iconLabel.text = "↩️"
            amountLabel.textColor = .systemGreen
        case .contentPublishing:     // Added to make switch exhaustive
            iconLabel.text = "📤"
            amountLabel.textColor = .systemRed
        }
        
        containerView.addSubview(iconLabel)
        containerView.addSubview(titleLabel)
        containerView.addSubview(timeLabel)
        containerView.addSubview(amountLabel)
        
        iconLabel.snp.makeConstraints { make in
            make.left.centerY.equalToSuperview()
            make.width.equalTo(24)
        }
        
        titleLabel.snp.makeConstraints { make in
            make.left.equalTo(iconLabel.snp.right).offset(8)
            make.top.equalToSuperview().offset(4)
        }
        
        timeLabel.snp.makeConstraints { make in
            make.left.equalTo(titleLabel)
            make.top.equalTo(titleLabel.snp.bottom).offset(2)
        }
        
        amountLabel.snp.makeConstraints { make in
            make.right.centerY.equalToSuperview()
            make.width.greaterThanOrEqualTo(60)
        }
        
        return containerView
    }
    
    // MARK: - Actions
    @objc private func showFullHistory() {
        let historyVC = CoinTransactionHistoryViewController()
        navigationController?.pushViewController(historyVC, animated: true)
    }
}
