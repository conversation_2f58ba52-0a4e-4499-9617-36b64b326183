import UIKit
import SnapKit

class AddFriendsViewController: UIViewController {
    
    // MARK: - Properties
    private var searchResults: [Friend] = []
    private var suggestedFriends: [Friend] = []
    private var isSearching = false
    
    // MARK: - UI Components
    private lazy var searchController: UISearchController = {
        let searchController = UISearchController(searchResultsController: nil)
        searchController.searchResultsUpdater = self
        searchController.obscuresBackgroundDuringPresentation = false
        searchController.searchBar.placeholder = "Search by username or email..."
        searchController.searchBar.tintColor = .systemPink
        return searchController
    }()
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = .systemGroupedBackground
        tableView.register(AddFriendCell.self, forCellReuseIdentifier: AddFriendCell.identifier)
        tableView.register(QRCodeCell.self, forCellReuseIdentifier: QRCodeCell.identifier)
        tableView.separatorStyle = .none
        return tableView
    }()
    
    private lazy var emptyStateView: UIView = {
        let view = UIView()
        view.isHidden = true
        return view
    }()
    
    private lazy var emptyStateImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "person.2.circle")
        imageView.tintColor = .systemGray3
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private lazy var emptyStateLabel: UILabel = {
        let label = UILabel()
        label.text = "No users found"
        label.font = .systemFont(ofSize: 18, weight: .semibold)
        label.textColor = .secondaryLabel
        label.textAlignment = .center
        return label
    }()
    
    private lazy var emptyStateSubtitleLabel: UILabel = {
        let label = UILabel()
        label.text = "Try searching with a different username or email"
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .tertiaryLabel
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        loadSuggestedFriends()
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        
        view.addSubview(tableView)
        view.addSubview(emptyStateView)
        
        emptyStateView.addSubview(emptyStateImageView)
        emptyStateView.addSubview(emptyStateLabel)
        emptyStateView.addSubview(emptyStateSubtitleLabel)
    }
    
    private func setupConstraints() {
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
        
        emptyStateView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(40)
        }
        
        emptyStateImageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
            make.width.height.equalTo(80)
        }
        
        emptyStateLabel.snp.makeConstraints { make in
            make.top.equalTo(emptyStateImageView.snp.bottom).offset(16)
            make.left.right.equalToSuperview()
        }
        
        emptyStateSubtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(emptyStateLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
    }
    
    private func setupNavigationBar() {
        title = "Add Friends"
        navigationController?.navigationBar.prefersLargeTitles = true
        navigationController?.navigationBar.tintColor = .systemPink
        
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelTapped)
        )
        
        navigationItem.searchController = searchController
        navigationItem.hidesSearchBarWhenScrolling = false
        
        definesPresentationContext = true
    }
    
    private func loadSuggestedFriends() {
        // Load real suggested friends (users not already friends)
        let allUsers = Friend.realFriends
        let currentFriends = Friend.realFriends // In real app, get actual friends

        suggestedFriends = allUsers.filter { user in
            !currentFriends.contains { $0.id == user.id }
        }

        DispatchQueue.main.async {
            self.tableView.reloadData()
        }
    }
    
    private func searchUsers(with query: String) {
        // Simulate user search
        isSearching = true
        
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) {
            // Mock search results
            self.searchResults = self.suggestedFriends.filter { user in
                user.displayName.lowercased().contains(query.lowercased()) ||
                user.username.lowercased().contains(query.lowercased())
            }
            
            self.updateEmptyState()
            self.tableView.reloadData()
        }
    }
    
    private func updateEmptyState() {
        let shouldShowEmpty = isSearching && searchResults.isEmpty
        emptyStateView.isHidden = !shouldShowEmpty
        tableView.isHidden = shouldShowEmpty
    }
    
    // MARK: - Actions
    @objc private func cancelTapped() {
        dismiss(animated: true)
    }
    
    private func sendFriendRequest(to user: Friend) {
        let alert = UIAlertController(
            title: "Send Friend Request",
            message: "Send a friend request to \(user.displayName)?",
            preferredStyle: .alert
        )
        
        alert.addTextField { textField in
            textField.placeholder = "Add a message (optional)"
            textField.autocapitalizationType = .sentences
        }
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Send", style: .default) { _ in
            let message = alert.textFields?.first?.text
            self.performSendFriendRequest(to: user, message: message)
        })
        
        present(alert, animated: true)
    }
    
    private func performSendFriendRequest(to user: Friend, message: String?) {
        // Simulate sending friend request
        let alert = UIAlertController(
            title: "Request Sent! ✨",
            message: "Your friend request has been sent to \(user.displayName)",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Great!", style: .default))
        present(alert, animated: true)
        
        // Remove from suggested friends
        if let index = suggestedFriends.firstIndex(where: { $0.id == user.id }) {
            suggestedFriends.remove(at: index)
        }
        
        if let index = searchResults.firstIndex(where: { $0.id == user.id }) {
            searchResults.remove(at: index)
        }
        
        tableView.reloadData()
    }
    
    private func showQRCode() {
        let qrVC = QRCodeViewController()
        let navController = UINavigationController(rootViewController: qrVC)
        present(navController, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension AddFriendsViewController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        return isSearching ? 1 : 2 // QR Code section + Suggested friends
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        if isSearching {
            return searchResults.count
        } else {
            if section == 0 {
                return 1 // QR Code cell
            } else {
                return suggestedFriends.count
            }
        }
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        if !isSearching && indexPath.section == 0 {
            // QR Code cell
            let cell = tableView.dequeueReusableCell(withIdentifier: QRCodeCell.identifier, for: indexPath) as! QRCodeCell
            cell.delegate = self
            return cell
        } else {
            // Friend cell
            let cell = tableView.dequeueReusableCell(withIdentifier: AddFriendCell.identifier, for: indexPath) as! AddFriendCell
            
            let user = isSearching ? searchResults[indexPath.row] : suggestedFriends[indexPath.row]
            cell.configure(with: user)
            cell.delegate = self
            
            return cell
        }
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        if isSearching {
            return searchResults.isEmpty ? nil : "Search Results"
        } else {
            if section == 0 {
                return "Quick Add"
            } else {
                return "Suggested Friends"
            }
        }
    }
    
    func tableView(_ tableView: UITableView, titleForFooterInSection section: Int) -> String? {
        if !isSearching && section == 1 && suggestedFriends.isEmpty {
            return "No suggested friends at the moment. Try searching for specific users!"
        }
        return nil
    }
}

// MARK: - UITableViewDelegate
extension AddFriendsViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        if !isSearching && indexPath.section == 0 {
            return 80 // QR Code cell
        } else {
            return 70 // Friend cell
        }
    }
}

// MARK: - UISearchResultsUpdating
extension AddFriendsViewController: UISearchResultsUpdating {
    func updateSearchResults(for searchController: UISearchController) {
        guard let searchText = searchController.searchBar.text else { return }
        
        if searchText.isEmpty {
            isSearching = false
            searchResults.removeAll()
        } else {
            searchUsers(with: searchText)
        }
        
        updateEmptyState()
        tableView.reloadData()
    }
}

// MARK: - AddFriendCellDelegate
extension AddFriendsViewController: AddFriendCellDelegate {
    func addFriendCell(_ cell: AddFriendCell, didTapAddFriend user: Friend) {
        sendFriendRequest(to: user)
    }
}

// MARK: - Delegate Protocols
protocol AddFriendCellDelegate: AnyObject {
    func addFriendCell(_ cell: AddFriendCell, didTapAddFriend user: Friend)
}

protocol QRCodeCellDelegate: AnyObject {
    func qrCodeCellDidTapShowQR(_ cell: QRCodeCell)
}

// MARK: - QRCodeCellDelegate
extension AddFriendsViewController: QRCodeCellDelegate {
    func qrCodeCellDidTapShowQR(_ cell: QRCodeCell) {
        showQRCode()
    }
}
