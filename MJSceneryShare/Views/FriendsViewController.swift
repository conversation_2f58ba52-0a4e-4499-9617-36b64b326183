import UIKit
import SnapKit

class FriendsViewController: UIViewController {
    
    // MARK: - Properties
    private var friends: [Friend] = []
    private var filteredFriends: [Friend] = []
    private var friendRequests: [FriendRequest] = []
    private var isSearching = false
    
    // MARK: - UI Components
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = .systemGroupedBackground
        tableView.register(FriendTableViewCell.self, forCellReuseIdentifier: FriendTableViewCell.identifier)
        tableView.register(FriendRequestTableViewCell.self, forCellReuseIdentifier: FriendRequestTableViewCell.identifier)
        tableView.separatorStyle = .none
        return tableView
    }()
    
    private lazy var searchController: UISearchController = {
        let searchController = UISearchController(searchResultsController: nil)
        searchController.searchResultsUpdater = self
        searchController.obscuresBackgroundDuringPresentation = false
        searchController.searchBar.placeholder = "Search friends..."
        searchController.searchBar.tintColor = .systemPink
        return searchController
    }()

    private lazy var discoverButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Explore Discover", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.backgroundColor = .systemPink
        button.layer.cornerRadius = 20
        button.titleLabel?.font = .systemFont(ofSize: 16, weight: .semibold)
        button.isUserInteractionEnabled = true // Ensure button can be tapped

        // Add debug border to verify button visibility and bounds
        button.layer.borderWidth = 2
        button.layer.borderColor = UIColor.red.cgColor
        button.addTarget(self, action: #selector(exploreDiscoverTapped), for: .touchUpInside)
        print("🔘 Discover button created")
        return button
    }()

    private lazy var emptyStateView: UIView = {
        let view = UIView()
        view.backgroundColor = .clear
        view.isUserInteractionEnabled = true // Enable user interaction for the container

        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "person.2.slash")
        imageView.tintColor = .systemGray3
        imageView.contentMode = .scaleAspectFit

        let titleLabel = UILabel()
        titleLabel.text = "No Friends Yet"
        titleLabel.font = .systemFont(ofSize: 24, weight: .bold)
        titleLabel.textColor = .secondaryLabel
        titleLabel.textAlignment = .center

        let subtitleLabel = UILabel()
        subtitleLabel.text = "Start chatting with people from Discover to add them as friends"
        subtitleLabel.font = .systemFont(ofSize: 16)
        subtitleLabel.textColor = .tertiaryLabel
        subtitleLabel.textAlignment = .center
        subtitleLabel.numberOfLines = 0

        view.addSubview(imageView)
        view.addSubview(titleLabel)
        view.addSubview(subtitleLabel)
        view.addSubview(self.discoverButton)

        imageView.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.centerY.equalToSuperview().offset(-80) // Center vertically with upward offset
            make.width.height.equalTo(100)
        }

        titleLabel.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(24)
            make.left.right.equalToSuperview().inset(40)
        }

        subtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(titleLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(40)
        }

        self.discoverButton.snp.makeConstraints { make in
            make.top.equalTo(subtitleLabel.snp.bottom).offset(32)
            make.centerX.equalToSuperview()
            make.width.equalTo(160)
            make.height.equalTo(40)
        }

        return view
    }()
    
    private lazy var addFriendButton: UIBarButtonItem = {
        let button = UIBarButtonItem(
            image: UIImage(systemName: "person.badge.plus"),
            style: .plain,
            target: self,
            action: #selector(addFriendTapped)
        )
        button.tintColor = .systemPink
        return button
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        setupNotifications()
        loadData()

        // Debug: Test button setup after a delay
        DispatchQueue.main.asyncAfter(deadline: .now() + 1.0) {
            self.debugButtonSetup()
        }
    }

    private func debugButtonSetup() {
        print("🧪 Debug button setup:")
        print("🔘 Button exists: \(discoverButton)")
        print("🔘 Button targets count: \(discoverButton.allTargets.count)")
        print("🔘 Button actions for touchUpInside: \(discoverButton.actions(forTarget: self, forControlEvent: .touchUpInside) ?? [])")
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        refreshData()
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        view.addSubview(tableView)
    }
    
    private func setupConstraints() {
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    private func setupNavigationBar() {
        title = "Friends"
        navigationController?.navigationBar.prefersLargeTitles = true
        navigationController?.navigationBar.tintColor = .systemPink

        navigationItem.rightBarButtonItem = addFriendButton
        navigationItem.searchController = searchController
        navigationItem.hidesSearchBarWhenScrolling = false

        definesPresentationContext = true
    }

    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(friendActivityDidUpdate),
            name: .friendActivityDidUpdate,
            object: nil
        )

        NotificationCenter.default.addObserver(
            self,
            selector: #selector(friendsListDidUpdate),
            name: .friendsListDidUpdate,
            object: nil
        )
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Data Methods
    private func loadData() {
        // Load friends from FriendsManager (only added friends, no default friends)
        friends = FriendsManager.shared.getAllFriends()
        friendRequests = [] // Real friend requests would go here
        filteredFriends = friends

        DispatchQueue.main.async {
            self.updateUI()
        }
    }

    private func updateUI() {
        if friends.isEmpty && !isSearching {
            showEmptyState()
        } else {
            hideEmptyState()
        }
        tableView.reloadData()
    }

    private func showEmptyState() {
        // Hide tableView content and show empty state as overlay
        tableView.backgroundView = nil
        tableView.isScrollEnabled = false

        // Remove any existing constraints and superview
        emptyStateView.removeFromSuperview()

        // Add empty state directly to main view to avoid tableView interference
        view.addSubview(emptyStateView)

        emptyStateView.snp.remakeConstraints { make in
            make.center.equalTo(view.safeAreaLayoutGuide)
            make.left.right.equalTo(view.safeAreaLayoutGuide).inset(20)
            make.height.greaterThanOrEqualTo(300) // Ensure minimum height for content
        }

        // Force layout update
        view.layoutIfNeeded()

        // Debug state BEFORE layout
        print("📱 Showing empty state with discover button")
        print("📦 EmptyStateView frame BEFORE: \(emptyStateView.frame)")
        print("🔘 Button frame BEFORE: \(discoverButton.frame)")

        // Bring empty state to front
        view.bringSubviewToFront(emptyStateView)

        // Force another layout update and check state AFTER
        DispatchQueue.main.async {
            self.view.layoutIfNeeded()
            print("📦 EmptyStateView frame AFTER: \(self.emptyStateView.frame)")
            print("🔘 Button frame AFTER layout: \(self.discoverButton.frame)")
            print("🔘 Button superview: \(self.discoverButton.superview?.description ?? "nil")")
            print("🔘 Button isUserInteractionEnabled: \(self.discoverButton.isUserInteractionEnabled)")
            print("� Button isHidden: \(self.discoverButton.isHidden)")
            print("🔘 Button alpha: \(self.discoverButton.alpha)")
            print("🔘 Button targets: \(self.discoverButton.allTargets)")

            // Test if button is actually clickable by checking hit test
            let buttonCenter = CGPoint(x: self.discoverButton.frame.midX, y: self.discoverButton.frame.midY)
            let hitView = self.view.hitTest(buttonCenter, with: nil)
            print("🎯 Hit test at button center: \(hitView?.description ?? "nil")")
        }
    }

    private func hideEmptyState() {
        // Remove empty state from main view and restore tableView
        emptyStateView.removeFromSuperview()
        tableView.backgroundView = nil
        tableView.isScrollEnabled = true
    }
    
    private func refreshData() {
        // Simulate data refresh
        loadData()
    }
    
    // MARK: - Helper Methods
    private func groupedFriends() -> [(String, [Friend])] {
        let friendsToShow = isSearching ? filteredFriends : friends

        // Get friends with recent message activity
        let recentActiveFriends = friendsToShow.filter { friend in
            FriendActivityManager.shared.hasRecentActivity(with: friend.userId)
        }

        // Get all other friends (excluding those in recent active)
        let recentActiveFriendIds = Set(recentActiveFriends.map { $0.userId })
        let allOtherFriends = friendsToShow.filter { friend in
            !recentActiveFriendIds.contains(friend.userId)
        }

        var sections: [(String, [Friend])] = []

        // Add Recent Active section if there are any
        if !recentActiveFriends.isEmpty {
            sections.append(("Recent Active (\(recentActiveFriends.count))", recentActiveFriends))
        }

        // Add All Friends section
        if !allOtherFriends.isEmpty {
            sections.append(("All Friends (\(allOtherFriends.count))", allOtherFriends))
        }

        return sections
    }
    
    // MARK: - Actions
    @objc private func addFriendTapped() {
        let addFriendsVC = AddFriendsViewController()
        let navController = UINavigationController(rootViewController: addFriendsVC)
        present(navController, animated: true)
    }

    @objc private func exploreDiscoverTapped() {
        print("🎯 exploreDiscoverTapped method called!")
        print("📱 Current tab index: \(tabBarController?.selectedIndex ?? -1)")

        // Switch to Discover tab
        tabBarController?.selectedIndex = 0

        print("📱 Switched to tab index: \(tabBarController?.selectedIndex ?? -1)")
        print("✅ Tab switch completed")
    }

    @objc private func friendActivityDidUpdate(_ notification: Notification) {
        // Reload the table view to reflect updated friend grouping
        DispatchQueue.main.async {
            self.tableView.reloadData()
        }
    }

    @objc private func friendsListDidUpdate(_ notification: Notification) {
        // Reload friends data when the friends list is updated
        loadData()
    }
    
    private func openChat(with friend: Friend) {
        let chatVC = ChatViewController(friend: friend)
        navigationController?.pushViewController(chatVC, animated: true)
    }
    
    private func sharePhoto(to friend: Friend) {
        let photoSharingVC = PhotoSharingViewController(friend: friend)
        let navController = UINavigationController(rootViewController: photoSharingVC)
        present(navController, animated: true)
    }
    
    private func viewProfile(of friend: Friend) {
        let profileVC = FriendProfileViewController(friend: friend)
        navigationController?.pushViewController(profileVC, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension FriendsViewController: UITableViewDataSource {
    func numberOfSections(in tableView: UITableView) -> Int {
        var sections = 0
        
        // Friend requests section
        if !friendRequests.isEmpty {
            sections += 1
        }
        
        // Friends sections
        sections += groupedFriends().count
        
        return sections
    }
    
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        var currentSection = section
        
        // Friend requests section
        if !friendRequests.isEmpty {
            if currentSection == 0 {
                return 1 // Single row for friend requests
            }
            currentSection -= 1
        }
        
        // Friends sections
        let groupedData = groupedFriends()
        if currentSection < groupedData.count {
            return groupedData[currentSection].1.count
        }
        
        return 0
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        var currentSection = indexPath.section
        
        // Friend requests section
        if !friendRequests.isEmpty {
            if currentSection == 0 {
                let cell = tableView.dequeueReusableCell(withIdentifier: FriendRequestTableViewCell.identifier, for: indexPath) as! FriendRequestTableViewCell
                cell.configure(with: friendRequests.count)
                cell.delegate = self
                return cell
            }
            currentSection -= 1
        }
        
        // Friends sections
        let groupedData = groupedFriends()
        if currentSection < groupedData.count {
            let friend = groupedData[currentSection].1[indexPath.row]
            let cell = tableView.dequeueReusableCell(withIdentifier: FriendTableViewCell.identifier, for: indexPath) as! FriendTableViewCell
            cell.configure(with: friend)
            cell.delegate = self
            return cell
        }
        
        return UITableViewCell()
    }
    
    func tableView(_ tableView: UITableView, titleForHeaderInSection section: Int) -> String? {
        var currentSection = section
        
        // Friend requests section
        if !friendRequests.isEmpty {
            if currentSection == 0 {
                return nil // No header for friend requests
            }
            currentSection -= 1
        }
        
        // Friends sections
        let groupedData = groupedFriends()
        if currentSection < groupedData.count {
            return groupedData[currentSection].0
        }
        
        return nil
    }
}

// MARK: - UITableViewDelegate
extension FriendsViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        var currentSection = indexPath.section
        
        // Friend requests section
        if !friendRequests.isEmpty {
            if currentSection == 0 {
                let friendRequestsVC = FriendRequestsViewController()
                navigationController?.pushViewController(friendRequestsVC, animated: true)
                return
            }
            currentSection -= 1
        }
        
        // Friends sections
        let groupedData = groupedFriends()
        if currentSection < groupedData.count {
            let friend = groupedData[currentSection].1[indexPath.row]
            openChat(with: friend)
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 70
    }
}

// MARK: - UISearchResultsUpdating
extension FriendsViewController: UISearchResultsUpdating {
    func updateSearchResults(for searchController: UISearchController) {
        guard let searchText = searchController.searchBar.text else { return }

        isSearching = !searchText.isEmpty

        if isSearching {
            filteredFriends = friends.filter { friend in
                friend.displayName.lowercased().contains(searchText.lowercased()) ||
                friend.username.lowercased().contains(searchText.lowercased())
            }
        } else {
            filteredFriends = friends
        }

        updateUI()
    }
}

// MARK: - Delegate Protocols (to be implemented)
protocol FriendTableViewCellDelegate: AnyObject {
    func friendCell(_ cell: FriendTableViewCell, didTapMessage friend: Friend)
    func friendCell(_ cell: FriendTableViewCell, didTapSharePhoto friend: Friend)
    func friendCell(_ cell: FriendTableViewCell, didTapProfile friend: Friend)
}

protocol FriendRequestTableViewCellDelegate: AnyObject {
    func friendRequestCellDidTap(_ cell: FriendRequestTableViewCell)
}

// MARK: - FriendTableViewCellDelegate
extension FriendsViewController: FriendTableViewCellDelegate {
    func friendCell(_ cell: FriendTableViewCell, didTapMessage friend: Friend) {
        openChat(with: friend)
    }

    func friendCell(_ cell: FriendTableViewCell, didTapSharePhoto friend: Friend) {
        sharePhoto(to: friend)
    }

    func friendCell(_ cell: FriendTableViewCell, didTapProfile friend: Friend) {
        viewProfile(of: friend)
    }
}

// MARK: - FriendRequestTableViewCellDelegate
extension FriendsViewController: FriendRequestTableViewCellDelegate {
    func friendRequestCellDidTap(_ cell: FriendRequestTableViewCell) {
        let friendRequestsVC = FriendRequestsViewController()
        navigationController?.pushViewController(friendRequestsVC, animated: true)
    }
}
