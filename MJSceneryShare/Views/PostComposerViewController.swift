//
//  PostComposerViewController.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-04.
//

import UIKit
import SnapKit

class PostComposerViewController: UIViewController {
    
    // MARK: - Properties
    private let selectedImage: UIImage
    private let analysisResult: ImageRecognitionResult
    private var suggestedTags: [ImageTag] = []
    private var selectedTags: [ImageTag] = []
    
    // MARK: - UI Components
    private lazy var scrollView: UIScrollView = {
        let scrollView = UIScrollView()
        scrollView.alwaysBounceVertical = true
        scrollView.backgroundColor = .systemGroupedBackground
        return scrollView
    }()
    
    private lazy var contentView: UIView = {
        let view = UIView()
        return view
    }()
    
    private lazy var imageView: UIImageView = {
        let imageView = UIImageView()
        imageView.contentMode = .scaleAspectFill
        imageView.clipsToBounds = true
        imageView.layer.cornerRadius = 16
        imageView.image = selectedImage
        return imageView
    }()
    
    private lazy var captionContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 8
        return view
    }()
    
    private lazy var captionLabel: UILabel = {
        let label = UILabel()
        label.text = "Caption"
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .label
        return label
    }()
    
    private lazy var captionTextView: UITextView = {
        let textView = UITextView()
        textView.font = .systemFont(ofSize: 16)
        textView.textColor = .label
        textView.backgroundColor = .clear
        textView.layer.cornerRadius = 8
        textView.layer.borderWidth = 1
        textView.layer.borderColor = UIColor.systemGray4.cgColor
        textView.delegate = self
        return textView
    }()
    
    private lazy var suggestedCaptionButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Use AI Suggested Caption", for: .normal)
        button.setTitleColor(.systemPink, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 14, weight: .medium)
        button.addTarget(self, action: #selector(useSuggestedCaptionTapped), for: .touchUpInside)
        return button
    }()
    
    private lazy var tagsContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 8
        return view
    }()
    
    private lazy var tagsLabel: UILabel = {
        let label = UILabel()
        label.text = "Suggested Tags"
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .label
        return label
    }()
    
    private lazy var tagsCollectionView: UICollectionView = {
        let layout = UICollectionViewFlowLayout()
        layout.scrollDirection = .vertical
        layout.minimumInteritemSpacing = 8
        layout.minimumLineSpacing = 8
        layout.estimatedItemSize = UICollectionViewFlowLayout.automaticSize
        
        let collectionView = UICollectionView(frame: .zero, collectionViewLayout: layout)
        collectionView.backgroundColor = .clear
        collectionView.register(TagCell.self, forCellWithReuseIdentifier: "TagCell")
        collectionView.dataSource = self
        collectionView.delegate = self
        collectionView.allowsMultipleSelection = true
        return collectionView
    }()
    
    private lazy var analysisContainerView: UIView = {
        let view = UIView()
        view.backgroundColor = .systemBackground
        view.layer.cornerRadius = 16
        view.layer.shadowColor = UIColor.black.cgColor
        view.layer.shadowOpacity = 0.1
        view.layer.shadowOffset = CGSize(width: 0, height: 2)
        view.layer.shadowRadius = 8
        return view
    }()
    
    private lazy var analysisLabel: UILabel = {
        let label = UILabel()
        label.text = "AI Analysis"
        label.font = .systemFont(ofSize: 16, weight: .semibold)
        label.textColor = .label
        return label
    }()
    
    private lazy var contentTypeLabel: UILabel = {
        let label = UILabel()
        label.text = analysisResult.displayText
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .systemPink
        return label
    }()
    
    private lazy var confidenceLabel: UILabel = {
        let label = UILabel()
        label.text = "Confidence: \(String(format: "%.0f", analysisResult.confidence * 100))%"
        label.font = .systemFont(ofSize: 12)
        label.textColor = .systemBlue
        return label
    }()
    
    private lazy var publishButton: UIButton = {
        let button = UIButton(type: .system)
        button.setTitle("Publish Post", for: .normal)
        button.setTitleColor(.white, for: .normal)
        button.titleLabel?.font = .systemFont(ofSize: 18, weight: .semibold)
        button.backgroundColor = .systemPink
        button.layer.cornerRadius = 12
        button.addTarget(self, action: #selector(publishButtonTapped), for: .touchUpInside)
        return button
    }()
    
    // MARK: - Initialization
    init(image: UIImage, analysisResult: ImageRecognitionResult) {
        self.selectedImage = image
        self.analysisResult = analysisResult
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        loadSuggestedTags()
        setupInitialCaption()
        
        // Add keyboard observers
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillShow), name: UIResponder.keyboardWillShowNotification, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(keyboardWillHide), name: UIResponder.keyboardWillHideNotification, object: nil)
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        
        view.addSubview(scrollView)
        scrollView.addSubview(contentView)
        
        contentView.addSubview(imageView)
        contentView.addSubview(captionContainerView)
        contentView.addSubview(tagsContainerView)
        contentView.addSubview(analysisContainerView)
        contentView.addSubview(publishButton)
        
        captionContainerView.addSubview(captionLabel)
        captionContainerView.addSubview(captionTextView)
        captionContainerView.addSubview(suggestedCaptionButton)
        
        tagsContainerView.addSubview(tagsLabel)
        tagsContainerView.addSubview(tagsCollectionView)
        
        analysisContainerView.addSubview(analysisLabel)
        analysisContainerView.addSubview(contentTypeLabel)
        analysisContainerView.addSubview(confidenceLabel)
    }
    
    private func setupConstraints() {
        scrollView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        contentView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
            make.width.equalToSuperview()
        }
        
        imageView.snp.makeConstraints { make in
            make.top.equalToSuperview().offset(20)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(250)
        }
        
        captionContainerView.snp.makeConstraints { make in
            make.top.equalTo(imageView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(16)
        }
        
        captionLabel.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview().inset(16)
        }
        
        captionTextView.snp.makeConstraints { make in
            make.top.equalTo(captionLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(100)
        }
        
        suggestedCaptionButton.snp.makeConstraints { make in
            make.top.equalTo(captionTextView.snp.bottom).offset(8)
            make.left.equalToSuperview().offset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        tagsContainerView.snp.makeConstraints { make in
            make.top.equalTo(captionContainerView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(16)
        }
        
        tagsLabel.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview().inset(16)
        }
        
        tagsCollectionView.snp.makeConstraints { make in
            make.top.equalTo(tagsLabel.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(120)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        analysisContainerView.snp.makeConstraints { make in
            make.top.equalTo(tagsContainerView.snp.bottom).offset(20)
            make.left.right.equalToSuperview().inset(16)
        }
        
        analysisLabel.snp.makeConstraints { make in
            make.top.left.right.equalToSuperview().inset(16)
        }
        
        contentTypeLabel.snp.makeConstraints { make in
            make.top.equalTo(analysisLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(16)
        }
        
        confidenceLabel.snp.makeConstraints { make in
            make.top.equalTo(contentTypeLabel.snp.bottom).offset(4)
            make.left.right.equalToSuperview().inset(16)
            make.bottom.equalToSuperview().offset(-16)
        }
        
        publishButton.snp.makeConstraints { make in
            make.top.equalTo(analysisContainerView.snp.bottom).offset(24)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(50)
            make.bottom.equalToSuperview().offset(-24)
        }
    }
    
    private func setupNavigationBar() {
        title = "Publish Post"
        navigationController?.navigationBar.tintColor = .systemPink

        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .cancel,
            target: self,
            action: #selector(cancelTapped)
        )
    }
    
    private func loadSuggestedTags() {
        suggestedTags = ImageTaggingService.shared.getSuggestedTags(for: analysisResult)
        tagsCollectionView.reloadData()
    }
    
    private func setupInitialCaption() {
        let user = AppViewModel.shared.currentUser

        // Check if user has AI processing available
        if user.totalAvailableProcessing > 0 {
            // User has AI processing available, generate AI caption
            Task {
                let suggestedCaption = await ImageContentRecognitionService.shared.generateCaption(for: selectedImage)
                DispatchQueue.main.async {
                    self.captionTextView.text = suggestedCaption
                }
            }
        } else {
            // User has no AI processing available, offer manual input option
            showManualCaptionInputOption()
        }
    }

    // MARK: - Actions
    @objc private func cancelTapped() {
        dismiss(animated: true)
    }

    @objc private func useSuggestedCaptionTapped() {
        let user = AppViewModel.shared.currentUser

        // Check if user has AI processing available
        if user.totalAvailableProcessing > 0 {
            // User has AI processing available, generate AI caption
            Task {
                let suggestedCaption = await ImageContentRecognitionService.shared.generateCaption(for: selectedImage)
                DispatchQueue.main.async {
                    self.captionTextView.text = suggestedCaption
                }
            }
        } else {
            // User has no AI processing available, offer manual input option
            showManualCaptionInputOption()
        }
    }

    @objc private func publishButtonTapped() {
        guard !captionTextView.text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty else {
            showAlert(title: "Missing Caption", message: "Please add a caption for your post.")
            return
        }

        // Check coin balance and process publishing fee
        let userId = AppViewModel.shared.currentUser.id
        let publishingResult = CoinTransactionManager.shared.processContentPublishing(for: userId)

        switch publishingResult {
        case .success(let transaction):
            // Publishing fee paid successfully, proceed with publishing
            publishContent(transaction: transaction)

        case .failure(let error):
            // Insufficient funds or other error
            showInsufficientFundsAlert(error: error)
        }
    }

    private func publishContent(transaction: CoinTransaction) {
        // Create post data
        let postData = PostData(
            image: selectedImage,
            caption: captionTextView.text,
            tags: selectedTags,
            analysisResult: analysisResult,
            timestamp: Date()
        )

        // Save to local storage
        PostStorageManager.shared.savePost(postData)

        // Add to Discover feed
        AppViewModel.shared.addPostToDiscoverFeed(postData)

        // Show success and dismiss
        showSuccessAlert(transaction: transaction)
    }

    @objc private func keyboardWillShow(notification: NSNotification) {
        guard let keyboardFrame = notification.userInfo?[UIResponder.keyboardFrameEndUserInfoKey] as? CGRect else { return }

        let keyboardHeight = keyboardFrame.height
        scrollView.contentInset.bottom = keyboardHeight
        scrollView.scrollIndicatorInsets.bottom = keyboardHeight
    }

    @objc private func keyboardWillHide(notification: NSNotification) {
        scrollView.contentInset.bottom = 0
        scrollView.scrollIndicatorInsets.bottom = 0
    }

    // MARK: - Helper Methods
    private func showAlert(title: String, message: String) {
        let alert = UIAlertController(title: title, message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "OK", style: .default))
        present(alert, animated: true)
    }

    private func showSuccessAlert(transaction: CoinTransaction) {
        let message = "Your post has been published to the Discover feed! \(transaction.formattedAmount) coins were deducted for publishing."
        let alert = UIAlertController(title: "Post Published! ✨", message: message, preferredStyle: .alert)
        alert.addAction(UIAlertAction(title: "View in Discover", style: .default) { _ in
            self.dismiss(animated: true) {
                // Switch to Discover tab
                if let tabBarController = self.presentingViewController as? UITabBarController {
                    tabBarController.selectedIndex = 0 // Discover tab is at index 0
                }
            }
        })
        alert.addAction(UIAlertAction(title: "OK", style: .cancel) { _ in
            self.dismiss(animated: true)
        })
        present(alert, animated: true)
    }

    private func showInsufficientFundsAlert(error: CoinSystemError) {
        let alert = UIAlertController(
            title: "Insufficient Coins",
            message: error.localizedDescription + " You need \(CoinSystemConfig.contentPublishingCost) coins to publish content.",
            preferredStyle: .alert
        )

        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))

        alert.addAction(UIAlertAction(title: "Get Coins", style: .default) { _ in
            // Navigate to purchase page
            let purchaseVC = PurchaseViewController()
            let navController = UINavigationController(rootViewController: purchaseVC)
            navController.modalPresentationStyle = .pageSheet

            if let sheet = navController.sheetPresentationController {
                sheet.detents = [.large()]
                sheet.prefersGrabberVisible = true
                sheet.preferredCornerRadius = 20
            }

            self.present(navController, animated: true)
        })

        present(alert, animated: true)
    }
}

// MARK: - UITextViewDelegate
extension PostComposerViewController: UITextViewDelegate {
    func textViewDidChange(_ textView: UITextView) {
        // Update publish button state based on content
        publishButton.isEnabled = !textView.text.trimmingCharacters(in: .whitespacesAndNewlines).isEmpty
        publishButton.alpha = publishButton.isEnabled ? 1.0 : 0.6
    }
}

// MARK: - UICollectionViewDataSource
extension PostComposerViewController: UICollectionViewDataSource {
    func collectionView(_ collectionView: UICollectionView, numberOfItemsInSection section: Int) -> Int {
        return suggestedTags.count
    }

    func collectionView(_ collectionView: UICollectionView, cellForItemAt indexPath: IndexPath) -> UICollectionViewCell {
        let cell = collectionView.dequeueReusableCell(withReuseIdentifier: "TagCell", for: indexPath) as! TagCell
        let tag = suggestedTags[indexPath.item]
        let isSelected = selectedTags.contains { $0.id == tag.id }
        cell.configure(with: tag, isSelected: isSelected)
        return cell
    }
}

// MARK: - UICollectionViewDelegate
extension PostComposerViewController: UICollectionViewDelegate {
    func collectionView(_ collectionView: UICollectionView, didSelectItemAt indexPath: IndexPath) {
        let tag = suggestedTags[indexPath.item]

        if let index = selectedTags.firstIndex(where: { $0.id == tag.id }) {
            // Remove tag if already selected
            selectedTags.remove(at: index)
        } else {
            // Add tag if not selected
            selectedTags.append(tag)
        }

        // Update cell appearance
        collectionView.reloadItems(at: [indexPath])

        // Add tag to caption
        updateCaptionWithTags()
    }

    private func updateCaptionWithTags() {
        let currentText = captionTextView.text ?? ""
        let tagStrings = selectedTags.map { "#\($0.name)" }

        // Remove existing hashtags from caption
        let lines = currentText.components(separatedBy: .newlines)
        let nonTagLines = lines.filter { !$0.hasPrefix("#") && !$0.trimmingCharacters(in: .whitespaces).isEmpty }

        // Combine non-tag content with selected tags
        var newCaption = nonTagLines.joined(separator: "\n")
        if !tagStrings.isEmpty {
            if !newCaption.isEmpty {
                newCaption += "\n\n"
            }
            newCaption += tagStrings.joined(separator: " ")
        }

        captionTextView.text = newCaption
    }
}
