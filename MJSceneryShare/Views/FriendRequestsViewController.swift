import UIKit
import SnapKit

class FriendRequestsViewController: UIViewController {
    
    // MARK: - Properties
    private var receivedRequests: [FriendRequest] = []
    private var sentRequests: [FriendRequest] = []
    
    // MARK: - UI Components
    private lazy var segmentedControl: UISegmentedControl = {
        let items = ["Received", "Sent"]
        let control = UISegmentedControl(items: items)
        control.selectedSegmentIndex = 0
        control.selectedSegmentTintColor = .systemPink
        control.setTitleTextAttributes([.foregroundColor: UIColor.white], for: .selected)
        control.addTarget(self, action: #selector(segmentChanged), for: .valueChanged)
        return control
    }()
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.backgroundColor = .systemGroupedBackground
        tableView.register(FriendRequestCell.self, forCellReuseIdentifier: FriendRequestCell.identifier)
        tableView.separatorStyle = .none
        return tableView
    }()
    
    private lazy var emptyStateView: UIView = {
        let view = UIView()
        view.isHidden = true
        return view
    }()
    
    private lazy var emptyStateImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "person.badge.plus")
        imageView.tintColor = .systemGray3
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private lazy var emptyStateLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 18, weight: .semibold)
        label.textColor = .secondaryLabel
        label.textAlignment = .center
        return label
    }()
    
    private lazy var emptyStateSubtitleLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .tertiaryLabel
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        loadFriendRequests()
    }
    
    // MARK: - Setup Methods
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        
        view.addSubview(segmentedControl)
        view.addSubview(tableView)
        view.addSubview(emptyStateView)
        
        emptyStateView.addSubview(emptyStateImageView)
        emptyStateView.addSubview(emptyStateLabel)
        emptyStateView.addSubview(emptyStateSubtitleLabel)
    }
    
    private func setupConstraints() {
        segmentedControl.snp.makeConstraints { make in
            make.top.equalTo(view.safeAreaLayoutGuide).offset(16)
            make.left.right.equalToSuperview().inset(16)
            make.height.equalTo(32)
        }
        
        tableView.snp.makeConstraints { make in
            make.top.equalTo(segmentedControl.snp.bottom).offset(16)
            make.left.right.bottom.equalToSuperview()
        }
        
        emptyStateView.snp.makeConstraints { make in
            make.center.equalTo(tableView)
            make.left.right.equalToSuperview().inset(40)
        }
        
        emptyStateImageView.snp.makeConstraints { make in
            make.top.equalToSuperview()
            make.centerX.equalToSuperview()
            make.width.height.equalTo(80)
        }
        
        emptyStateLabel.snp.makeConstraints { make in
            make.top.equalTo(emptyStateImageView.snp.bottom).offset(16)
            make.left.right.equalToSuperview()
        }
        
        emptyStateSubtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(emptyStateLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview()
            make.bottom.equalToSuperview()
        }
    }
    
    private func setupNavigationBar() {
        title = "Friend Requests"
        navigationController?.navigationBar.tintColor = .systemPink
    }
    
    private func loadFriendRequests() {
        // Load real friend requests
        receivedRequests = FriendRequest.realReceivedRequests()
        sentRequests = FriendRequest.realSentRequests()

        updateEmptyState()
        tableView.reloadData()
    }
    
    private func updateEmptyState() {
        let currentRequests = segmentedControl.selectedSegmentIndex == 0 ? receivedRequests : sentRequests
        let isEmpty = currentRequests.isEmpty
        
        emptyStateView.isHidden = !isEmpty
        tableView.isHidden = isEmpty
        
        if isEmpty {
            if segmentedControl.selectedSegmentIndex == 0 {
                emptyStateLabel.text = "No Friend Requests"
                emptyStateSubtitleLabel.text = "When someone sends you a friend request, it will appear here ✨"
            } else {
                emptyStateLabel.text = "No Sent Requests"
                emptyStateSubtitleLabel.text = "Friend requests you send will appear here"
            }
        }
    }
    
    // MARK: - Actions
    @objc private func segmentChanged() {
        updateEmptyState()
        tableView.reloadData()
    }
    
    private func acceptFriendRequest(_ request: FriendRequest) {
        let alert = UIAlertController(
            title: "Accept Friend Request",
            message: "Accept friend request from \(request.senderInfo.displayName)?",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Accept", style: .default) { _ in
            self.performAcceptRequest(request)
        })
        
        present(alert, animated: true)
    }
    
    private func declineFriendRequest(_ request: FriendRequest) {
        let alert = UIAlertController(
            title: "Decline Friend Request",
            message: "Decline friend request from \(request.senderInfo.displayName)?",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        alert.addAction(UIAlertAction(title: "Decline", style: .destructive) { _ in
            self.performDeclineRequest(request)
        })
        
        present(alert, animated: true)
    }
    
    private func performAcceptRequest(_ request: FriendRequest) {
        // Remove from received requests
        if let index = receivedRequests.firstIndex(where: { $0.id == request.id }) {
            receivedRequests.remove(at: index)
        }
        
        // Show success message
        let alert = UIAlertController(
            title: "Friend Added! 🎉",
            message: "You and \(request.senderInfo.displayName) are now friends",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "Great!", style: .default))
        present(alert, animated: true)
        
        updateEmptyState()
        tableView.reloadData()
    }
    
    private func performDeclineRequest(_ request: FriendRequest) {
        // Remove from received requests
        if let index = receivedRequests.firstIndex(where: { $0.id == request.id }) {
            receivedRequests.remove(at: index)
        }
        
        updateEmptyState()
        tableView.reloadData()
    }
    
    private func cancelSentRequest(_ request: FriendRequest) {
        let alert = UIAlertController(
            title: "Cancel Request",
            message: "Cancel friend request to \(request.senderInfo.displayName)?",
            preferredStyle: .alert
        )
        
        alert.addAction(UIAlertAction(title: "No", style: .cancel))
        alert.addAction(UIAlertAction(title: "Cancel Request", style: .destructive) { _ in
            self.performCancelRequest(request)
        })
        
        present(alert, animated: true)
    }
    
    private func performCancelRequest(_ request: FriendRequest) {
        // Remove from sent requests
        if let index = sentRequests.firstIndex(where: { $0.id == request.id }) {
            sentRequests.remove(at: index)
        }
        
        updateEmptyState()
        tableView.reloadData()
    }
}

// MARK: - UITableViewDataSource
extension FriendRequestsViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return segmentedControl.selectedSegmentIndex == 0 ? receivedRequests.count : sentRequests.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: FriendRequestCell.identifier, for: indexPath) as! FriendRequestCell
        
        let request = segmentedControl.selectedSegmentIndex == 0 ? receivedRequests[indexPath.row] : sentRequests[indexPath.row]
        let isReceived = segmentedControl.selectedSegmentIndex == 0
        
        cell.configure(with: request, isReceived: isReceived)
        cell.delegate = self
        
        return cell
    }
}

// MARK: - UITableViewDelegate
extension FriendRequestsViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return segmentedControl.selectedSegmentIndex == 0 ? 100 : 80 // Received requests are taller
    }
}

// MARK: - Delegate Protocol
protocol FriendRequestCellDelegate: AnyObject {
    func friendRequestCell(_ cell: FriendRequestCell, didTapAccept request: FriendRequest)
    func friendRequestCell(_ cell: FriendRequestCell, didTapDecline request: FriendRequest)
    func friendRequestCell(_ cell: FriendRequestCell, didTapCancel request: FriendRequest)
    func friendRequestCell(_ cell: FriendRequestCell, didTapProfile request: FriendRequest)
}

// MARK: - FriendRequestCellDelegate
extension FriendRequestsViewController: FriendRequestCellDelegate {
    func friendRequestCell(_ cell: FriendRequestCell, didTapAccept request: FriendRequest) {
        acceptFriendRequest(request)
    }

    func friendRequestCell(_ cell: FriendRequestCell, didTapDecline request: FriendRequest) {
        declineFriendRequest(request)
    }

    func friendRequestCell(_ cell: FriendRequestCell, didTapCancel request: FriendRequest) {
        cancelSentRequest(request)
    }

    func friendRequestCell(_ cell: FriendRequestCell, didTapProfile request: FriendRequest) {
        let profileVC = FriendProfileViewController(friend: request.senderInfo)
        navigationController?.pushViewController(profileVC, animated: true)
    }
}

// MARK: - Real Data Extensions
extension FriendRequest {
    static func realReceivedRequests() -> [FriendRequest] {
        return RealDataManager.generateRealFriendRequests().received
    }

    static func realSentRequests() -> [FriendRequest] {
        return RealDataManager.generateRealFriendRequests().sent
    }
}
