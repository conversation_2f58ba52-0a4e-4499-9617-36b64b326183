import UIKit
import SnapKit

class LoadingTableViewCell: UITableViewCell {
    static let identifier = "LoadingTableViewCell"
    
    private lazy var activityIndicator: UIActivityIndicatorView = {
        let indicator = UIActivityIndicatorView(style: .medium)
        indicator.color = .systemPink
        indicator.hidesWhenStopped = true
        return indicator
    }()
    
    private lazy var loadingLabel: UILabel = {
        let label = UILabel()
        label.text = "Loading more content..."
        label.font = .systemFont(ofSize: 14, weight: .medium)
        label.textColor = .secondaryLabel
        label.textAlignment = .center
        return label
    }()
    
    private lazy var pageInfoLabel: UILabel = {
        let label = UILabel()
        label.font = .systemFont(ofSize: 12, weight: .regular)
        label.textColor = .tertiaryLabel
        label.textAlignment = .center
        return label
    }()
    
    override init(style: UITableViewCell.CellStyle, reuseIdentifier: String?) {
        super.init(style: style, reuseIdentifier: reuseIdentifier)
        setupUI()
        setupConstraints()
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    private func setupUI() {
        backgroundColor = .systemGroupedBackground
        selectionStyle = .none
        
        contentView.addSubview(activityIndicator)
        contentView.addSubview(loadingLabel)
        contentView.addSubview(pageInfoLabel)
    }
    
    private func setupConstraints() {
        activityIndicator.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalToSuperview().offset(20)
        }
        
        loadingLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(activityIndicator.snp.bottom).offset(12)
            make.left.right.equalToSuperview().inset(20)
        }
        
        pageInfoLabel.snp.makeConstraints { make in
            make.centerX.equalToSuperview()
            make.top.equalTo(loadingLabel.snp.bottom).offset(8)
            make.left.right.equalToSuperview().inset(20)
            make.bottom.equalToSuperview().offset(-20)
        }
    }
    
    func startLoading() {
        activityIndicator.startAnimating()
        loadingLabel.text = "Loading more content..."
        
        let currentPage = AppViewModel.shared.getCurrentPage()
        let totalPages = AppViewModel.shared.getTotalPages()
        pageInfoLabel.text = "Page \(currentPage) of \(totalPages)"
    }
    
    func stopLoading() {
        activityIndicator.stopAnimating()
        
        let currentPage = AppViewModel.shared.getCurrentPage()
        let totalPages = AppViewModel.shared.getTotalPages()
        
        if currentPage >= totalPages {
            loadingLabel.text = "You've reached the end! 🎉"
            pageInfoLabel.text = "All \(totalPages) pages loaded"
        } else {
            loadingLabel.text = "Scroll to load more"
            pageInfoLabel.text = "Page \(currentPage) of \(totalPages)"
        }
    }
}
