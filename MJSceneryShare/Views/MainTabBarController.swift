import UIKit

class MainTabBarController: UITabBarController {
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupViewControllers()
        setupAppearance()
    }
    
    private func setupViewControllers() {
        let shareVC = ShareViewController()
        let shareNav = UINavigationController(rootViewController: shareVC)
        shareNav.tabBarItem = UITabBarItem(title: "Discover", image: UIImage(systemName: "heart.circle"), tag: 0)

        let cameraVC = CameraViewController()
        let cameraNav = UINavigationController(rootViewController: cameraVC)
        cameraNav.tabBarItem = UITabBarItem(title: "Post", image: UIImage(systemName: "paperplane.circle.fill"), tag: 1)

        let friendsVC = FriendsViewController()
        let friendsNav = UINavigationController(rootViewController: friendsVC)
        friendsNav.tabBarItem = UITabBarItem(title: "Friends", image: UIImage(systemName: "person.2.circle"), tag: 2)

        let profileVC = ProfileViewController()
        let profileNav = UINavigationController(rootViewController: profileVC)
        profileNav.tabBarItem = UITabBarItem(title: "Profile", image: UIImage(systemName: "person.circle"), tag: 3)

        viewControllers = [shareNav, cameraNav, friendsNav, profileNav]
    }

    private func setupAppearance() {
        // Modern feminine color scheme
        tabBar.tintColor = .systemPink
        tabBar.unselectedItemTintColor = .systemGray3
    }
} 