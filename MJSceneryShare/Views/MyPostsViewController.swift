//
//  MyPostsViewController.swift
//  MJSceneryShare
//
//  Created by AI Assistant on 2025-07-04.
//

import UIKit
import SnapKit

class MyPostsViewController: UIViewController {
    
    // MARK: - Properties
    private var posts: [PostData] = []
    private var filteredPosts: [PostData] = []
    private var currentFilter: PostFilterOption = .all
    private var currentSort: PostSortOption = .newest
    
    // MARK: - UI Components
    private lazy var tableView: UITableView = {
        let tableView = UITableView(frame: .zero, style: .insetGrouped)
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(PostTableViewCell.self, forCellReuseIdentifier: "PostCell")
        tableView.backgroundColor = .systemGroupedBackground
        tableView.separatorStyle = .none
        return tableView
    }()
    
    private lazy var emptyStateView: UIView = {
        let view = UIView()
        view.isHidden = true
        return view
    }()
    
    private lazy var emptyImageView: UIImageView = {
        let imageView = UIImageView()
        imageView.image = UIImage(systemName: "photo.on.rectangle.angled")
        imageView.tintColor = .systemGray3
        imageView.contentMode = .scaleAspectFit
        return imageView
    }()
    
    private lazy var emptyTitleLabel: UILabel = {
        let label = UILabel()
        label.text = "No Posts Yet"
        label.font = .systemFont(ofSize: 20, weight: .semibold)
        label.textColor = .secondaryLabel
        label.textAlignment = .center
        return label
    }()
    
    private lazy var emptySubtitleLabel: UILabel = {
        let label = UILabel()
        label.text = "Publish your first post using the Post tab"
        label.font = .systemFont(ofSize: 16)
        label.textColor = .tertiaryLabel
        label.textAlignment = .center
        label.numberOfLines = 0
        return label
    }()
    
    private lazy var searchController: UISearchController = {
        let controller = UISearchController(searchResultsController: nil)
        controller.searchResultsUpdater = self
        controller.obscuresBackgroundDuringPresentation = false
        controller.searchBar.placeholder = "Search posts..."
        return controller
    }()
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        setupConstraints()
        setupNavigationBar()
        setupNotifications()
        loadPosts()
    }
    
    override func viewWillAppear(_ animated: Bool) {
        super.viewWillAppear(animated)
        loadPosts()
    }
    
    deinit {
        NotificationCenter.default.removeObserver(self)
    }
    
    // MARK: - Setup
    private func setupUI() {
        view.backgroundColor = .systemGroupedBackground
        
        view.addSubview(tableView)
        view.addSubview(emptyStateView)
        
        emptyStateView.addSubview(emptyImageView)
        emptyStateView.addSubview(emptyTitleLabel)
        emptyStateView.addSubview(emptySubtitleLabel)
    }
    
    private func setupConstraints() {
        tableView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        emptyStateView.snp.makeConstraints { make in
            make.center.equalToSuperview()
            make.left.right.equalToSuperview().inset(40)
        }
        
        emptyImageView.snp.makeConstraints { make in
            make.top.centerX.equalToSuperview()
            make.width.height.equalTo(80)
        }
        
        emptyTitleLabel.snp.makeConstraints { make in
            make.top.equalTo(emptyImageView.snp.bottom).offset(16)
            make.left.right.equalToSuperview()
        }
        
        emptySubtitleLabel.snp.makeConstraints { make in
            make.top.equalTo(emptyTitleLabel.snp.bottom).offset(8)
            make.left.right.bottom.equalToSuperview()
        }
    }
    
    private func setupNavigationBar() {
        title = "My Posts"
        navigationController?.navigationBar.prefersLargeTitles = true
        navigationController?.navigationBar.tintColor = .systemPink
        
        // Add filter and sort buttons
        let filterButton = UIBarButtonItem(
            image: UIImage(systemName: "line.3.horizontal.decrease.circle"),
            style: .plain,
            target: self,
            action: #selector(filterButtonTapped)
        )
        
        let sortButton = UIBarButtonItem(
            image: UIImage(systemName: "arrow.up.arrow.down.circle"),
            style: .plain,
            target: self,
            action: #selector(sortButtonTapped)
        )
        
        navigationItem.rightBarButtonItems = [sortButton, filterButton]
        navigationItem.searchController = searchController
        navigationItem.hidesSearchBarWhenScrolling = false
        
        definesPresentationContext = true
    }
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(
            self,
            selector: #selector(postsDidUpdate),
            name: .postsDidUpdate,
            object: nil
        )
    }
    
    // MARK: - Data Loading
    private func loadPosts() {
        posts = PostStorageManager.shared.loadAllPosts()
        applyFiltersAndSort()
        updateEmptyState()
    }
    
    private func applyFiltersAndSort() {
        filteredPosts = posts.filtered(by: currentFilter).sorted(by: currentSort)
        
        // Apply search filter if active
        if let searchText = searchController.searchBar.text, !searchText.isEmpty {
            let searchResults = filteredPosts.search(text: searchText)
            filteredPosts = searchResults.map { $0.post }
        }
        
        tableView.reloadData()
    }
    
    private func updateEmptyState() {
        let isEmpty = filteredPosts.isEmpty
        emptyStateView.isHidden = !isEmpty
        tableView.isHidden = isEmpty
        
        if isEmpty && !posts.isEmpty {
            // Has posts but filtered out
            emptyTitleLabel.text = "No Matching Posts"
            emptySubtitleLabel.text = "Try adjusting your filters or search terms"
        } else if posts.isEmpty {
            // No posts at all
            emptyTitleLabel.text = "No Posts Yet"
            emptySubtitleLabel.text = "Create your first post using the camera tab"
        }
    }
    
    // MARK: - Actions
    @objc private func postsDidUpdate() {
        DispatchQueue.main.async {
            self.loadPosts()
        }
    }
    
    @objc private func filterButtonTapped() {
        let alert = UIAlertController(title: "Filter Posts", message: "Choose a content type to filter by", preferredStyle: .actionSheet)
        
        for filter in PostFilterOption.allCases {
            let action = UIAlertAction(title: filter.displayName, style: .default) { _ in
                self.currentFilter = filter
                self.applyFiltersAndSort()
                self.updateEmptyState()
            }
            
            if filter == currentFilter {
                action.setValue(true, forKey: "checked")
            }
            
            alert.addAction(action)
        }
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        if let popover = alert.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItems?.last
        }
        
        present(alert, animated: true)
    }
    
    @objc private func sortButtonTapped() {
        let alert = UIAlertController(title: "Sort Posts", message: "Choose how to sort your posts", preferredStyle: .actionSheet)
        
        for sort in PostSortOption.allCases {
            let action = UIAlertAction(title: sort.displayName, style: .default) { _ in
                self.currentSort = sort
                self.applyFiltersAndSort()
            }
            
            if sort == currentSort {
                action.setValue(true, forKey: "checked")
            }
            
            alert.addAction(action)
        }
        
        alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
        
        if let popover = alert.popoverPresentationController {
            popover.barButtonItem = navigationItem.rightBarButtonItems?.first
        }
        
        present(alert, animated: true)
    }
}

// MARK: - UITableViewDataSource
extension MyPostsViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        return filteredPosts.count
    }
    
    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let cell = tableView.dequeueReusableCell(withIdentifier: "PostCell", for: indexPath) as! PostTableViewCell
        let post = filteredPosts[indexPath.row]
        cell.configure(with: post)
        return cell
    }
}

// MARK: - UITableViewDelegate
extension MyPostsViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)
        
        let post = filteredPosts[indexPath.row]
        let detailVC = PostDetailViewController(post: post)
        navigationController?.pushViewController(detailVC, animated: true)
    }
    
    func tableView(_ tableView: UITableView, commit editingStyle: UITableViewCell.EditingStyle, forRowAt indexPath: IndexPath) {
        if editingStyle == .delete {
            let post = filteredPosts[indexPath.row]
            
            let alert = UIAlertController(title: "Delete Post", message: "Are you sure you want to delete this post? This action cannot be undone.", preferredStyle: .alert)
            
            alert.addAction(UIAlertAction(title: "Delete", style: .destructive) { _ in
                PostStorageManager.shared.deletePost(withId: post.id)
            })
            
            alert.addAction(UIAlertAction(title: "Cancel", style: .cancel))
            
            present(alert, animated: true)
        }
    }
    
    func tableView(_ tableView: UITableView, heightForRowAt indexPath: IndexPath) -> CGFloat {
        return 120
    }
}

// MARK: - UISearchResultsUpdating
extension MyPostsViewController: UISearchResultsUpdating {
    func updateSearchResults(for searchController: UISearchController) {
        applyFiltersAndSort()
        updateEmptyState()
    }
}
