import UIKit
import SnapKit

class ShareViewController: UIViewController {
    
    private lazy var tableView: UITableView = {
        let tableView = UITableView()
        tableView.delegate = self
        tableView.dataSource = self
        tableView.register(ShareTableViewCell.self, forCellReuseIdentifier: ShareTableViewCell.identifier)
        tableView.register(LoadingTableViewCell.self, forCellReuseIdentifier: LoadingTableViewCell.identifier)
        tableView.separatorStyle = .none
        tableView.backgroundColor = .systemGroupedBackground
        tableView.showsVerticalScrollIndicator = false
        return tableView
    }()
    
    private lazy var coinsBarButton: UIBarButtonItem = {
        let button = UIBarButtonItem(title: "💰 0", style: .plain, target: self, action: #selector(coinsPurchaseButtonTapped))

        // Style the button to make it more prominent
        button.setTitleTextAttributes([
            .foregroundColor: UIColor.systemPink,
            .font: UIFont.systemFont(ofSize: 16, weight: .semibold)
        ], for: .normal)

        button.setTitleTextAttributes([
            .foregroundColor: UIColor.systemPink.withAlphaComponent(0.6),
            .font: UIFont.systemFont(ofSize: 16, weight: .semibold)
        ], for: .highlighted)

        return button
    }()
    
    override func viewDidLoad() {
        super.viewDidLoad()
        
        setupUI()
        setupConstraints()
        setupNavigationBar()
        setupNotifications()
        updateCoinsDisplay()
    }
    
    private func setupUI() {
        view.backgroundColor = .systemBackground
        view.addSubview(tableView)
    }
    
    private func setupConstraints() {
        tableView.snp.makeConstraints { make in
            make.edges.equalToSuperview()
        }
    }
    
    private func setupNavigationBar() {
        updateNavigationTitle()
        navigationController?.navigationBar.prefersLargeTitles = true
        navigationItem.rightBarButtonItem = coinsBarButton

        // Feminine navigation styling
        navigationController?.navigationBar.tintColor = .systemPink
    }
    
    private func setupNotifications() {
        NotificationCenter.default.addObserver(self, selector: #selector(handleDataUpdate), name: .sharesDidUpdate, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(handleDataUpdate), name: .postsDidUpdate, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(handleUserChange), name: .userDidChange, object: nil)
        NotificationCenter.default.addObserver(self, selector: #selector(handleCoinBalanceUpdate), name: .coinBalanceDidUpdate, object: nil)
    }
    
    @objc private func handleDataUpdate() {
        DispatchQueue.main.async { [weak self] in
            self?.tableView.reloadData()
            self?.updateNavigationTitle()
        }
    }

    private func updateNavigationTitle() {
        // Keep navigation title simple
        title = "Discover"

        // Update tab bar title with page information
        updateTabBarTitle()
    }

    private func updateTabBarTitle() {
        let currentPage = AppViewModel.shared.getCurrentPage()
        let totalPages = AppViewModel.shared.getTotalPages()

        if currentPage == 1 {
            tabBarItem.title = "Discover"
        } else {
            tabBarItem.title = "Discover (\(currentPage)/\(totalPages))"
        }
    }
    
    @objc private func handleUserChange() {
        updateCoinsDisplay()
    }

    @objc private func handleCoinBalanceUpdate() {
        updateCoinsDisplay()
    }

    private func updateCoinsDisplay() {
        let user = AppViewModel.shared.currentUser
        let coinText = "💰 \(user.coins)"

        // 显示总AI次数，包括免费和购买的
        let totalAI = user.totalAvailableProcessing
        let aiText = totalAI > 0 ? " (🤖\(totalAI))" : ""

        coinsBarButton.title = coinText + aiText
    }

    @objc private func coinsPurchaseButtonTapped() {
        let purchaseVC = PurchaseViewController()
        let navController = UINavigationController(rootViewController: purchaseVC)
        navController.modalPresentationStyle = .pageSheet

        // Configure the sheet presentation
        if let sheet = navController.sheetPresentationController {
            sheet.detents = [.large()]
            sheet.prefersGrabberVisible = true
            sheet.preferredCornerRadius = 20
        }

        present(navController, animated: true)
    }

    deinit {
        NotificationCenter.default.removeObserver(self)
    }
}

// MARK: - UITableViewDataSource
extension ShareViewController: UITableViewDataSource {
    func tableView(_ tableView: UITableView, numberOfRowsInSection section: Int) -> Int {
        let shareCount = AppViewModel.shared.visibleShares.count
        // Add 1 for loading cell if there are more pages
        return AppViewModel.shared.hasMorePages() ? shareCount + 1 : shareCount
    }

    func tableView(_ tableView: UITableView, cellForRowAt indexPath: IndexPath) -> UITableViewCell {
        let shareCount = AppViewModel.shared.visibleShares.count

        // Check if this is the loading cell
        if indexPath.row == shareCount && AppViewModel.shared.hasMorePages() {
            let cell = tableView.dequeueReusableCell(withIdentifier: LoadingTableViewCell.identifier, for: indexPath) as! LoadingTableViewCell

            if AppViewModel.shared.getIsLoading() {
                cell.startLoading()
            } else {
                cell.stopLoading()
            }

            return cell
        }

        // Regular share cell
        let cell = tableView.dequeueReusableCell(withIdentifier: ShareTableViewCell.identifier, for: indexPath) as! ShareTableViewCell
        let share = AppViewModel.shared.visibleShares[indexPath.row]
        cell.configure(with: share)
        cell.delegate = self
        return cell
    }
}

// MARK: - UITableViewDelegate
extension ShareViewController: UITableViewDelegate {
    func tableView(_ tableView: UITableView, didSelectRowAt indexPath: IndexPath) {
        tableView.deselectRow(at: indexPath, animated: true)

        // Don't allow selection of loading cell
        let shareCount = AppViewModel.shared.visibleShares.count
        if indexPath.row >= shareCount {
            return
        }

        let share = AppViewModel.shared.visibleShares[indexPath.row]
        let detailVC = ShareDetailViewController(share: share)
        navigationController?.pushViewController(detailVC, animated: true)
    }

    func tableView(_ tableView: UITableView, willDisplay cell: UITableViewCell, forRowAt indexPath: IndexPath) {
        let shareCount = AppViewModel.shared.visibleShares.count

        // Check if we're displaying the loading cell and trigger pagination
        if indexPath.row == shareCount && AppViewModel.shared.hasMorePages() && !AppViewModel.shared.getIsLoading() {
            AppViewModel.shared.loadNextPage()
        }
    }
}

// MARK: - ShareTableViewCellDelegate
extension ShareViewController: ShareTableViewCellDelegate {
    func shareCell(_ cell: ShareTableViewCell, didTapLike share: Share) {
        AppViewModel.shared.toggleLike(for: share.id)
    }
    
    func shareCell(_ cell: ShareTableViewCell, didTapComment share: Share) {
        // Check if this is the current user's own post
        let currentUserId = AppViewModel.shared.currentUser.id
        if share.author.id == currentUserId {
            // Show alert that user cannot chat with themselves
            let alert = UIAlertController(
                title: "Cannot Chat",
                message: "You cannot start a chat with yourself. This is your own post!",
                preferredStyle: .alert
            )
            alert.addAction(UIAlertAction(title: "OK", style: .default))
            present(alert, animated: true)
            return
        }

        // Increment chat count when user enters chat
        AppViewModel.shared.incrementChatCount(for: share.id)

        // Create a Friend from the share author and navigate to chat
        let friend = Friend.fromShareAuthor(share.author)
        let chatVC = ChatViewController(friend: friend)
        navigationController?.pushViewController(chatVC, animated: true)

        print("💬 [ShareViewController] Opening chat with \(friend.displayName) (ID: \(share.author.id))")
    }
    
    func shareCell(_ cell: ShareTableViewCell, didTapShare share: Share) {
        let activityVC = UIActivityViewController(activityItems: [share.title, share.description], applicationActivities: nil)
        present(activityVC, animated: true)
    }

    func shareCell(_ cell: ShareTableViewCell, didTapFavorite share: Share) {
        AppViewModel.shared.toggleFavorite(for: share.id)
    }

    func shareCell(_ cell: ShareTableViewCell, didTapReport share: Share) {
        showReportConfirmation(for: share)
    }

    func shareCell(_ cell: ShareTableViewCell, didTapBlock share: Share) {
        showBlockConfirmation(for: share)
    }

    // MARK: - Report & Block Actions
    private func showReportConfirmation(for share: Share) {
        let alert = UIAlertController(
            title: "Report Content",
            message: "Are you sure you want to report this content? It will be hidden from your feed.",
            preferredStyle: .alert
        )

        let reportAction = UIAlertAction(title: "Report", style: .destructive) { [weak self] _ in
            self?.reportContent(share)
        }

        let cancelAction = UIAlertAction(title: "Cancel", style: .cancel)

        alert.addAction(reportAction)
        alert.addAction(cancelAction)

        present(alert, animated: true)
    }

    private func showBlockConfirmation(for share: Share) {
        let alert = UIAlertController(
            title: "Block User",
            message: "Are you sure you want to block @\(share.author.nickname)? You won't see their content anymore.",
            preferredStyle: .alert
        )

        let blockAction = UIAlertAction(title: "Block", style: .destructive) { [weak self] _ in
            self?.blockUser(share)
        }

        let cancelAction = UIAlertAction(title: "Cancel", style: .cancel)

        alert.addAction(blockAction)
        alert.addAction(cancelAction)

        present(alert, animated: true)
    }

    private func reportContent(_ share: Share) {
        AppViewModel.shared.reportShare(share.id)

        let successAlert = UIAlertController(
            title: "Content Reported",
            message: "Thank you for your report. This content has been hidden from your feed.",
            preferredStyle: .alert
        )
        successAlert.addAction(UIAlertAction(title: "OK", style: .default))
        present(successAlert, animated: true)
    }

    private func blockUser(_ share: Share) {
        AppViewModel.shared.blockUser(share.author.id)

        let successAlert = UIAlertController(
            title: "User Blocked",
            message: "You have blocked @\(share.author.nickname). Their content will no longer appear in your feed.",
            preferredStyle: .alert
        )
        successAlert.addAction(UIAlertAction(title: "OK", style: .default))
        present(successAlert, animated: true)
    }
}