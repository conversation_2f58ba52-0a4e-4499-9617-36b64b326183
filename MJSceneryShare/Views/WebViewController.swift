import UIKit
import WebKit
import SnapKit

class WebViewController: UIViewController {
    
    // MARK: - Properties
    private let webView = WKWebView()
    private let url: URL
    private let pageTitle: String
    
    // MARK: - Initialization
    init(url: URL, title: String) {
        self.url = url
        self.pageTitle = title
        super.init(nibName: nil, bundle: nil)
    }
    
    required init?(coder: NSCoder) {
        fatalError("init(coder:) has not been implemented")
    }
    
    // MARK: - Lifecycle
    override func viewDidLoad() {
        super.viewDidLoad()
        setupUI()
        loadContent()
    }
    
    // MARK: - Setup
    private func setupUI() {
        title = pageTitle
        view.backgroundColor = .systemBackground
        
        // Navigation bar setup
        navigationItem.leftBarButtonItem = UIBarButtonItem(
            barButtonSystemItem: .done,
            target: self,
            action: #selector(dismissViewController)
        )
        
        // Add web view
        view.addSubview(webView)
        webView.snp.makeConstraints { make in
            make.edges.equalTo(view.safeAreaLayoutGuide)
        }
        
        // Configure web view
        webView.navigationDelegate = self
        webView.backgroundColor = .systemBackground
        webView.scrollView.backgroundColor = .systemBackground
    }
    
    private func loadContent() {
        // Load the HTML file
        webView.loadFileURL(url, allowingReadAccessTo: url.deletingLastPathComponent())
    }
    
    @objc private func dismissViewController() {
        dismiss(animated: true)
    }
}

// MARK: - WKNavigationDelegate
extension WebViewController: WKNavigationDelegate {
    
    func webView(_ webView: WKWebView, didFinish navigation: WKNavigation!) {
        // Page loaded successfully
    }
    
    func webView(_ webView: WKWebView, didFail navigation: WKNavigation!, withError error: Error) {
        // Handle loading error
        showErrorAlert(error: error)
    }
    
    func webView(_ webView: WKWebView, didFailProvisionalNavigation navigation: WKNavigation!, withError error: Error) {
        // Handle provisional loading error
        showErrorAlert(error: error)
    }
    
    private func showErrorAlert(error: Error) {
        let alert = UIAlertController(
            title: "Loading Error",
            message: "Failed to load content: \(error.localizedDescription)",
            preferredStyle: .alert
        )
        alert.addAction(UIAlertAction(title: "OK", style: .default) { _ in
            self.dismiss(animated: true)
        })
        present(alert, animated: true)
    }
}
