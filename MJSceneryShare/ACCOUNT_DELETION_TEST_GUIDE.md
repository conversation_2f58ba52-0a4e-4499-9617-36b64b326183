# Account Deletion Feature Test Guide

## Overview
This guide explains how to test the account deletion feature that ensures user-generated content is properly removed from the Discover feed when a user deletes their account.

## What Gets Deleted
When a user deletes their account, the following data is cleaned up:

1. **Authentication Data**: User login state and stored user data
2. **Post Storage**: All locally stored posts and images
3. **Discover Feed Content**: User's posts are removed from both `allShares` and `userShares` arrays
4. **Chat Data**: All messages and conversations involving the deleted user
5. **Friends Data**: The user is removed from other users' friends lists and friend activity data is cleared
6. **Coin Transactions**: All transaction history, balance, and statistics for the user
7. **Guest User History**: For guest users, removes the user from login history

## Implementation Summary
The account deletion process has been enhanced to ensure complete data cleanup:

### Key Changes Made:
- **Enhanced `UserAuthenticationManager.deleteUserAccount()`**: Added comprehensive cleanup logic
- **Added `clearUserContentFromDiscoverFeed()`**: Removes user content from Discover feed arrays
- **Added `clearUserChatData()`**: Removes all chat messages and conversations involving the user
- **Enhanced `clearUserFriendsData()`**: Removes user from friends lists and clears activity data
- **Added debug logging**: Comprehensive logging for tracking the deletion process

## Test Scenarios

### Scenario 1: Guest User Account Deletion

**Setup:**
1. Launch the app as a guest user
2. Create some posts using the camera/gallery feature
3. Verify posts appear in the Discover tab
4. Note the user ID in debug logs

**Test Steps:**
1. Go to Profile tab
2. Scroll down to "Delete Account" option
3. Tap "Delete Account"
4. Follow the confirmation prompts (type "DELETE")
5. Wait for deletion process to complete

**Expected Results:**
- User is redirected to login screen
- All posts created by the user are removed from Discover feed
- Debug logs show cleanup process:
  ```
  🗑️ [UserAuth] Starting account deletion for user: [user_id]
  🗑️ [UserAuth] Cleared authentication data
  🗑️ [UserAuth] Cleared coin transaction data
  🗑️ [UserAuth] Cleared post storage data
  🗑️ [UserAuth] Cleared user content from Discover feed for user: [user_id]
  🗑️ [UserAuth] Cleared chat data for user: [user_id]
  🗑️ [UserAuth] Cleared friends data for user: [user_id]
  🗑️ [UserAuth] Account deletion completed for user: [user_id]
  📱 [AppViewModel] Loading data...
  📱 [AppViewModel] Loaded X shares from saved data
  ```

### Scenario 2: Apple ID User Account Deletion

**Setup:**
1. Login with Apple ID
2. Create some posts
3. Add some friends and send messages
4. Verify content appears in Discover tab

**Test Steps:**
1. Follow same deletion steps as Scenario 1

**Expected Results:**
- Same cleanup behavior as guest user
- Apple-specific data is also cleared
- User's content no longer appears in Discover feed

### Scenario 3: Multiple Users Content Verification

**Setup:**
1. Create content with User A
2. Switch to User B (or use different device/simulator)
3. Create content with User B
4. Verify both users' content appears in Discover feed

**Test Steps:**
1. Delete User A's account
2. Check Discover feed as User B

**Expected Results:**
- Only User A's content is removed
- User B's content remains visible
- Default content from shares.json remains visible

## Debug Information

### Key Debug Messages to Look For:

1. **Account Deletion Start:**
   ```
   🗑️ [UserAuth] Starting account deletion for user: [user_id]
   ```

2. **Discover Feed Cleanup:**
   ```
   🗑️ [UserAuth] Cleared user content from Discover feed for user: [user_id]
   ```

3. **Data Reload:**
   ```
   📱 [AppViewModel] Loading data...
   📱 [AppViewModel] Loaded X shares from saved data
   ```

### UserDefaults Keys to Monitor:
- `allShares`: Contains all shares including user interactions
- `userShares`: Contains only user-created shares
- `chatMessages`: Contains all chat messages
- `conversations`: Contains conversation metadata
- `friends`: Contains friends list data

## Manual Verification Steps

1. **Before Deletion:**
   - Count posts in Discover feed
   - Note which posts belong to the user being deleted
   - Check chat conversations
   - Verify friends list

2. **After Deletion:**
   - Verify user's posts are no longer in Discover feed
   - Check that other users' posts remain
   - Confirm chat data involving deleted user is removed
   - Verify user is removed from friends lists

## Troubleshooting

### If User Content Still Appears After Deletion:
1. Check debug logs for cleanup messages
2. Verify `clearUserContentFromDiscoverFeed` method is being called
3. Check if `AppViewModel.loadData()` is being called after cleanup
4. Manually inspect UserDefaults data

### Common Issues:
- **Async timing**: Ensure UI updates happen on main thread
- **Data persistence**: Verify UserDefaults are properly updated
- **Reference equality**: Ensure user ID matching works correctly

## Implementation Details

The account deletion process involves these key components:

1. **UserAuthenticationManager.deleteUserAccount()**: Main deletion orchestrator
2. **clearUserContentFromDiscoverFeed()**: Removes user content from Discover arrays
3. **clearUserChatData()**: Removes chat messages and conversations
4. **clearUserFriendsData()**: Removes user from friends lists
5. **AppViewModel.loadData()**: Reloads data after cleanup

The process ensures that deleted users' content is completely removed from the app while preserving other users' data and the default content.

## Complete Data Cleanup Checklist

### UserDefaults Keys Cleaned:
- ✅ `CurrentUserData` - Current user authentication data
- ✅ `UserLoginState` - User login state flag
- ✅ `GuestUserHistory` - Guest user login history (for guest users)
- ✅ `allShares` - All shares including user interactions
- ✅ `userShares` - User-created shares only
- ✅ `chatMessages` - All chat messages
- ✅ `conversations` - All conversation metadata
- ✅ `user_friends_list` - Friends list data
- ✅ `friend_activity_data` - Friend activity tracking
- ✅ `CoinTransactions` - All coin transaction records
- ✅ `CoinBalance_[userId]` - User-specific coin balance
- ✅ `CoinStatistics_[userId]` - User-specific coin usage statistics
- ✅ `SavedPosts` - All saved post data
- ✅ `PostStatistics` - Post statistics

### File System Cleanup:
- ✅ All images in `PostImages` directory
- ✅ All post data files

### Memory Cleanup:
- ✅ AppViewModel data refresh
- ✅ UI notification updates
- ✅ Current user reset to guest

## Verification Steps
After implementing this feature, you can verify it works by:

1. **Creating test content** as a user
2. **Verifying content appears** in Discover feed
3. **Deleting the account** through Profile settings
4. **Confirming content disappears** from Discover feed
5. **Checking debug logs** for cleanup confirmation

The implementation ensures that when a user deletes their account, no trace of their content remains in the Discover feed or anywhere else in the app, providing complete data privacy and cleanup.
