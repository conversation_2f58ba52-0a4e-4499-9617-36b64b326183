import Foundation
import UIKit
import AuthenticationServices

class AppViewModel {
    static let shared = AppViewModel()

    var currentUser: User = .guest {
        didSet {
            NotificationCenter.default.post(name: .userDidChange, object: nil)
        }
    }

    // All shares data
    private var allShares: [Share] = []

    // Pagination properties
    private let itemsPerPage = 10
    private let totalPages = 5
    private var currentPage = 1
    private var isLoading = false

    // Currently displayed shares (paginated)
    var shares: [Share] = [] {
        didSet {
            NotificationCenter.default.post(name: .sharesDidUpdate, object: nil)
        }
    }
    
    private init() {
        loadSavedUserState()
        loadData()
    }

    func loadData() {
        print("📱 [AppViewModel] Loading data...")

        // Configure date decoder
        let decoder = JSONDecoder()
        decoder.dateDecodingStrategy = .iso8601

        // Try to load all shares with interaction states first
        if let savedData = UserDefaults.standard.data(forKey: "allShares"),
           let savedShares = try? decoder.decode([Share].self, from: savedData) {
            allShares = savedShares
            print("📱 [AppViewModel] Loaded \(savedShares.count) shares from saved data")
            loadFirstPage()
            return
        }

        // Fallback: Load default shares from bundle
        var defaultShares: [Share] = []
        if let sharesURL = Bundle.main.url(forResource: "shares", withExtension: "json"),
           let sharesData = try? Data(contentsOf: sharesURL),
           let sharesResponse = try? decoder.decode(ShareResponse.self, from: sharesData) {
            defaultShares = sharesResponse.shares
            print("📱 [AppViewModel] Loaded \(defaultShares.count) default shares from bundle")
        }

        // Then load user-created shares from local storage
        var userShares: [Share] = []
        if let savedData = UserDefaults.standard.data(forKey: "userShares"),
           let savedShares = try? decoder.decode([Share].self, from: savedData) {
            userShares = savedShares
            print("📱 [AppViewModel] Loaded \(userShares.count) user shares from storage")
        }

        // Combine user shares (newest first) with default shares
        allShares = userShares + defaultShares
        print("📱 [AppViewModel] Total shares after combining: \(allShares.count)")

        // Load first page
        loadFirstPage()
    }

    private func loadSavedUserState() {
        // Try to load saved user state
        if let savedUser = UserAuthenticationManager.shared.loadSavedUserState() {
            var user = savedUser
            // Update coin balance from stored data
            CoinTransactionManager.shared.updateUserCoins(&user)
            user.updateLastLogin()

            // Update user data in storage
            UserAuthenticationManager.shared.updateUserData(user)

            currentUser = user
            print("🔄 [AppViewModel] Loaded saved user state: \(user.id) (\(user.loginTypeDisplayName))")
        } else {
            // No saved state, start with guest user
            currentUser = .guest
            print("🔄 [AppViewModel] No saved state, starting with default guest user")
        }
    }

    func login() {
        // Get or create device guest user (persistent for this device)
        var guestUser = UserAuthenticationManager.shared.getOrCreateDeviceGuestUser()

        // Update coin balance and last login
        CoinTransactionManager.shared.updateUserCoins(&guestUser)
        guestUser.updateLastLogin()

        // Update device guest user data
        UserAuthenticationManager.shared.updateDeviceGuestUser(guestUser)

        // Save login state
        UserAuthenticationManager.shared.saveUserLoginState(user: guestUser, loginType: .guest)

        currentUser = guestUser
        print("🔑 [AppViewModel] Guest login successful with device user: \(guestUser.id)")
    }

    func loginWithApple(credential: ASAuthorizationAppleIDCredential) {
        // Create Apple user from credentials
        var newUser = UserAuthenticationManager.shared.createAppleUser(from: credential)

        // Initialize coin balance
        CoinTransactionManager.shared.updateUserCoins(&newUser)
        newUser.updateLastLogin()

        // Save login state
        UserAuthenticationManager.shared.saveUserLoginState(user: newUser, loginType: .apple)

        currentUser = newUser
    }
    
    func logout() {
        // Clear login state but preserve user data
        UserAuthenticationManager.shared.clearLoginState()
        currentUser = .guest
    }

    func deleteAccount() {
        let userToDelete = currentUser
        let loginType = UserAuthenticationManager.shared.getCurrentLoginType() ?? .guest

        // Delete user account and all associated data
        UserAuthenticationManager.shared.deleteUserAccount(user: userToDelete, loginType: loginType)

        // Reset to guest user
        currentUser = .guest
    }
    
    func purchaseCoins(package: CoinPurchasePackage) {
        let result = CoinTransactionManager.shared.processCoinPurchase(package: package, for: currentUser.id)

        switch result {
        case .success(let transaction):
            print("Purchase successful: \(transaction.description)")
        case .failure(let error):
            print("Purchase failed: \(error.localizedDescription)")
        }
    }

    // Legacy method for backward compatibility
    func purchaseCoins(amount: Int) {
        currentUser.coins += amount
        NotificationCenter.default.post(name: .coinBalanceDidUpdate, object: nil)
    }

    // MARK: - Pagination Methods

    private func loadFirstPage() {
        currentPage = 1
        let startIndex = 0
        let endIndex = min(itemsPerPage, allShares.count)
        shares = Array(allShares[startIndex..<endIndex])
    }

    func loadNextPage() {
        guard !isLoading && hasMorePages() else { return }

        isLoading = true

        // Simulate network delay for better UX
        DispatchQueue.main.asyncAfter(deadline: .now() + 0.5) { [weak self] in
            guard let self = self else { return }

            self.currentPage += 1
            let startIndex = (self.currentPage - 1) * self.itemsPerPage
            let endIndex = min(startIndex + self.itemsPerPage, self.allShares.count)

            if startIndex < self.allShares.count {
                let newShares = Array(self.allShares[startIndex..<endIndex])
                self.shares.append(contentsOf: newShares)
            }

            self.isLoading = false
        }
    }

    func hasMorePages() -> Bool {
        return currentPage < totalPages && shares.count < allShares.count
    }

    func getCurrentPage() -> Int {
        return currentPage
    }

    func getTotalPages() -> Int {
        return totalPages
    }

    func getIsLoading() -> Bool {
        return isLoading
    }

    // Add new post to Discover feed
    func addPostToDiscoverFeed(_ postData: PostData) {
        let newShare = postData.toShare()
        // Insert at the beginning of both arrays to show newest content first
        allShares.insert(newShare, at: 0)
        shares.insert(newShare, at: 0)
        saveData()
    }

    func toggleLike(for shareId: String) {
        // Update in both arrays
        if let allIndex = allShares.firstIndex(where: { $0.id == shareId }) {
            allShares[allIndex].likes += allShares[allIndex].isLiked ? -1 : 1
            allShares[allIndex].isLiked.toggle()
        }

        if let index = shares.firstIndex(where: { $0.id == shareId }) {
            shares[index].likes += shares[index].isLiked ? -1 : 1
            shares[index].isLiked.toggle()
        }

        saveData()
    }

    func toggleFavorite(for shareId: String) {
        // Update in both arrays
        if let allIndex = allShares.firstIndex(where: { $0.id == shareId }) {
            allShares[allIndex].favorites += allShares[allIndex].isFavorited ? -1 : 1
            allShares[allIndex].isFavorited.toggle()
        }

        if let index = shares.firstIndex(where: { $0.id == shareId }) {
            shares[index].favorites += shares[index].isFavorited ? -1 : 1
            shares[index].isFavorited.toggle()
        }

        saveData()
    }

    func incrementChatCount(for shareId: String) {
        // Update in both arrays
        if let allIndex = allShares.firstIndex(where: { $0.id == shareId }) {
            allShares[allIndex].comments += 1
        }

        if let index = shares.firstIndex(where: { $0.id == shareId }) {
            shares[index].comments += 1
        }

        saveData()
    }

    func reportShare(_ shareId: String) {
        if let index = shares.firstIndex(where: { $0.id == shareId }) {
            shares[index].isReported = true
            saveData()
        }
    }

    func blockUser(_ userId: String) {
        // Mark all shares from this user as blocked
        for index in shares.indices {
            if shares[index].author.id == userId {
                shares[index].isBlocked = true
            }
        }
        saveData()
    }

    // Filter out reported and blocked content
    var visibleShares: [Share] {
        return shares.filter { !$0.isReported && !$0.isBlocked }
    }

    // Get user's liked shares
    var likedShares: [Share] {
        return allShares.filter { $0.isLiked && !$0.isReported && !$0.isBlocked }
    }

    // Get user's favorited shares
    var favoritedShares: [Share] {
        return allShares.filter { $0.isFavorited && !$0.isReported && !$0.isBlocked }
    }

    private func saveData() {
        // Save all shares to preserve interaction states
        let encoder = JSONEncoder()
        encoder.dateEncodingStrategy = .iso8601

        if let encoded = try? encoder.encode(allShares) {
            UserDefaults.standard.set(encoded, forKey: "allShares")
        }

        // Also save user-created shares separately for backward compatibility
        let userShares = allShares.filter { share in
            share.imageURL.hasPrefix("local://") || share.author.id == currentUser.id
        }

        if let encoded = try? encoder.encode(userShares) {
            UserDefaults.standard.set(encoded, forKey: "userShares")
        }
    }
}

struct ShareResponse: Codable {
    let shares: [Share]
}

// MARK: - Notification Names
extension Notification.Name {
    static let userDidChange = Notification.Name("userDidChange")
    static let sharesDidUpdate = Notification.Name("sharesDidUpdate")
}