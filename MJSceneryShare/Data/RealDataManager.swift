import Foundation

class RealDataManager {
    static let shared = RealDataManager()

    private init() {}

    // MARK: - Friends Real Data
    static func generateRealFriends() -> [Friend] {
        // Generate random avatars for each friend
        let avatarManager = AvatarManager.shared

        return [
            Friend(
                id: "friend_1",
                userId: "user_emma",
                username: "<PERSON><PERSON>",
                displayName: "<PERSON>",
                avatar: avatarManager.getRandomAvatarName(),
                bio: "Travel enthusiast & photographer 📸✈️ Living my best life one adventure at a time!",
                location: "California, USA",
                status: .online,
                lastSeen: Date(),
                mutualFriends: 12,
                friendshipDate: Date().addingTimeInterval(-86400 * 30),
                isBlocked: false
            ),
            Friend(
                id: "friend_2",
                userId: "user_sophia",
                username: "Sophia<PERSON><PERSON>V<PERSON>",
                displayName: "<PERSON>",
                avatar: avatarManager.getRandomAvatarName(),
                bio: "Beach lover & wellness coach 🌊🧘‍♀️ Spreading good vibes and positive energy ✨",
                location: "Miami, FL",
                status: .online,
                lastSeen: Date().addingTimeInterval(-300),
                mutualFriends: 8,
                friendshipDate: Date().addingTimeInterval(-86400 * 45),
                isBlocked: false
            ),
            Friend(
                id: "friend_3",
                userId: "user_chloe",
                username: "ChloeCityLife",
                displayName: "Chloe Chen",
                avatar: avatarManager.getRandomAvatarName(),
                bio: "City explorer & foodie 🏙️🍜 Always hunting for the best spots in NYC!",
                location: "New York, NY",
                status: .recentlyActive,
                lastSeen: Date().addingTimeInterval(-1800),
                mutualFriends: 15,
                friendshipDate: Date().addingTimeInterval(-86400 * 60),
                isBlocked: false
            ),
            Friend(
                id: "friend_4",
                userId: "user_lily",
                username: "LilyNatureLover",
                displayName: "Lily Thompson",
                avatar: avatarManager.getRandomAvatarName(),
                bio: "Nature photographer & hiker 🌿📷 Finding magic in every sunrise and sunset",
                location: "Portland, OR",
                status: .recentlyActive,
                lastSeen: Date().addingTimeInterval(-2700),
                mutualFriends: 6,
                friendshipDate: Date().addingTimeInterval(-86400 * 20),
                isBlocked: false
            ),
            Friend(
                id: "friend_5",
                userId: "user_grace",
                username: "GraceCozyMoments",
                displayName: "Grace Wilson",
                avatar: avatarManager.getRandomAvatarName(),
                bio: "Coffee lover & bookworm ☕📚 Creating cozy moments and sharing good reads",
                location: "Seattle, WA",
                status: .offline,
                lastSeen: Date().addingTimeInterval(-7200),
                mutualFriends: 4,
                friendshipDate: Date().addingTimeInterval(-86400 * 15),
                isBlocked: false
            ),
            Friend(
                id: "friend_6",
                userId: "user_rose",
                username: "RoseGardenDreams",
                displayName: "Rose Anderson",
                avatar: avatarManager.getRandomAvatarName(),
                bio: "Flower enthusiast & aesthetic lover 🌸🌺 Bringing beauty to everyday life",
                location: "Austin, TX",
                status: .doNotDisturb,
                lastSeen: Date().addingTimeInterval(-3600),
                mutualFriends: 9,
                friendshipDate: Date().addingTimeInterval(-86400 * 25),
                isBlocked: false
            ),
            Friend(
                id: "friend_7",
                userId: "user_mia",
                username: "MiaFashionista",
                displayName: "Mia Rodriguez",
                avatar: avatarManager.getRandomAvatarName(),
                bio: "Fashion lover & style blogger 👗✨ Sharing outfit inspiration and beauty tips",
                location: "Los Angeles, CA",
                status: .online,
                lastSeen: Date().addingTimeInterval(-600),
                mutualFriends: 18,
                friendshipDate: Date().addingTimeInterval(-86400 * 40),
                isBlocked: false
            ),
            Friend(
                id: "friend_8",
                userId: "user_zoe",
                username: "ZoeArtistic",
                displayName: "Zoe Kim",
                avatar: avatarManager.getRandomAvatarName(),
                bio: "Artist & creative soul 🎨✨ Painting my world with colors and dreams",
                location: "Chicago, IL",
                status: .recentlyActive,
                lastSeen: Date().addingTimeInterval(-4500),
                mutualFriends: 7,
                friendshipDate: Date().addingTimeInterval(-86400 * 18),
                isBlocked: false
            ),
            Friend(
                id: "friend_9",
                userId: "user_ava",
                username: "AvaFitnessQueen",
                displayName: "Ava Thompson",
                avatar: avatarManager.getRandomAvatarName(),
                bio: "Fitness coach & wellness advocate 💪🏃‍♀️ Helping women feel strong and confident",
                location: "Denver, CO",
                status: .offline,
                lastSeen: Date().addingTimeInterval(-10800),
                mutualFriends: 11,
                friendshipDate: Date().addingTimeInterval(-86400 * 35),
                isBlocked: false
            ),
            Friend(
                id: "friend_10",
                userId: "user_isabella",
                username: "IsabellaTravels",
                displayName: "Isabella Chen",
                avatar: avatarManager.getRandomAvatarName(),
                bio: "Travel blogger & culture explorer 🌍✈️ Collecting memories from around the world",
                location: "San Francisco, CA",
                status: .offline,
                lastSeen: Date().addingTimeInterval(-14400),
                mutualFriends: 13,
                friendshipDate: Date().addingTimeInterval(-86400 * 50),
                isBlocked: false
            )
        ]
    }
    
    // MARK: - Chat Messages Real Data
    static func generateRealChatMessages(for conversationId: String, with friendId: String) -> [ChatMessage] {
        let currentUserId = AppViewModel.shared.currentUser.id
        
        let messageTemplates = [
            ("Hey! How was your weekend? 😊", false, -3600),
            ("It was amazing! Went hiking and got some beautiful photos ✨", true, -3500),
            ("I'd love to see them! Can you share some? 📸", false, -3300),
            ("https://picsum.photos/400/300", true, -3200),
            ("Absolutely stunning! Where was this taken? 🏔️", false, -3000),
            ("That's in the Rocky Mountains! The sunrise was incredible 🌅", true, -2800),
            ("I'm so jealous! I need to plan a trip there soon 😍", false, -2600),
            ("You should! I can send you my itinerary if you want 💕", true, -2400),
            ("That would be amazing, thank you! 🙏", false, -2200),
            ("Just sent it to your email! Have fun planning ✨", true, -2000)
        ]
        
        return messageTemplates.enumerated().map { index, template in
            ChatMessage(
                id: "msg_\(conversationId)_\(index)",
                conversationId: conversationId,
                senderId: template.1 ? currentUserId : friendId,
                receiverId: template.1 ? friendId : currentUserId,
                content: template.0,
                type: template.0.hasPrefix("http") ? .photo : .text,
                timestamp: Date().addingTimeInterval(TimeInterval(template.2)),
                isRead: index < messageTemplates.count - 2,
                isDelivered: true,
                reactions: index == 3 ? [
                    MessageReaction(
                        id: "reaction_\(index)",
                        messageId: "msg_\(conversationId)_\(index)",
                        userId: friendId,
                        emoji: "😍",
                        timestamp: Date().addingTimeInterval(TimeInterval(template.2 + 100))
                    )
                ] : [],
                replyToMessageId: nil
            )
        }
    }
    
    // MARK: - Shared Photos Real Data
    static func generateRealSharedPhotos() -> [SharedPhoto] {
        let currentUserId = AppViewModel.shared.currentUser.id
        let friends = generateRealFriends()
        
        return [
            SharedPhoto(
                id: "shared_1",
                senderId: friends[0].userId,
                receiverId: currentUserId,
                imageURL: "https://picsum.photos/400/600?random=1",
                thumbnailURL: "https://picsum.photos/200/300?random=1",
                caption: "Check out this amazing sunset from my hike today! 🌅 The colors were absolutely breathtaking!",
                timestamp: Date().addingTimeInterval(-3600),
                expiresAt: Date().addingTimeInterval(82800),
                isViewed: false,
                reactions: [
                    PhotoReaction(
                        id: "photo_reaction_1",
                        photoId: "shared_1",
                        userId: currentUserId,
                        type: .love,
                        timestamp: Date().addingTimeInterval(-3500)
                    )
                ],
                originalPostId: "1"
            ),
            SharedPhoto(
                id: "shared_2",
                senderId: currentUserId,
                receiverId: friends[1].userId,
                imageURL: "https://picsum.photos/400/601?random=2",
                thumbnailURL: "https://picsum.photos/200/301?random=2",
                caption: "My morning coffee setup ☕✨ Perfect way to start the day!",
                timestamp: Date().addingTimeInterval(-7200),
                expiresAt: nil,
                isViewed: true,
                reactions: [
                    PhotoReaction(
                        id: "photo_reaction_2",
                        photoId: "shared_2",
                        userId: friends[1].userId,
                        type: .heart,
                        timestamp: Date().addingTimeInterval(-7000)
                    ),
                    PhotoReaction(
                        id: "photo_reaction_3",
                        photoId: "shared_2",
                        userId: friends[1].userId,
                        type: .sparkles,
                        timestamp: Date().addingTimeInterval(-6900)
                    )
                ],
                originalPostId: nil
            ),
            SharedPhoto(
                id: "shared_3",
                senderId: friends[2].userId,
                receiverId: currentUserId,
                imageURL: "https://picsum.photos/400/602?random=3",
                thumbnailURL: "https://picsum.photos/200/302?random=3",
                caption: "New York vibes! 🏙️ This city never fails to inspire me",
                timestamp: Date().addingTimeInterval(-10800),
                expiresAt: Date().addingTimeInterval(79200),
                isViewed: true,
                reactions: [],
                originalPostId: "3"
            ),
            SharedPhoto(
                id: "shared_4",
                senderId: currentUserId,
                receiverId: friends[3].userId,
                imageURL: "https://picsum.photos/400/603?random=4",
                thumbnailURL: "https://picsum.photos/200/303?random=4",
                caption: "Found this beautiful flower garden 🌸 Nature's artwork at its finest!",
                timestamp: Date().addingTimeInterval(-14400),
                expiresAt: Date().addingTimeInterval(604800),
                isViewed: true,
                reactions: [
                    PhotoReaction(
                        id: "photo_reaction_4",
                        photoId: "shared_4",
                        userId: friends[3].userId,
                        type: .wow,
                        timestamp: Date().addingTimeInterval(-14200)
                    )
                ],
                originalPostId: nil
            ),
            SharedPhoto(
                id: "shared_5",
                senderId: friends[4].userId,
                receiverId: currentUserId,
                imageURL: "https://picsum.photos/400/604?random=5",
                thumbnailURL: "https://picsum.photos/200/304?random=5",
                caption: "Cozy reading corner with my favorite latte 📚☕ Perfect Sunday vibes",
                timestamp: Date().addingTimeInterval(-18000),
                expiresAt: Date().addingTimeInterval(3600),
                isViewed: true,
                reactions: [
                    PhotoReaction(
                        id: "photo_reaction_5",
                        photoId: "shared_5",
                        userId: currentUserId,
                        type: .heart,
                        timestamp: Date().addingTimeInterval(-17800)
                    )
                ],
                originalPostId: nil
            )
        ]
    }
    
    // MARK: - Friend Requests Real Data
    static func generateRealFriendRequests() -> (received: [FriendRequest], sent: [FriendRequest]) {
        let currentUserId = AppViewModel.shared.currentUser.id
        let avatarManager = AvatarManager.shared
        
        let receivedRequests = [
            FriendRequest(
                id: "req_received_1",
                senderId: "user_mia",
                receiverId: currentUserId,
                senderInfo: Friend(
                    id: "friend_mia_req",
                    userId: "user_mia",
                    username: "MiaFashionista",
                    displayName: "Mia Rodriguez",
                    avatar: avatarManager.getRandomAvatarName(),
                    bio: "Fashion lover from NYC 🗽✨ Sharing style inspiration daily!",
                    location: "New York, NY",
                    status: .online,
                    lastSeen: Date(),
                    mutualFriends: 5,
                    friendshipDate: Date(),
                    isBlocked: false
                ),
                message: "Hi! I love your style posts! Would love to connect 💕",
                createdDate: Date().addingTimeInterval(-3600),
                status: .pending
            ),
            FriendRequest(
                id: "req_received_2",
                senderId: "user_zoe",
                receiverId: currentUserId,
                senderInfo: Friend(
                    id: "friend_zoe_req",
                    userId: "user_zoe",
                    username: "ZoeArtistic",
                    displayName: "Zoe Kim",
                    avatar: avatarManager.getRandomAvatarName(),
                    bio: "Artist & dreamer ✨🎨 Creating beauty in everyday moments",
                    location: "Los Angeles, CA",
                    status: .recentlyActive,
                    lastSeen: Date().addingTimeInterval(-1800),
                    mutualFriends: 2,
                    friendshipDate: Date(),
                    isBlocked: false
                ),
                message: nil,
                createdDate: Date().addingTimeInterval(-7200),
                status: .pending
            )
        ]
        
        let sentRequests = [
            FriendRequest(
                id: "req_sent_1",
                senderId: currentUserId,
                receiverId: "user_isabella",
                senderInfo: Friend(
                    id: "friend_isabella_req",
                    userId: "user_isabella",
                    username: "IsabellaTravels",
                    displayName: "Isabella Chen",
                    avatar: avatarManager.getRandomAvatarName(),
                    bio: "Travel blogger & photographer 📸🌍 Exploring the world one city at a time",
                    location: "San Francisco, CA",
                    status: .offline,
                    lastSeen: Date().addingTimeInterval(-14400),
                    mutualFriends: 3,
                    friendshipDate: Date(),
                    isBlocked: false
                ),
                message: "Love your travel photos! Would love to connect and share travel tips ✈️",
                createdDate: Date().addingTimeInterval(-10800),
                status: .pending
            )
        ]
        
        return (received: receivedRequests, sent: sentRequests)
    }
}
