//
//  AppDelegate.swift
//  MJSceneryShare
//
//  Created by liangj<PERSON> on 2025/5/19.
//

import UIKit

@main
class AppDelegate: UIResponder, UIApplicationDelegate {



    func application(_ application: UIApplication, didFinishLaunchingWithOptions launchOptions: [UIApplication.LaunchOptionsKey: Any]?) -> Bool {
        // Override point for customization after application launch.

        print("🚀 App launching...")

        // Setup API configuration
        setupAPIConfiguration()

        return true
    }

    private func setupAPIConfiguration() {
        print("⚙️ Setting up API configuration...")

        #if DEBUG
        // Setup development API key for testing
        APIConfiguration.setupDevelopmentAPIKey()
        APIConfiguration.testAPIKeyAccess()
        #endif

        // Test if Gemini API is available
        if APIConfiguration.isGeminiAPIAvailable() {
            print("✅ Gemini AI API is ready for use")
        } else {
            print("⚠️ Gemini AI API is not available")
        }
    }

    // MARK: UISceneSession Lifecycle

    func application(_ application: UIApplication, configurationForConnecting connectingSceneSession: UISceneSession, options: UIScene.ConnectionOptions) -> UISceneConfiguration {
        // Called when a new scene session is being created.
        // Use this method to select a configuration to create the new scene with.
        return UISceneConfiguration(name: "Default Configuration", sessionRole: connectingSceneSession.role)
    }

    func application(_ application: UIApplication, didDiscardSceneSessions sceneSessions: Set<UISceneSession>) {
        // Called when the user discards a scene session.
        // If any sessions were discarded while the application was not running, this will be called shortly after application:didFinishLaunchingWithOptions.
        // Use this method to release any resources that were specific to the discarded scenes, as they will not return.
    }


}

