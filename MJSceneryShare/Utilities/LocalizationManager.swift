import Foundation

class LocalizationManager {
    static let shared = LocalizationManager()

    private init() {}

    // Simplified to English-only for international app
    func localizedString(for key: String, comment: String = "") -> String {
        return NSLocalizedString(key, comment: comment)
    }
}

// MARK: - String Extension for Easy Localization
extension String {
    var localized: String {
        return LocalizationManager.shared.localizedString(for: self)
    }

    func localized(with arguments: CVarArg...) -> String {
        return String(format: self.localized, arguments: arguments)
    }
}

// MARK: - Localization Keys
struct LocalizationKeys {
    // Tab Bar
    static let tabDiscover = "tab.discover"
    static let tabPost = "tab.post"
    static let tabFriends = "tab.friends"
    static let tabProfile = "tab.profile"
    
    // Navigation
    static let navDiscover = "nav.discover"
    static let navProfile = "nav.profile"
    static let navAbout = "nav.about"
    static let navGetCoins = "nav.getCoins"
    
    // Authentication
    static let authAppName = "auth.appName"
    static let authSubtitle = "auth.subtitle"
    static let authGuestLogin = "auth.guestLogin"
    
    // Profile
    static let profileAccount = "profile.account"
    static let profileApp = "profile.app"
    static let profilePersonalInfo = "profile.personalInfo"
    static let profileSettings = "profile.settings"
    static let profileGetCoins = "profile.getCoins"
    static let profileAbout = "profile.about"
    static let profileFeedback = "profile.feedback"
    static let profilePrivacy = "profile.privacy"
    static let profileTerms = "profile.terms"
    static let profileSignOut = "profile.signOut"
    
    // Alerts
    static let alertSignOutTitle = "alert.signOut.title"
    static let alertSignOutMessage = "alert.signOut.message"
    static let alertCancel = "alert.cancel"
    static let alertOK = "alert.ok"
    static let alertAwesome = "alert.awesome"
    
    // Comments
    static let alertCommentsTitle = "alert.comments.title"
    static let alertCommentsMessage = "alert.comments.message"
    
    // Purchase
    static let alertPurchaseTitle = "alert.purchase.title"
    static let alertPurchaseMessage = "alert.purchase.message"
    static let alertPurchaseButton = "alert.purchase.button"
    static let alertPurchaseSuccessTitle = "alert.purchase.success.title"
    static let alertPurchaseSuccessMessage = "alert.purchase.success.message"
    
    // Save
    static let alertSaveFailed = "alert.save.failed"
    static let alertSaveSuccessTitle = "alert.save.success.title"
    static let alertSaveSuccessMessage = "alert.save.success.message"
    static let alertSavePermissionTitle = "alert.save.permission.title"
    static let alertSavePermissionMessage = "alert.save.permission.message"
    
    // About
    static let aboutAppName = "about.appName"
    static let aboutVersion = "about.version"
    static let aboutDescription = "about.description"
    static let aboutContact = "about.contact"
    
    // Purchase
    static let purchaseCurrentBalance = "purchase.currentBalance"
    static let purchaseHeader = "purchase.header"
    static let purchaseFooter = "purchase.footer"
    static let purchaseCoins = "purchase.coins"
    
    // User
    static let userGuest = "user.guest"
}
