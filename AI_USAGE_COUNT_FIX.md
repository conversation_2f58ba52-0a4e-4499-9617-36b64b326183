# AI Usage Count Display Fix

## 问题描述
用户反馈：内购商品购买了之后，Coins正确的增加了，但是 AI 的使用次数没有增加。

## 问题分析
系统的设计逻辑是：
1. 用户购买金币包（如100金币）
2. 每次AI处理消耗20金币（`AIProcessingPricing.fullProcessingCost = 20`）
3. 理论上100金币可以进行5次AI处理（100 ÷ 20 = 5）

但是用户界面显示的AI使用次数没有正确反映用户实际可以进行的AI处理次数。

## 解决方案
添加了新的计算属性来正确显示用户可用的AI处理次数：

### 1. 在 `User.swift` 中添加计算属性：
```swift
/// Total number of AI processing attempts available (free + paid)
var totalAvailableProcessing: Int {
    let paidProcessing = coins / AIProcessingPricing.fullProcessingCost
    return freeProcessingCount + paidProcessing
}

/// Number of paid AI processing attempts available
var paidProcessingCount: Int {
    return coins / AIProcessingPricing.fullProcessingCost
}
```

### 2. 更新购买页面显示 (`PurchaseViewController.swift`)：
- 显示总的可用AI分析次数
- 区分免费和付费次数
- 当没有可用次数时提示用户购买

### 3. 更新金币分析页面显示 (`CoinAnalyticsViewController.swift`)：
- 显示总的可用AI分析次数
- 简化显示逻辑

### 4. 更新验证消息 (`CoinTransactionManager.swift`)：
- 在处理请求验证时显示更详细的信息
- 让用户清楚了解他们有多少AI处理次数

## 重要修复1：游客用户限制
发现并修复了游客用户的问题：
- **问题**：游客用户不应该能使用付费AI处理，但之前的代码给游客用户也计算了付费次数
- **修复**：添加了`isGuestUser`检查，确保游客用户只能使用免费处理

## 重要修复2：Apple登录用户初始金币问题
发现并修复了Apple登录用户的初始金币问题：
- **问题**：Apple登录用户获得100个初始金币，导致显示"3 free + 5 paid"
- **原因**：`CoinSystemConfig.newUserStartingCoins = 100`
- **修复**：将初始金币改为0，确保所有新用户都只有3次免费AI分析

### 修复后的计算逻辑：
```swift
/// Total number of AI processing attempts available (free + paid)
var totalAvailableProcessing: Int {
    // Guest users can only use free processing
    if isGuestUser {
        return freeProcessingCount
    }
    let paidProcessing = coins / AIProcessingPricing.fullProcessingCost
    return freeProcessingCount + paidProcessing
}
```

## 测试结果
通过测试验证了以下场景：
- **游客用户（默认）**：3次免费，0次付费 = 3次总计 ✅
- **游客用户（使用1次后）**：2次免费，0次付费 = 2次总计 ✅
- **游客用户（用完免费）**：0次免费，0次付费 = 0次总计 ✅
- **Apple登录用户（新用户）**：3次免费，0次付费 = 3次总计 ✅
- **Apple登录用户（购买100金币后）**：3次免费 + 5次付费 = 8次总计 ✅
- **Apple登录用户（购买300金币后）**：3次免费 + 15次付费 = 18次总计 ✅

## 购买包对应的AI分析次数
根据每次AI处理消耗20金币计算：

| 购买包 | 总金币 | AI分析次数 | 显示特性 |
|--------|--------|------------|----------|
| Starter | 100 | 5次 | "5 AI image analyses" |
| Popular | 300 (250+50) | 15次 | "15 AI image analyses" |
| Value | 650 (500+150) | 32次 | "32+ AI image analyses" |
| Premium | 1400 (1000+400) | 70次 | "70+ AI image analyses" |
| Ultimate | 3000 (2000+1000) | 150次 | "150+ AI image analyses" |
| Mega | 8000 (5000+3000) | 400次 | "400+ AI image analyses" |

## 修复效果
现在用户购买金币包后：
1. ✅ 金币正确增加
2. ✅ AI使用次数显示正确反映实际可用次数
3. ✅ 用户界面清楚显示免费和付费次数
4. ✅ 购买包的features描述与实际可用次数匹配
5. ✅ **游客用户只显示免费次数，不会显示付费次数**
6. ✅ **登录用户可以看到完整的AI分析次数（免费+付费）**

### 界面显示示例：
- **游客用户**：`🆓 3 free AI analyses remaining (Guest mode)`
- **Apple登录用户（新用户）**：`🆓 3 free AI analyses remaining`
- **Apple登录用户（购买金币后）**：`🤖 8 AI analyses available (3 free + 5 paid)`
- **游客用户（用完免费）**：`🚫 No free analyses left - Sign in to purchase more`

用户现在可以清楚地看到他们购买的金币可以进行多少次AI分析，完全解决了"AI使用次数没有增加"的困惑，同时正确区分了游客和登录用户的权限。
